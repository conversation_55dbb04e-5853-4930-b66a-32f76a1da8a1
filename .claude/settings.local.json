{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "Bash(dotnet build)", "Bash(dotnet run:*)", "mcp__browsermcp__browser_navigate", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "mcp__browsermcp__browser_screenshot", "mcp__browsermcp__browser_get_console_logs", "mcp__browsermcp__browser_click", "Bash(grep:*)", "Bash(kill:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./test-page.sh:*)", "Bash(google-chrome:*)", "Bash(chromium:*)", "Bash(chromium-browser:*)", "Bash(venv/bin/python:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(timeout:*)", "mcp__ide__getDiagnostics", "Bash(dotnet build:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "<PERSON><PERSON>(sed:*)", "Bash(for dir in ./Areas/*/Controllers/)", "Bash(do echo \"Processing $dir\")", "Bash(done)", "Bash(for dir in ./Areas/*/Views/)", "<PERSON><PERSON>(mkdir:*)", "Bash(awk:*)", "Bash(/home/<USER>/source/repos/Cerebrum3-upgrade/migration-rules/find-missing-inherits.sh)", "Bash(/home/<USER>/source/repos/Cerebrum3-upgrade/migration-rules/find-missing-inherits-cerebrum30.sh)", "Bash(./fix-dynamic-expressions.sh:*)", "Bash(./fix-dynamic-expressions-simple.sh:*)", "Bash(./fix-truncate-calls.sh:*)", "Bash(/home/<USER>/source/repos/Cerebrum3-upgrade/migration-rules/identify-missing-inherits-safe.sh:*)", "Bash(/home/<USER>/source/repos/Cerebrum3-upgrade/migration-rules/fix-inherits-single-file.sh:*)", "Bash(./fix_object_casts.sh:*)", "Bash(/home/<USER>/source/repos/Cerebrum3-upgrade/migration-rules/fix-all-missing-inherits.sh:*)", "Bash(dotnet --version)", "Bash(/home/<USER>/source/repos/Cerebrum3-upgrade/migration-rules/fix_object_casts.sh:*)", "Bash(dotnet clean:*)", "Bash(cp:*)", "mcp__browsermcp__browser_snapshot", "<PERSON><PERSON>(journalctl:*)", "<PERSON><PERSON>(jobs)", "Bash(npm install)", "Bash(npm install:*)", "Bash(npx tsc:*)", "Bash(./node_modules/.bin/tsc:*)", "Bash(npm run build:ts:*)", "Bash(node:*)", "Bash(for file in wwwroot/js/compiled/*.ts)", "Bash(do mv \"$file\" \"$file%.ts.js\")", "WebSearch", "WebFetch(domain:weblog.west-wind.com)", "WebFetch(domain:stackoverflow.com)", "mcp__browsermcp__browser_press_key", "mcp__browsermcp__browser_wait", "Bash(./install.sh:*)", "Bash(./uninstall.sh:*)", "<PERSON><PERSON>(dos2unix:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(./seed/apply_seeds.sh:*)", "Bash(./load_snapshots.sh:*)", "<PERSON><PERSON>(docker cp:*)", "<PERSON><PERSON>(docker run:*)", "Bash(./import_bacpac_no_fulltext.sh:*)", "Bash(bash:*)", "Bash(./seed/load_schema_and_seeds.sh:*)", "Bash(./test_database.sh:*)", "Bash(./load_schema_and_seeds.sh:*)", "Bash(./install_clr_assembly.sh:*)", "Bash(./prepare_stored_procedures.sh:*)", "Bash(./run_migrations.sh:*)", "Bash(./reset_database.sh:*)", "Bash(./stop.sh)", "Bash(/dev/null)", "Bash(./build-seeded-container.sh:*)", "Bash(docker build:*)", "mcp__postgres__list_tables", "mcp__mssql__list_tables", "mcp__mssql__execute_sql", "Bash(dotnet add package:*)", "mcp__postgres__execute_sql", "<PERSON><PERSON>(comm:*)", "Read(//tmp/**)", "Read(//home/<USER>/source/repos/**)", "Bash(xargs ls:*)", "Bash(sort:*)", "Bash(xargs:*)", "Bash(./deploy-postgres-functions.sh:*)", "Bash(migration-rules/postgresql-procedures/deploy-postgres-functions.sh:*)", "Bash(set +f)", "Ba<PERSON>(./test-list.sh:*)", "Bash(./extract_procedures.sh:*)", "Bash(./run_all_tests.sh)", "Bash(python test_procedure.py:*)", "Bash(python test_sch_getccdoctors_manual.py)", "Bash(python test_getpatientinfo.py:*)", "Bash(./conversion-logs/add-postgres-tests.sh:*)", "Bash(echo \"Exit code: $?\")", "Bash(psql:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(docker inspect:*)", "Bash(./run_test.sh:*)", "<PERSON><PERSON>(time python3:*)", "<PERSON><PERSON>(time:*)", "Bash(./migration-rules/postgresql-procedures/deploy-postgres-functions.sh:*)", "Bash(../../../target-source/Cerebrum3-upgrade/migration-rules/postgresql-procedures/testing)", "Bash(mcp__postgres__list_tables:*)"], "deny": [], "defaultMode": "acceptEdits"}, "enabledMcpjsonServers": ["postgres", "mssql", "postgres-audit", "mssql-audit"]}