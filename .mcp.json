{"mcpServers": {"postgres": {"command": "/home/<USER>/.linuxbrew/bin/toolbox", "args": ["--prebuilt", "postgres", "--st<PERSON>"], "env": {"POSTGRES_HOST": "localhost", "POSTGRES_PORT": "5432", "POSTGRES_DATABASE": "c3_dev", "POSTGRES_USER": "postgres", "POSTGRES_PASSWORD": "postgres123"}}, "postgres-audit": {"command": "/home/<USER>/.linuxbrew/bin/toolbox", "args": ["--prebuilt", "postgres", "--st<PERSON>"], "env": {"POSTGRES_HOST": "localhost", "POSTGRES_PORT": "5432", "POSTGRES_DATABASE": "c3_audit", "POSTGRES_USER": "postgres", "POSTGRES_PASSWORD": "postgres123"}}, "mssql": {"command": "/home/<USER>/.linuxbrew/bin/toolbox", "args": ["--prebuilt", "mssql", "--st<PERSON>"], "env": {"MSSQL_HOST": "localhost", "MSSQL_PORT": "1433", "MSSQL_DATABASE": "C3_Dev", "MSSQL_USER": "sa", "MSSQL_PASSWORD": "n2wc2r2br5m%"}}, "mssql-audit": {"command": "/home/<USER>/.linuxbrew/bin/toolbox", "args": ["--prebuilt", "mssql", "--st<PERSON>"], "env": {"MSSQL_HOST": "localhost", "MSSQL_PORT": "1433", "MSSQL_DATABASE": "C3_Audit", "MSSQL_USER": "sa", "MSSQL_PASSWORD": "n2wc2r2br5m%"}}}}