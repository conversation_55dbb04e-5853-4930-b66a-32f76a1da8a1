# Dual Database Support Guide (SQL Server & PostgreSQL)

## Overview
This guide documents the **Repository Pattern** approach for supporting both SQL Server and PostgreSQL databases in the Cerebrum3 application. This pattern ensures database-specific logic is isolated in the Data layer while keeping the BLL synchronous and database-agnostic.

## Architecture Pattern

### Repository Pattern for Database Abstraction
All database-specific knowledge is contained within **repository classes** in the `Cerebrum.Data` project:

```
├── Cerebrum.BLL (Business Logic - Database Agnostic)
│   └── Uses Repository Interfaces
├── Cerebrum.Data (Data Access - Contains Database Logic)
│   ├── Repositories/
│   │   ├── IRepository.cs (Interface)
│   │   └── Repository.cs (Implementation with DB logic)
│   └── DataAccess/CerebrumContext.cs
└── Application Code (Controllers, etc.)
    └── Uses BLL Methods (No Database Awareness)
```

## Key Design Principles

1. **Database Abstraction**: Application code remains completely unaware of database type
2. **Synchronous Operations**: Repository methods are synchronous to avoid async cascade issues
3. **Repository Isolation**: All database-specific logic lives in `Cerebrum.Data` repositories
4. **Interface-Based**: BLL uses repository interfaces for testability and flexibility

## Implementation Example: DaysheetRepository

### 1. PostgreSQL Function Created
**Location**: `/migration-rules/postgresql-procedures/GetDaysheetAppointmentTests_v4.sql`

```sql
CREATE OR REPLACE FUNCTION dbo.GetDaysheetAppointmentTests_v4(
    p_office_id INTEGER DEFAULT NULL,
    p_selected_date TIMESTAMP DEFAULT NULL,
    -- ... other parameters
) RETURNS TABLE(
    Id INTEGER,
    PracticeId INTEGER,
    -- ... other columns
) AS $$
BEGIN
    RETURN QUERY SELECT
        apt.Id,
        apt.PracticeId,
        -- ... other fields
    FROM dbo.Appointments apt
    -- ... function logic
END;
$$ LANGUAGE plpgsql;
```

### 2. Repository Interface
**File**: `/target-source/Cerebrum.Data/Repositories/IDaysheetRepository.cs`

```csharp
public interface IDaysheetRepository
{
    List<SP_DaysheetAppointmentTest> GetDaysheetAppointmentTests(
        int? officeId = null,
        DateTime? selectedDate = null,
        // ... other parameters
    );

    List<SP_DaysheetAppointmentTest> GetDaysheetAppointmentItem(
        int appointmentId,
        int userId = 0,
        List<int> testGroupIds = null
    );
}
```

### 3. Repository Implementation
**File**: `/target-source/Cerebrum.Data/Repositories/DaysheetRepository.cs`

```csharp
public class DaysheetRepository : IDaysheetRepository
{
    private readonly CerebrumContext _context;

    public List<SP_DaysheetAppointmentTest> GetDaysheetAppointmentTests(...)
    {
        if (_context.IsPostgreSQL)
        {
            return GetDaysheetAppointmentTestsPostgreSql(...);
        }
        else
        {
            return GetDaysheetAppointmentTestsSqlServer(...);
        }
    }

    private List<SP_DaysheetAppointmentTest> GetDaysheetAppointmentTestsPostgreSql(...)
    {
        var parameters = new List<DbParameter>();
        parameters.Add(new NpgsqlParameter("p_office_id", NpgsqlDbType.Integer) 
            { Value = (object)officeId ?? DBNull.Value });
        // ... add other PostgreSQL parameters

        var functionCall = "dbo.GetDaysheetAppointmentTests_v4(" +
            string.Join(", ", parameters.Select(p => $"@{p.ParameterName}")) + ")";

        var result = _context.GetDataWithDbParameters<SP_DaysheetAppointmentTest>(
            functionCall, parameters);
        return result.ToList();
    }

    private List<SP_DaysheetAppointmentTest> GetDaysheetAppointmentTestsSqlServer(...)
    {
        var parameters = new List<DbParameter>();
        parameters.Add(new SqlParameter("officeId", (object)officeId ?? DBNull.Value));
        // ... add other SQL Server parameters

        var result = _context.GetDataWithDbParameters<SP_DaysheetAppointmentTest>(
            "[dbo].[GetDaysheetAppointmentTests_v4]", parameters);
        return result.ToList();
    }
}
```

### 4. BLL Layer (Database Agnostic)
**File**: `/target-source/Cerebrum.BLL/Daysheet/DaysheetBLL.cs`

```csharp
public class DaysheetBLL
{
    private readonly IDaysheetRepository _daysheetRepository;

    public VMDaysheetAppointmentsMain GetDaysheetAppointmentsMain(
        VMDaySheetRequest request, List<int> testGroupIds, int practiceId, int userId)
    {
        // Business logic here - no database knowledge
        List<SP_DaysheetAppointmentTest> dbData = _daysheetRepository.GetDaysheetAppointmentTests(
            officeId: officeId,
            selectedDate: daysheetDate,
            showExpected: request.Expected,
            // ... other parameters
        );
        
        // Process results and return view model
        return ProcessDaysheetData(dbData);
    }
}
```

### 5. Dependency Injection
**File**: `/target-source/Cerebrum30/Program.cs`

```csharp
// Register repository in DI container
builder.Services.AddScoped<IDaysheetRepository, DaysheetRepository>();
```

### 6. Enhanced CerebrumContext
**File**: `/target-source/Cerebrum.Data/DataAccess/CerebrumContext.cs`

```csharp
// Synchronous method for database-agnostic operations
public IEnumerable<T> GetDataWithDbParameters<T>(string storedProcedure, 
    List<DbParameter> parameters = null, int sqlCommandTimeOut = 0) where T : class
{
    using (System.Data.Common.DbCommand cmd = Database.GetDbConnection().CreateCommand())
    {
        // Adjust for PostgreSQL function call syntax
        if (IsPostgreSQL)
        {
            cmd.CommandText = storedProcedure.StartsWith("SELECT", StringComparison.OrdinalIgnoreCase) 
                ? storedProcedure : $"SELECT * FROM {storedProcedure}";
            cmd.CommandType = System.Data.CommandType.Text;
        }
        else
        {
            cmd.CommandText = storedProcedure;
            cmd.CommandType = System.Data.CommandType.StoredProcedure;
        }
        
        // Add parameters and execute
        // ... implementation
    }
}
```

### 7. Dependencies Added
- **Package**: `Npgsql.EntityFrameworkCore.PostgreSQL` (version 8.0.0)
- **Added to**: `Cerebrum.Data` project (not BLL)
- **Project Reference**: Added `Cerebrum.DTO` reference to `Cerebrum.Data` for SP entities

## Configuration

### Database Provider Selection
The database provider is configured in `appsettings.json`:
```json
{
  "DatabaseProvider": "SqlServer",  // or "PostgreSQL"
  "ConnectionStrings": {
    "SqlServer": { ... },
    "PostgreSQL": { ... }
  }
}
```

## Implementation Pattern

When converting other stored procedures/functions:

### 1. Create PostgreSQL Version
```sql
-- Create schema if needed
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create function in dbo schema
CREATE OR REPLACE FUNCTION dbo.function_name(parameters)
RETURNS TABLE(columns) AS $$
BEGIN
    -- Function logic
    -- Important: Use schema prefix for all table references
    -- Example: SELECT * FROM dbo.TableName
    RETURN QUERY SELECT ...;
END;
$$ LANGUAGE plpgsql;
```

### 2. Update BLL Layer
```csharp
if (_context.IsPostgreSQL)
{
    // PostgreSQL implementation
    var parameters = new List<DbParameter>();
    parameters.Add(new NpgsqlParameter("param_name", NpgsqlTypes.NpgsqlDbType.Type) { Value = value });
    var query = "SELECT * FROM dbo.function_name(@param_name)";
    result = _context.GetDataWithDbParameters<ResultType>(query, parameters);
}
else
{
    // SQL Server implementation
    var parameters = new List<SqlParameter>();
    parameters.Add(new SqlParameter("param_name", value));
    result = _context.GetData<ResultType>("[dbo].[sp_name]", parameters);
}
```

## Key Differences Between SQL Server and PostgreSQL

1. **Procedure vs Function**: PostgreSQL uses functions that return tables, SQL Server uses stored procedures
2. **Parameter Naming**: PostgreSQL typically uses `p_` prefix, SQL Server doesn't require prefixes
3. **Schema Handling**: 
   - PostgreSQL requires explicit schema references for tables
   - Must include schema name (e.g., `dbo.function_name`) in function calls
   - Create functions in the `dbo` schema to match SQL Server structure
   - All table references within functions should include the schema (e.g., `dbo.AspNetUsers`)
4. **Execution Syntax**: 
   - PostgreSQL: `SELECT * FROM dbo.function_name(params)`
   - SQL Server: Direct stored procedure execution `[dbo].[sp_name]`
5. **Parameter Types**:
   - PostgreSQL: Use `NpgsqlParameter` with `NpgsqlDbType`
   - SQL Server: Use `SqlParameter` with `SqlDbType`
6. **Boolean Handling**:
   - PostgreSQL: Use `true` and `false` for boolean columns
   - SQL Server: Can use `1` and `0` or `true` and `false`
   - When converting, replace `column = 1` with `column = true` and `column = 0` with `column = false`

## Testing

To test with PostgreSQL:
1. Change `DatabaseProvider` in `appsettings.json` to "PostgreSQL"
2. Ensure PostgreSQL connection strings are configured
3. Deploy PostgreSQL functions to the database
4. Run the application

## Benefits

- **Flexibility**: Support for multiple database providers
- **Backward Compatibility**: Existing SQL Server code continues to work
- **Clean Separation**: Database-specific logic is isolated
- **Future-Proof**: Easy to add support for additional providers