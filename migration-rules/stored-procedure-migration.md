# Stored Procedure Migration Guide (SQL Server → PostgreSQL)

## Overview

This guide provides comprehensive instructions for migrating SQL Server stored procedures to PostgreSQL functions while maintaining dual database support in the Cerebrum3 application. The migration follows the Repository Pattern to isolate database-specific logic in the Data layer.

## Prerequisites

- Review the [Dual Database Support Guide](dual-database-support-guide.md) for architectural context
- Understand the existing Repository Pattern implementation
- Familiarity with both SQL Server and PostgreSQL syntax differences

## Migration Process Overview

### Step 1: Analysis Phase
1. **Identify the stored procedure** in the codebase by searching for `[dbo].[ProcedureName]`
2. **Document parameters, return types, and complexity**
3. **Check for dependencies** on other procedures or functions
4. **Categorize complexity**: Simple SELECT, Complex Business Logic, or Table-Valued Parameters

### Step 2: PostgreSQL Function Creation
1. **Create function file** in `migration-rules/postgresql-procedures/[FunctionName].sql`
2. **Convert SQL Server syntax** to PostgreSQL syntax
3. **Test function** in PostgreSQL database
4. **Document any behavioral differences**

### Step 3: Repository Pattern Implementation
1. **Create/update repository interface** in `Cerebrum.Data/Repositories/I[Domain]Repository.cs`
2. **Implement repository class** with dual database support
3. **Update BLL layer** to use repository
4. **Register repository** in DI container

### Step 4: Testing & Validation
1. **Test both database implementations**
2. **Compare results** for identical input data
3. **Performance validation**
4. **Update migration log**

## SQL Server to PostgreSQL Conversion Rules

### Function Structure

**SQL Server Stored Procedure:**
```sql
CREATE PROCEDURE [dbo].[GetPatientInfo]
    @patientId INT,
    @includeHistory BIT = 0
AS
BEGIN
    SELECT Id, Name, DateOfBirth 
    FROM Patients 
    WHERE Id = @patientId
END
```

**PostgreSQL Function:**
```sql
CREATE OR REPLACE FUNCTION dbo.GetPatientInfo(
    p_patient_id INTEGER,
    p_include_history BOOLEAN DEFAULT false
)
RETURNS TABLE(
    id integer,
    name text,
    dateofbirth timestamp
) AS $$
BEGIN
    RETURN QUERY
    SELECT p.Id::integer, p.Name::text, p.DateOfBirth AT TIME ZONE 'UTC'
    FROM dbo.Patients p
    WHERE p.Id = p_patient_id;
END;
$$ LANGUAGE plpgsql;
```

### Key Conversion Patterns

#### 1. Parameter Naming
- **SQL Server**: `@parameterName`
- **PostgreSQL**: `p_parameter_name` (use lowercase with underscores)

#### 2. Data Types
| SQL Server | PostgreSQL | Notes |
|------------|------------|--------|
| `INT` | `INTEGER` | Direct mapping |
| `BIT` | `BOOLEAN` | Use `true`/`false` instead of `1`/`0` |
| `NVARCHAR(n)` | `VARCHAR(n)` or `TEXT` | PostgreSQL handles UTF-8 natively |
| `DATETIME` | `TIMESTAMP` | Always use `AT TIME ZONE 'UTC'` |
| `UNIQUEIDENTIFIER` | `UUID` | Requires uuid-ossp extension |
| `MONEY` | `DECIMAL(19,4)` | PostgreSQL has no MONEY type |

#### 3. Table-Valued Parameters (TVP)
**SQL Server TVP:**
```sql
-- User-defined table type
CREATE TYPE dbo.IntegerList AS TABLE (Value INT);

-- Procedure using TVP
CREATE PROCEDURE GetPatientsByIds @ids dbo.IntegerList READONLY
AS
SELECT * FROM Patients p
INNER JOIN @ids i ON p.Id = i.Value
```

**PostgreSQL Array:**
```sql
CREATE OR REPLACE FUNCTION dbo.GetPatientsByIds(
    p_ids INTEGER[] DEFAULT ARRAY[]::INTEGER[]
)
RETURNS TABLE(...) AS $$
BEGIN
    RETURN QUERY
    SELECT * FROM dbo.Patients p
    WHERE p.Id = ANY(p_ids);
END;
$$ LANGUAGE plpgsql;
```

#### 4. Boolean Logic
- **SQL Server**: `column = 1`, `column = 0`
- **PostgreSQL**: `column = true`, `column = false`

#### 5. String Functions
| SQL Server | PostgreSQL |
|------------|------------|
| `LEN(string)` | `LENGTH(string)` |
| `ISNULL(col, default)` | `COALESCE(col, default)` |
| `CHARINDEX(substr, str)` | `POSITION(substr IN str)` |
| `LEFT(str, n)` | `LEFT(str, n)` or `SUBSTRING(str, 1, n)` |

#### 6. Date Functions
| SQL Server | PostgreSQL |
|------------|------------|
| `GETDATE()` | `NOW()` or `CURRENT_TIMESTAMP` |
| `DATEADD(day, n, date)` | `date + INTERVAL 'n days'` |
| `DATEDIFF(day, start, end)` | `(end::date - start::date)` |
| `YEAR(date)` | `EXTRACT(year FROM date)` |

## Repository Pattern Implementation

### 1. Interface Definition

```csharp
// File: Cerebrum.Data/Repositories/I[Domain]Repository.cs
public interface IPatientRepository
{
    List<SP_PatientInfo> GetPatientInfo(int patientId, bool includeHistory = false);
    List<SP_PatientSearch> SearchPatients(string searchTerm, int practiceId);
    // ... other methods
}
```

### 2. Repository Implementation

```csharp
// File: Cerebrum.Data/Repositories/[Domain]Repository.cs
public class PatientRepository : IPatientRepository
{
    private readonly CerebrumContext _context;

    public PatientRepository(CerebrumContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    public List<SP_PatientInfo> GetPatientInfo(int patientId, bool includeHistory = false)
    {
        if (_context.IsPostgreSQL)
        {
            return GetPatientInfoPostgreSql(patientId, includeHistory);
        }
        else
        {
            return GetPatientInfoSqlServer(patientId, includeHistory);
        }
    }

    private List<SP_PatientInfo> GetPatientInfoPostgreSql(int patientId, bool includeHistory)
    {
        var parameters = new List<DbParameter>();
        parameters.Add(new NpgsqlParameter("p_patient_id", NpgsqlDbType.Integer) { Value = patientId });
        parameters.Add(new NpgsqlParameter("p_include_history", NpgsqlDbType.Boolean) { Value = includeHistory });

        var functionCall = "SELECT * FROM dbo.GetPatientInfo(@p_patient_id, @p_include_history)";
        var result = _context.GetDataWithDbParameters<SP_PatientInfo>(functionCall, parameters);
        return result.ToList();
    }

    private List<SP_PatientInfo> GetPatientInfoSqlServer(int patientId, bool includeHistory)
    {
        var parameters = new List<DbParameter>();
        parameters.Add(new SqlParameter("patientId", patientId));
        parameters.Add(new SqlParameter("includeHistory", includeHistory));

        var result = _context.GetDataWithDbParameters<SP_PatientInfo>("[dbo].[GetPatientInfo]", parameters);
        return result.ToList();
    }
}
```

### 3. BLL Layer Update

```csharp
// File: Cerebrum.BLL/Patient/PatientBLL.cs
public class PatientBLL
{
    private readonly IPatientRepository _patientRepository;

    public PatientBLL(IPatientRepository patientRepository)
    {
        _patientRepository = patientRepository ?? throw new ArgumentNullException(nameof(patientRepository));
    }

    public VMPatientInfo GetPatientInfo(int patientId)
    {
        // Business logic - database agnostic
        var dbData = _patientRepository.GetPatientInfo(patientId, includeHistory: true);
        
        // Process and transform data
        return MapToViewModel(dbData);
    }
}
```

### 4. Dependency Injection Registration

```csharp
// File: Cerebrum30/Program.cs
// Register repository in DI container
builder.Services.AddScoped<IPatientRepository, PatientRepository>();
```

## Parameter Mapping Guidelines

### NpgsqlParameter Types
```csharp
// Common parameter types for PostgreSQL
parameters.Add(new NpgsqlParameter("p_int_param", NpgsqlDbType.Integer) { Value = intValue });
parameters.Add(new NpgsqlParameter("p_string_param", NpgsqlDbType.Varchar) { Value = stringValue ?? (object)DBNull.Value });
parameters.Add(new NpgsqlParameter("p_bool_param", NpgsqlDbType.Boolean) { Value = boolValue });
parameters.Add(new NpgsqlParameter("p_date_param", NpgsqlDbType.Timestamp) { Value = dateValue ?? (object)DBNull.Value });
parameters.Add(new NpgsqlParameter("p_array_param", NpgsqlDbType.Array | NpgsqlDbType.Integer) { Value = intArray ?? new int[0] });
```

### SqlParameter Types (for comparison)
```csharp
// Common parameter types for SQL Server
parameters.Add(new SqlParameter("intParam", intValue));
parameters.Add(new SqlParameter("stringParam", stringValue ?? (object)DBNull.Value));
parameters.Add(new SqlParameter("boolParam", boolValue));
parameters.Add(new SqlParameter("dateParam", dateValue ?? (object)DBNull.Value));
// TVP handling for SQL Server arrays
var tvp = CreateIntegerListTableParameter("arrayParam", intList);
parameters.Add(tvp);
```

## Complex Migration Scenarios

### 1. Procedures with Multiple Result Sets
SQL Server procedures can return multiple result sets, but PostgreSQL functions return a single result set. Solutions:
- **Split into multiple functions**: Create separate functions for each result set
- **Union approach**: Combine results with type indicators
- **JSON approach**: Return complex nested JSON structure

### 2. Temporary Tables
**SQL Server:**
```sql
CREATE TABLE #TempTable (Id INT, Name NVARCHAR(100))
INSERT INTO #TempTable ...
SELECT * FROM #TempTable
```

**PostgreSQL (use CTEs):**
```sql
WITH temp_data AS (
    SELECT Id, Name FROM SomeTable WHERE condition
)
SELECT * FROM temp_data;
```

### 3. Cursors
Avoid cursors in PostgreSQL. Use:
- **FOR loops** for row-by-row processing
- **Set-based operations** where possible
- **LATERAL joins** for complex correlations

### 4. Error Handling
**SQL Server:**
```sql
BEGIN TRY
    -- Code
END TRY
BEGIN CATCH
    -- Error handling
END CATCH
```

**PostgreSQL:**
```sql
BEGIN
    -- Code
EXCEPTION 
    WHEN others THEN
        -- Error handling
        RAISE EXCEPTION 'Error: %', SQLERRM;
END;
```

## Testing Procedure

### 1. Unit Testing Template
```csharp
[Test]
public void GetPatientInfo_WithValidId_ReturnsPatientData()
{
    // Arrange
    var patientId = 1;
    var repository = new PatientRepository(_context);

    // Act - Test both implementations
    var sqlServerResult = GetResultWithSqlServer(repository, patientId);
    var postgreSqlResult = GetResultWithPostgreSql(repository, patientId);

    // Assert
    Assert.AreEqual(sqlServerResult.Count, postgreSqlResult.Count);
    Assert.AreEqual(sqlServerResult.First().Name, postgreSqlResult.First().Name);
}
```

### 2. Comparison Testing Script
```sql
-- Run on both databases with identical seed data
-- SQL Server
EXEC [dbo].[GetPatientInfo] @patientId = 1, @includeHistory = 1;

-- PostgreSQL  
SELECT * FROM dbo.GetPatientInfo(1, true);
```

### 3. Performance Testing
- Compare execution times for identical operations
- Monitor query plans and index usage
- Test with production-like data volumes

## Common Pitfalls & Solutions

### 1. Schema References
- **Always use schema prefix**: `dbo.TableName` not just `TableName`
- **Create dbo schema** in PostgreSQL: `CREATE SCHEMA IF NOT EXISTS dbo;`

### 2. Case Sensitivity
- PostgreSQL is case-sensitive for identifiers
- Use consistent casing in function and column names
- Quote identifiers if mixed case is required: `"ColumnName"`

### 3. Null Handling
- Test null parameter values thoroughly
- Use `COALESCE()` instead of `ISNULL()`
- Handle DBNull.Value in parameter mapping

### 4. Transaction Scope
- PostgreSQL functions run in transaction context
- Be aware of isolation level differences
- Test rollback scenarios

## Migration Priority Categories

### High Priority (Critical Path)
- Patient search and retrieval procedures
- Appointment management procedures  
- Authentication and authorization procedures
- Real-time data procedures (kiosk, scheduling)

### Medium Priority (Business Logic)
- Reporting procedures
- Data validation procedures
- Complex calculation procedures
- Integration procedures (HL7, external systems)

### Low Priority (Administrative)
- Data maintenance procedures
- Historical reporting procedures
- Rarely used utility procedures

## File Organization

```
migration-rules/
├── postgresql-procedures/          # PostgreSQL function definitions
│   ├── GetPatientInfo.sql
│   ├── SearchPatients.sql
│   └── deploy-functions.sql       # Deployment script
├── stored-procedure-migration.md   # This guide
└── dual-database-support-guide.md # Architecture guide

conversion-logs/
└── stored-procedure-migration-log.md  # Progress tracking

target-source/
├── Cerebrum.Data/Repositories/     # Repository implementations
└── Cerebrum.BLL/                  # Updated BLL classes
```

## Best Practices

1. **Test Early and Often**: Test each function immediately after creation
2. **Maintain Compatibility**: Ensure SQL Server continues to work during migration
3. **Document Differences**: Note any behavioral differences between implementations
4. **Performance Baseline**: Establish performance benchmarks before migration
5. **Incremental Migration**: Migrate in small, testable batches
6. **Code Reviews**: Have database and application experts review conversions
7. **Rollback Plan**: Maintain ability to revert changes if needed

## Troubleshooting

### Common Errors and Solutions

**Error: Function does not exist**
- Check schema name (`dbo.FunctionName`)
- Verify function is deployed to database
- Check parameter count and types

**Error: Parameter type mismatch**
- Verify NpgsqlDbType mappings
- Check for null value handling
- Confirm array parameter syntax

**Error: Different result sets**
- Compare data types between databases
- Check for timezone conversions
- Verify boolean value representations

## Support and Resources

- **Database Documentation**: PostgreSQL and SQL Server documentation
- **MCP Connections**: Test procedures using MCP postgres and mssql connections
- **Migration Log**: Track progress in `conversion-logs/stored-procedure-migration-log.md`
- **Team Knowledge**: Consult with database and application experts

---

*This guide will be updated as migration patterns are refined and new challenges are discovered.*