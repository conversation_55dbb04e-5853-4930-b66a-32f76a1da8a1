{"procedure_name": "getreportqueueSearch", "test_date": "2025-09-12T12:50:22-07:00", "test_cases": [{"test_name": "report_queue", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetReportQueueSearch] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getreportqueuesearch(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "empty_queue", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetReportQueueSearch] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getreportqueuesearch(p_practice_id := 9999)", "result": "PREPARED"}]}