{"procedure_name": "getvpreportphrasesbyrootcategoryid", "test_date": "2025-09-12T12:50:25-07:00", "test_cases": [{"test_name": "phrases_by_category", "parameters": "1:2", "sql_server_query": "EXEC [dbo].[GetVPReportPhrasesByRootCategoryId] @PracticeId = 1, @CategoryId = 2;", "postgres_query": "SELECT * FROM dbo.getvpreportphrasesbyrootcategoryid(p_practice_id := 1, p_category_id := 2)", "result": "PREPARED"}, {"test_name": "no_phrases", "parameters": "9999:2", "sql_server_query": "EXEC [dbo].[GetVPReportPhrasesByRootCategoryId] @PracticeId = 9999, @CategoryId = 2;", "postgres_query": "SELECT * FROM dbo.getvpreportphrasesbyrootcategoryid(p_practice_id := 9999, p_category_id := 2)", "result": "PREPARED"}]}