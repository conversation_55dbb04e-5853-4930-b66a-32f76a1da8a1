{"procedure_name": "getinventoryitemhistory", "test_date": "2025-09-12T12:50:24-07:00", "test_cases": [{"test_name": "item_history", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetInventoryItemHistory] @ItemId = 1;", "postgres_query": "SELECT * FROM dbo.getinventoryitemhistory(p_item_id := 1)", "result": "PREPARED"}, {"test_name": "no_history", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetInventoryItemHistory] @ItemId = 9999;", "postgres_query": "SELECT * FROM dbo.getinventoryitemhistory(p_item_id := 9999)", "result": "PREPARED"}]}