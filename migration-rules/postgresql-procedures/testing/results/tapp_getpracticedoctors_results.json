{"procedure_name": "tapp_getpracticedoctors", "test_date": "2025-09-12T12:50:22-07:00", "test_cases": [{"test_name": "practice_doctors", "parameters": "1", "sql_server_query": "EXEC [dbo].[TAPP_GetPracticeDoctors] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.tapp_getpracticedoctors(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_doctors", "parameters": "9999", "sql_server_query": "EXEC [dbo].[TAPP_GetPracticeDoctors] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.tapp_getpracticedoctors(p_practice_id := 9999)", "result": "PREPARED"}]}