{"procedure_name": "SP_Find_Patients_V1", "test_run_date": "2024-09-12T00:00:00Z", "test_cases": [{"test_name": "ohip_search_exact", "parameters": "practice_id=1, ohip=**********", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @OHIP = '**********', @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := '**********', p_top_result := 20, p_active := NULL);", "description": "OHIP number search - exact match", "result": "PENDING"}, {"test_name": "ohip_search_partial", "parameters": "practice_id=1, ohip=1234", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @OHIP = '1234', @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := '1234', p_top_result := 20, p_active := NULL);", "description": "OHIP number search - partial match", "result": "PENDING"}, {"test_name": "name_search_both", "parameters": "practice_id=1, last_name=<PERSON>, first_name=<PERSON>", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @LastName = 'Smith', @FirstName = 'John', @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := '<PERSON>', p_first_name := '<PERSON>', p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL);", "description": "Search by both first and last name", "result": "PENDING"}, {"test_name": "name_search_lastname_only", "parameters": "practice_id=1, last_name=<PERSON>", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @LastName = 'Smith', @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := '<PERSON>', p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL);", "description": "Search by last name only", "result": "PENDING"}, {"test_name": "name_search_firstname_only", "parameters": "practice_id=1, first_name=<PERSON>", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @FirstName = 'John', @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := '<PERSON>', p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL);", "description": "Search by first name only", "result": "PENDING"}, {"test_name": "phone_search", "parameters": "practice_id=1, phone_number=************", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @PhoneNumber = '************', @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := '************', p_ohip := NULL, p_top_result := 20, p_active := NULL);", "description": "Search by phone number", "result": "PENDING"}, {"test_name": "phone_search_digits_only", "parameters": "practice_id=1, phone_number=**********", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @PhoneNumber = '**********', @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := '**********', p_ohip := NULL, p_top_result := 20, p_active := NULL);", "description": "Search by phone number - digits only", "result": "PENDING"}, {"test_name": "dob_search_exact", "parameters": "practice_id=1, date_of_birth=1980-05-15", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @DateOfBirth = '1980-05-15', @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := '1980-05-15'::date, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL);", "description": "Search by exact date of birth", "result": "PENDING"}, {"test_name": "year_of_birth", "parameters": "practice_id=1, year_of_birth=1980", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @YearOfBirth = 1980, @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := 1980, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL);", "description": "Search by year of birth", "result": "PENDING"}, {"test_name": "patient_id_search", "parameters": "practice_id=1, patient_id=1002", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @PatientId = 1002, @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := 1002, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL);", "description": "Search by patient ID", "result": "PENDING"}, {"test_name": "active_patients_only", "parameters": "practice_id=1, last_name=<PERSON>, active=1", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @LastName = 'Smith', @TOPResult = 20, @Active = 1;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := '<PERSON>', p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := 1);", "description": "Search active patients only", "result": "PENDING"}, {"test_name": "inactive_patients", "parameters": "practice_id=1, last_name=<PERSON>, active=0", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @LastName = 'Smith', @TOPResult = 20, @Active = 0;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := '<PERSON>', p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := 0);", "description": "Search inactive patients", "result": "PENDING"}, {"test_name": "top_results_limit", "parameters": "practice_id=1, last_name=<PERSON>, top_result=5", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @LastName = 'Smith', @TOPResult = 5;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := '<PERSON>', p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 5, p_active := NULL);", "description": "Limit results to top 5", "result": "PENDING"}, {"test_name": "combined_search", "parameters": "practice_id=1, last_name=<PERSON>, first_name=<PERSON>, year_of_birth=1980, active=1", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @LastName = 'Smith', @FirstName = '<PERSON>', @YearOfBirth = 1980, @TOPResult = 20, @Active = 1;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := '<PERSON>', p_first_name := '<PERSON>', p_date_of_birth := NULL, p_year_of_birth := 1980, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := 1);", "description": "Combined search criteria", "result": "PENDING"}, {"test_name": "no_criteria", "parameters": "practice_id=1", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL);", "description": "No search criteria - should return empty", "result": "PENDING"}, {"test_name": "non_existent_patient", "parameters": "practice_id=1, last_name=NonExistentPatient999", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @LastName = 'NonExistentPatient999', @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := 'NonExistentPatient999', p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL);", "description": "Search for non-existent patient", "result": "PENDING"}]}