{"procedure_name": "getuserroles", "test_date": "2025-09-12T12:50:16-07:00", "test_cases": [{"test_name": "user_with_roles", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetUserRoles] @UserId = 1;", "postgres_query": "SELECT * FROM dbo.getuserroles(p_user_id := 1)", "result": "UNTESTED"}, {"test_name": "user_no_roles", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetUserRoles] @UserId = 9999;", "postgres_query": "SELECT * FROM dbo.getuserroles(p_user_id := 9999)", "result": "UNTESTED"}]}