{"procedure_name": "getpatientlocations", "test_date": "2025-09-12T12:50:19-07:00", "test_cases": [{"test_name": "patient_locations", "parameters": "2:1", "sql_server_query": "EXEC [dbo].[GetPatientLocations] @PatientId = 2, @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getpatientlocations(p_patient_id := 2, p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_locations", "parameters": "9999:1", "sql_server_query": "EXEC [dbo].[GetPatientLocations] @PatientId = 9999, @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getpatientlocations(p_patient_id := 9999, p_practice_id := 1)", "result": "PREPARED"}]}