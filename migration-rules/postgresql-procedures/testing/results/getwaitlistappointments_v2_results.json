{"procedure_name": "getwaitlistappointments_v2", "test_date": "2025-09-12T12:50:18-07:00", "test_cases": [{"test_name": "practice_waitlist", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetWaitlistAppointments_v2] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getwaitlistappointments_v2(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "empty_waitlist", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetWaitlistAppointments_v2] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getwaitlistappointments_v2(p_practice_id := 9999)", "result": "PREPARED"}]}