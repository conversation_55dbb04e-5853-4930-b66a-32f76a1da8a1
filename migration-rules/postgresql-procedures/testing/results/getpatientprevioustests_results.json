{"procedure_name": "getpatientprevioustests", "test_date": "2025-09-12T12:50:20-07:00", "test_cases": [{"test_name": "previous_tests", "parameters": "2", "sql_server_query": "EXEC [dbo].[GetPatientPreviousTests] @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.getpatientprevioustests(p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "no_previous", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetPatientPreviousTests] @PatientId = 9999;", "postgres_query": "SELECT * FROM dbo.getpatientprevioustests(p_patient_id := 9999)", "result": "PREPARED"}]}