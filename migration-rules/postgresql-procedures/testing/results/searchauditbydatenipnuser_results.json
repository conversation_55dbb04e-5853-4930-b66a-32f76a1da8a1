{"procedure_name": "searchauditbydatenipnuser", "test_date": "2025-09-12T12:50:25-07:00", "test_cases": [{"test_name": "audit_user", "parameters": "2024-01-01:2024-01-31:1", "sql_server_query": "EXEC [dbo].[SearchAuditByDateNIPNuser] @StartDate = '2024-01-01', @EndDate = '2024-01-31', @UserId = 1;", "postgres_query": "SELECT * FROM dbo.searchauditbydatenipnuser(p_start_date := '2024-01-01', p_end_date := '2024-01-31', p_user_id := 1)", "result": "PREPARED"}, {"test_name": "no_audit_user", "parameters": "2025-01-01:2025-01-31:9999", "sql_server_query": "EXEC [dbo].[SearchAuditByDateNIPNuser] @StartDate = '2025-01-01', @EndDate = '2025-01-31', @UserId = 9999;", "postgres_query": "SELECT * FROM dbo.searchauditbydatenipnuser(p_start_date := '2025-01-01', p_end_date := '2025-01-31', p_user_id := 9999)", "result": "PREPARED"}]}