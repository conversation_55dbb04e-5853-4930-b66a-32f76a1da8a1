{"procedure_name": "getaudit", "test_date": "2025-09-12T12:50:26-07:00", "test_cases": [{"test_name": "get_audit", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetAudit] @AuditId = 1;", "postgres_query": "SELECT * FROM dbo.getaudit(p_audit_id := 1)", "result": "PREPARED"}, {"test_name": "no_get_audit", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetAudit] @AuditId = 9999;", "postgres_query": "SELECT * FROM dbo.getaudit(p_audit_id := 9999)", "result": "PREPARED"}]}