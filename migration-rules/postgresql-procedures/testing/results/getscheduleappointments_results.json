{"procedure_name": "getscheduleappointments", "test_date": "2025-09-12T12:50:19-07:00", "test_cases": [{"test_name": "schedule_date", "parameters": "1:2024-01-01", "sql_server_query": "EXEC [dbo].[GetScheduleAppointments] @PracticeId = 1, @ScheduleDate = '2024-01-01';", "postgres_query": "SELECT * FROM dbo.getscheduleappointments(p_practice_id := 1, p_schedule_date := '2024-01-01')", "result": "PREPARED"}, {"test_name": "no_appointments", "parameters": "1:2025-01-01", "sql_server_query": "EXEC [dbo].[GetScheduleAppointments] @PracticeId = 1, @ScheduleDate = '2025-01-01';", "postgres_query": "SELECT * FROM dbo.getscheduleappointments(p_practice_id := 1, p_schedule_date := '2025-01-01')", "result": "PREPARED"}]}