{"procedure_name": "getuseroffices", "test_date": "2025-09-12T12:50:17-07:00", "test_cases": [{"test_name": "user_with_offices", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetUserOffices] @UserId = 1;", "postgres_query": "SELECT * FROM dbo.getuseroffices(p_user_id := 1)", "result": "UNTESTED"}, {"test_name": "user_no_offices", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetUserOffices] @UserId = 9999;", "postgres_query": "SELECT * FROM dbo.getuseroffices(p_user_id := 9999)", "result": "UNTESTED"}]}