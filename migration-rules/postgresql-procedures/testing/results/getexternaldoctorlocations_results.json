{"procedure_name": "getexternaldoctorlocations", "test_date": "2025-09-12T12:50:24-07:00", "test_cases": [{"test_name": "external_locations", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetExternalDoctorLocations] @ExternalDoctorId = 1;", "postgres_query": "SELECT * FROM dbo.getexternaldoctorlocations(p_external_doctor_id := 1)", "result": "PREPARED"}, {"test_name": "no_external", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetExternalDoctorLocations] @ExternalDoctorId = 9999;", "postgres_query": "SELECT * FROM dbo.getexternaldoctorlocations(p_external_doctor_id := 9999)", "result": "PREPARED"}]}