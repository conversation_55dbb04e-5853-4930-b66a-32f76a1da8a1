{"procedure_name": "getpracticescheduledusers", "test_date": "2025-09-12T12:50:17-07:00", "test_cases": [{"test_name": "practice_with_users", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetPracticeScheduledUsers] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getpracticescheduledusers(p_practice_id := 1)", "result": "UNTESTED"}, {"test_name": "practice_no_users", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetPracticeScheduledUsers] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getpracticescheduledusers(p_practice_id := 9999)", "result": "UNTESTED"}]}