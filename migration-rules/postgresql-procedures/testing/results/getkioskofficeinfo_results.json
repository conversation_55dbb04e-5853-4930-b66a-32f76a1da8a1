{"procedure_name": "getkioskofficeinfo", "test_date": "2025-09-12T12:50:22-07:00", "test_cases": [{"test_name": "office_kiosk", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetKioskOfficeInfo] @OfficeId = 1;", "postgres_query": "SELECT * FROM dbo.getkioskofficeinfo(p_office_id := 1)", "result": "PREPARED"}, {"test_name": "invalid_office", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetKioskOfficeInfo] @OfficeId = 9999;", "postgres_query": "SELECT * FROM dbo.getkioskofficeinfo(p_office_id := 9999)", "result": "PREPARED"}]}