{"procedure_name": "sp_getrecalllist", "test_date": "2025-09-12T12:50:27-07:00", "test_cases": [{"test_name": "recall_list", "parameters": "1", "sql_server_query": "EXEC [dbo].[sp_GetRecallList] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.sp_getrecalllist(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_recalls", "parameters": "9999", "sql_server_query": "EXEC [dbo].[sp_GetRecallList] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.sp_getrecalllist(p_practice_id := 9999)", "result": "PREPARED"}]}