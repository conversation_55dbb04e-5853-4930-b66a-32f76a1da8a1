{"procedure_name": "get_vp_reportphrases_custom", "test_date": "2025-09-12T12:50:25-07:00", "test_cases": [{"test_name": "custom_phrases", "parameters": "1:2:3", "sql_server_query": "EXEC [dbo].[Get_VP_ReportPhrases_Custom] @PracticeId = 1, @PatientId = 2, @CategoryId = 3;", "postgres_query": "SELECT * FROM dbo.get_vp_reportphrases_custom(p_practice_id := 1, p_patient_id := 2, p_category_id := 3)", "result": "PREPARED"}, {"test_name": "no_custom", "parameters": "9999:2:3", "sql_server_query": "EXEC [dbo].[Get_VP_ReportPhrases_Custom] @PracticeId = 9999, @PatientId = 2, @CategoryId = 3;", "postgres_query": "SELECT * FROM dbo.get_vp_reportphrases_custom(p_practice_id := 9999, p_patient_id := 2, p_category_id := 3)", "result": "PREPARED"}]}