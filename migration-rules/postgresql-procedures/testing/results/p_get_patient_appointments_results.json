{"procedure_name": "p_get_patient_appointments", "test_date": "2025-09-12T12:50:18-07:00", "test_cases": [{"test_name": "patient_appts", "parameters": "2", "sql_server_query": "EXEC [dbo].[P_Get_Patient_Appointments] @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.p_get_patient_appointments(p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "no_appointments", "parameters": "9999", "sql_server_query": "EXEC [dbo].[P_Get_Patient_Appointments] @PatientId = 9999;", "postgres_query": "SELECT * FROM dbo.p_get_patient_appointments(p_patient_id := 9999)", "result": "PREPARED"}]}