{"procedure_name": "get_vp_doctoroptions", "test_date": "2025-09-12T12:50:20-07:00", "test_cases": [{"test_name": "doctor_options", "parameters": "1:1", "sql_server_query": "EXEC [dbo].[Get_VP_DoctorOptions] @PracticeId = 1, @DoctorId = 1;", "postgres_query": "SELECT * FROM dbo.get_vp_doctoroptions(p_practice_id := 1, p_doctor_id := 1)", "result": "PREPARED"}, {"test_name": "no_options", "parameters": "1:9999", "sql_server_query": "EXEC [dbo].[Get_VP_DoctorOptions] @PracticeId = 1, @DoctorId = 9999;", "postgres_query": "SELECT * FROM dbo.get_vp_doctoroptions(p_practice_id := 1, p_doctor_id := 9999)", "result": "PREPARED"}]}