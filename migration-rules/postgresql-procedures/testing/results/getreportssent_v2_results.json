{"procedure_name": "getreportssent_v2", "test_date": "2025-09-12T12:50:22-07:00", "test_cases": [{"test_name": "reports_sent", "parameters": "1:2024-01-01", "sql_server_query": "EXEC [dbo].[GetReportsSent_V2] @PracticeId = 1, @StartDate = '2024-01-01';", "postgres_query": "SELECT * FROM dbo.getreportssent_v2(p_practice_id := 1, p_start_date := '2024-01-01')", "result": "PREPARED"}, {"test_name": "no_reports", "parameters": "1:2025-01-01", "sql_server_query": "EXEC [dbo].[GetReportsSent_V2] @PracticeId = 1, @StartDate = '2025-01-01';", "postgres_query": "SELECT * FROM dbo.getreportssent_v2(p_practice_id := 1, p_start_date := '2025-01-01')", "result": "PREPARED"}]}