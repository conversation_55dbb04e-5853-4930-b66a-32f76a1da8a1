{"procedure_name": "geteconsultmetadata", "test_date": "2025-09-12T12:50:26-07:00", "test_cases": [{"test_name": "econsult_metadata", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetEconsultMetadata] @EconsultId = 1;", "postgres_query": "SELECT * FROM dbo.geteconsultmetadata(p_econsult_id := 1)", "result": "PREPARED"}, {"test_name": "no_metadata", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetEconsultMetadata] @EconsultId = 9999;", "postgres_query": "SELECT * FROM dbo.geteconsultmetadata(p_econsult_id := 9999)", "result": "PREPARED"}]}