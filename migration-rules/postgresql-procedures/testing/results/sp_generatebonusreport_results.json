{"procedure_name": "sp_generatebonusreport", "test_date": "2025-09-12T12:50:26-07:00", "test_cases": [{"test_name": "bonus_report", "parameters": "1:2024-01-01:2024-12-31", "sql_server_query": "EXEC [dbo].[sp_GenerateBonusReport] @PracticeId = 1, @StartDate = '2024-01-01', @EndDate = '2024-12-31';", "postgres_query": "SELECT * FROM dbo.sp_generatebonusreport(p_practice_id := 1, p_start_date := '2024-01-01', p_end_date := '2024-12-31')", "result": "PREPARED"}, {"test_name": "no_bonus", "parameters": "9999:2025-01-01:2025-12-31", "sql_server_query": "EXEC [dbo].[sp_GenerateBonusReport] @PracticeId = 9999, @StartDate = '2025-01-01', @EndDate = '2025-12-31';", "postgres_query": "SELECT * FROM dbo.sp_generatebonusreport(p_practice_id := 9999, p_start_date := '2025-01-01', p_end_date := '2025-12-31')", "result": "PREPARED"}]}