{"procedure_name": "getauditlogdata", "test_date": "2025-09-12T12:50:26-07:00", "test_cases": [{"test_name": "audit_logdata", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetAuditLogData] @LogId = 1;", "postgres_query": "SELECT * FROM dbo.getauditlogdata(p_log_id := 1)", "result": "PREPARED"}, {"test_name": "no_logdata", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetAuditLogData] @LogId = 9999;", "postgres_query": "SELECT * FROM dbo.getauditlogdata(p_log_id := 9999)", "result": "PREPARED"}]}