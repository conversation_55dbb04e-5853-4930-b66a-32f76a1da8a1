{"procedure_name": "getappointmenttestinfo", "test_date": "2025-09-12T12:50:18-07:00", "test_cases": [{"test_name": "appointment_with_tests", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetAppointmentTestInfo] @AppointmentId = 1;", "postgres_query": "SELECT * FROM dbo.getappointmenttestinfo(p_appointment_id := 1)", "result": "PREPARED"}, {"test_name": "appointment_no_tests", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetAppointmentTestInfo] @AppointmentId = 9999;", "postgres_query": "SELECT * FROM dbo.getappointmenttestinfo(p_appointment_id := 9999)", "result": "PREPARED"}]}