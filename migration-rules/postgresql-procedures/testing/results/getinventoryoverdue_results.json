{"procedure_name": "getinventoryoverdue", "test_date": "2025-09-12T12:50:24-07:00", "test_cases": [{"test_name": "overdue_items", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetInventoryOverDue] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getinventoryoverdue(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_overdue", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetInventoryOverDue] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getinventoryoverdue(p_practice_id := 9999)", "result": "PREPARED"}]}