{"procedure_name": "geteconsults", "test_date": "2025-09-12T12:50:26-07:00", "test_cases": [{"test_name": "econsults", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetEConsults] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.geteconsults(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_econsults", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetEConsults] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.geteconsults(p_practice_id := 9999)", "result": "PREPARED"}]}