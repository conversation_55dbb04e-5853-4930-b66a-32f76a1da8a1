{"procedure_name": "sp_get_patient_immunizationtype", "test_date": "2025-09-12T12:50:21-07:00", "test_cases": [{"test_name": "patient_immunization", "parameters": "2", "sql_server_query": "EXEC [dbo].[SP_Get_Patient_ImmunizationType] @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.sp_get_patient_immunizationtype(p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "no_immunization", "parameters": "9999", "sql_server_query": "EXEC [dbo].[SP_Get_Patient_ImmunizationType] @PatientId = 9999;", "postgres_query": "SELECT * FROM dbo.sp_get_patient_immunizationtype(p_patient_id := 9999)", "result": "PREPARED"}]}