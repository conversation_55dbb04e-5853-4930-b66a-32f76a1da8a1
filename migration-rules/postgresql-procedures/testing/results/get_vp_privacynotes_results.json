{"procedure_name": "get_vp_privacynotes", "test_date": "2025-09-12T12:50:21-07:00", "test_cases": [{"test_name": "privacy_notes", "parameters": "1:2", "sql_server_query": "EXEC [dbo].[Get_VP_PrivacyNotes] @PracticeId = 1, @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.get_vp_privacynotes(p_practice_id := 1, p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "no_privacy", "parameters": "9999:2", "sql_server_query": "EXEC [dbo].[Get_VP_PrivacyNotes] @PracticeId = 9999, @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.get_vp_privacynotes(p_practice_id := 9999, p_patient_id := 2)", "result": "PREPARED"}]}