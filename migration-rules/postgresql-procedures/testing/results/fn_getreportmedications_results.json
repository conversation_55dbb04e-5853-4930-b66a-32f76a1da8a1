{"procedure_name": "fn_getreportmedications", "test_date": "2025-09-12T12:50:23-07:00", "test_cases": [{"test_name": "fn_medications", "parameters": "2:1", "sql_server_query": "EXEC [dbo].[fn_GetReportMedications] @PatientId = 2, @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.fn_getreportmedications(p_patient_id := 2, p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "fn_no_medications", "parameters": "9999:1", "sql_server_query": "EXEC [dbo].[fn_GetReportMedications] @PatientId = 9999, @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.fn_getreportmedications(p_patient_id := 9999, p_practice_id := 1)", "result": "PREPARED"}]}