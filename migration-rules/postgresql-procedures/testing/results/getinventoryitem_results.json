{"procedure_name": "getinventoryitem", "test_date": "2025-09-12T12:50:24-07:00", "test_cases": [{"test_name": "single_item", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetInventoryItem] @ItemId = 1;", "postgres_query": "SELECT * FROM dbo.getinventoryitem(p_item_id := 1)", "result": "PREPARED"}, {"test_name": "invalid_item", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetInventoryItem] @ItemId = 9999;", "postgres_query": "SELECT * FROM dbo.getinventoryitem(p_item_id := 9999)", "result": "PREPARED"}]}