{"procedure_name": "getappointmenttestsavedlogs", "test_date": "2025-09-12T12:50:18-07:00", "test_cases": [{"test_name": "appointment_logs", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetAppointmentTestSavedLogs] @AppointmentId = 1;", "postgres_query": "SELECT * FROM dbo.getappointmenttestsavedlogs(p_appointment_id := 1)", "result": "PREPARED"}, {"test_name": "no_logs", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetAppointmentTestSavedLogs] @AppointmentId = 9999;", "postgres_query": "SELECT * FROM dbo.getappointmenttestsavedlogs(p_appointment_id := 9999)", "result": "PREPARED"}]}