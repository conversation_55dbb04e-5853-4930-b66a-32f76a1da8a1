{"procedure_name": "sp_find_patients_v1", "test_date": "2025-09-12T13:09:43-07:00", "test_cases": [{"test_name": "ohip_search", "parameters": "**********", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @OHIP = '**********', @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := '**********', p_top_result := 20, p_active := NULL)", "result": "PREPARED"}, {"test_name": "name_search", "parameters": "PTLastOne", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @LastName = 'PTLastOne', @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := 'PTLastOne', p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL)", "result": "PREPARED"}, {"test_name": "patient_id", "parameters": "2", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @PatientId = 2, @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := 2, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL)", "result": "PREPARED"}, {"test_name": "phone_search", "parameters": "**********", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @PhoneNumber = '**********', @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := '**********', p_ohip := NULL, p_top_result := 20, p_active := NULL)", "result": "PREPARED"}, {"test_name": "no_criteria", "parameters": "EMPTY", "sql_server_query": "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @TOPResult = 20;", "postgres_query": "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL)", "result": "PREPARED"}]}