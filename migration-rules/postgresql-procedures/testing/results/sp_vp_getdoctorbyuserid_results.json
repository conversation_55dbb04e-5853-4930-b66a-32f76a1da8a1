{"procedure_name": "sp_vp_getdoctorbyuserid", "test_date": "2025-09-12T12:50:24-07:00", "test_cases": [{"test_name": "doctor_by_user", "parameters": "1", "sql_server_query": "EXEC [dbo].[SP_VP_GetDoctorByUserId] @UserId = 1;", "postgres_query": "SELECT * FROM dbo.sp_vp_getdoctorbyuserid(p_user_id := 1)", "result": "PREPARED"}, {"test_name": "invalid_user", "parameters": "9999", "sql_server_query": "EXEC [dbo].[SP_VP_GetDoctorByUserId] @UserId = 9999;", "postgres_query": "SELECT * FROM dbo.sp_vp_getdoctorbyuserid(p_user_id := 9999)", "result": "PREPARED"}]}