{"procedure_name": "getallpracticedoctors<PERSON><PERSON><PERSON>", "test_date": "2025-09-12T12:50:24-07:00", "test_cases": [{"test_name": "olis_doctors", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetAllPracticeDoctorsForOLIS] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getallpracticedoctorsforolis(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_olis_doctors", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetAllPracticeDoctorsForOLIS] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getallpracticedoctorsforolis(p_practice_id := 9999)", "result": "PREPARED"}]}