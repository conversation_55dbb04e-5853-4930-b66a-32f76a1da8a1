{"procedure_name": "sp_get_demographicenrolment", "test_date": "2025-09-12T12:50:20-07:00", "test_cases": [{"test_name": "patient_enrolment", "parameters": "2", "sql_server_query": "EXEC [dbo].[SP_Get_DemographicEnrolment] @PatientRecordId = 2;", "postgres_query": "SELECT * FROM dbo.sp_get_demographicenrolment(p_patient_id := 2)", "result": "PASS"}, {"test_name": "no_enrolment", "parameters": "9999", "sql_server_query": "EXEC [dbo].[SP_Get_DemographicEnrolment] @PatientRecordId = 9999;", "postgres_query": "SELECT * FROM dbo.sp_get_demographicenrolment(p_patient_id := 9999)", "result": "PASS"}]}