{"procedure_name": "get_vp_cpp_skipped", "test_date": "2025-09-12T12:50:21-07:00", "test_cases": [{"test_name": "cpp_skipped", "parameters": "1:2:1", "sql_server_query": "EXEC [dbo].[Get_VP_CPP_Skipped] @PracticeId = 1, @PatientId = 2, @DoctorId = 1;", "postgres_query": "SELECT * FROM dbo.get_vp_cpp_skipped(p_practice_id := 1, p_patient_id := 2, p_doctor_id := 1)", "result": "PREPARED"}, {"test_name": "no_skipped", "parameters": "9999:2:1", "sql_server_query": "EXEC [dbo].[Get_VP_CPP_Skipped] @PracticeId = 9999, @PatientId = 2, @DoctorId = 1;", "postgres_query": "SELECT * FROM dbo.get_vp_cpp_skipped(p_practice_id := 9999, p_patient_id := 2, p_doctor_id := 1)", "result": "PREPARED"}]}