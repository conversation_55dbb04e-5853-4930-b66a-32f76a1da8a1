{"procedure_name": "getmaindoctorinfo", "test_date": "2025-09-12T12:50:19-07:00", "test_cases": [{"test_name": "patient_doctor", "parameters": "2", "sql_server_query": "EXEC [dbo].[GetMainDoctorInfo] @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.getmaindoctorinfo(p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "patient_no_doctor", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetMainDoctorInfo] @PatientId = 9999;", "postgres_query": "SELECT * FROM dbo.getmaindoctorinfo(p_patient_id := 9999)", "result": "PREPARED"}]}