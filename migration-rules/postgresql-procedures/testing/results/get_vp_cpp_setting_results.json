{"procedure_name": "get_vp_cpp_setting", "test_date": "2025-09-12T12:50:20-07:00", "test_cases": [{"test_name": "cpp_setting", "parameters": "1:1:2", "sql_server_query": "EXEC [dbo].[Get_VP_CPP_Setting] @PracticeId = 1, @DoctorId = 1, @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.get_vp_cpp_setting(p_practice_id := 1, p_doctor_id := 1, p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "no_cpp", "parameters": "1:9999:2", "sql_server_query": "EXEC [dbo].[Get_VP_CPP_Setting] @PracticeId = 1, @DoctorId = 9999, @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.get_vp_cpp_setting(p_practice_id := 1, p_doctor_id := 9999, p_patient_id := 2)", "result": "PREPARED"}]}