{"procedure_name": "sp_get_patient_vp_cpp_immunization_types", "test_date": "2025-09-12T12:50:21-07:00", "test_cases": [{"test_name": "vp_immunization", "parameters": "2:1", "sql_server_query": "EXEC [dbo].[SP_Get_Patient_VP_CPP_Immunization_Types] @PatientId = 2, @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.sp_get_patient_vp_cpp_immunization_types(p_patient_id := 2, p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_vp_immunization", "parameters": "9999:1", "sql_server_query": "EXEC [dbo].[SP_Get_Patient_VP_CPP_Immunization_Types] @PatientId = 9999, @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.sp_get_patient_vp_cpp_immunization_types(p_patient_id := 9999, p_practice_id := 1)", "result": "PREPARED"}]}