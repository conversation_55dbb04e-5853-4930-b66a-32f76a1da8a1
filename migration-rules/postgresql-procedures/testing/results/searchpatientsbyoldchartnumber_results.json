{"procedure_name": "searchpatientsbyoldchartnumber", "test_date": "2025-09-12T12:50:20-07:00", "test_cases": [{"test_name": "chart_search", "parameters": "1:OLD123", "sql_server_query": "EXEC [dbo].[SearchPatientsByOldChartNumber] @PracticeId = 1, @ChartNumber = 'OLD123';", "postgres_query": "SELECT * FROM dbo.searchpatientsbyoldchartnumber(p_practice_id := 1, p_chart_number := 'OLD123')", "result": "PREPARED"}, {"test_name": "invalid_chart", "parameters": "1:INVALID", "sql_server_query": "EXEC [dbo].[SearchPatientsByOldChartNumber] @PracticeId = 1, @ChartNumber = 'INVALID';", "postgres_query": "SELECT * FROM dbo.searchpatientsbyoldchartnumber(p_practice_id := 1, p_chart_number := 'INVALID')", "result": "PREPARED"}]}