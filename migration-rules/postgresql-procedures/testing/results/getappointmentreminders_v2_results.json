{"procedure_name": "getappointmentreminders_v2", "test_date": "2025-09-12T12:50:18-07:00", "test_cases": [{"test_name": "practice_v2", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetAppointmentReminders_v2] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getappointmentreminders_v2(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "date_range_v2", "parameters": "1:2024-01-01", "sql_server_query": "EXEC [dbo].[GetAppointmentReminders_v2] @PracticeId = 1, @StartDate = '2024-01-01';", "postgres_query": "SELECT * FROM dbo.getappointmentreminders_v2(p_practice_id := 1, p_start_date := '2024-01-01')", "result": "PREPARED"}]}