{"procedure_name": "getreportmedications", "test_date": "2025-09-12T12:50:22-07:00", "test_cases": [{"test_name": "report_medications", "parameters": "2:1", "sql_server_query": "EXEC [dbo].[GetReportMedications] @PatientId = 2, @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getreportmedications(p_patient_id := 2, p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_medications", "parameters": "9999:1", "sql_server_query": "EXEC [dbo].[GetReportMedications] @PatientId = 9999, @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getreportmedications(p_patient_id := 9999, p_practice_id := 1)", "result": "PREPARED"}]}