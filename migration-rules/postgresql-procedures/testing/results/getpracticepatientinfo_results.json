{"procedure_name": "getpracticepatientinfo", "test_date": "2025-09-12T12:50:19-07:00", "test_cases": [{"test_name": "practice_patient", "parameters": "1:2", "sql_server_query": "EXEC [dbo].[GetPracticePatientInfo] @PracticeId = 1, @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.getpracticepatientinfo(p_practice_id := 1, p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "invalid_combo", "parameters": "1:9999", "sql_server_query": "EXEC [dbo].[GetPracticePatientInfo] @PracticeId = 1, @PatientId = 9999;", "postgres_query": "SELECT * FROM dbo.getpracticepatientinfo(p_practice_id := 1, p_patient_id := 9999)", "result": "PREPARED"}]}