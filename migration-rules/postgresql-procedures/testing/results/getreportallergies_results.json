{"procedure_name": "getreportallergies", "test_date": "2025-09-12T12:50:22-07:00", "test_cases": [{"test_name": "report_allergies", "parameters": "2", "sql_server_query": "EXEC [dbo].[GetReportAllergies] @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.getreportallergies(p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "no_allergies", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetReportAllergies] @PatientId = 9999;", "postgres_query": "SELECT * FROM dbo.getreportallergies(p_patient_id := 9999)", "result": "PREPARED"}]}