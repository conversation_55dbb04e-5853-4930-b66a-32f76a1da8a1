{"procedure_name": "sp_contactlistforsendletter", "test_date": "2025-09-12T12:50:26-07:00", "test_cases": [{"test_name": "contact_letter", "parameters": "1", "sql_server_query": "EXEC [dbo].[SP_ContactListForSendLetter] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.sp_contactlistforsendletter(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_contacts", "parameters": "9999", "sql_server_query": "EXEC [dbo].[SP_ContactListForSendLetter] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.sp_contactlistforsendletter(p_practice_id := 9999)", "result": "PREPARED"}]}