{"procedure_name": "punch_can_user_punch_inout", "test_date": "2025-09-12T12:50:27-07:00", "test_cases": [{"test_name": "punch_check", "parameters": "1", "sql_server_query": "EXEC [dbo].[PUNCH_Can_User_Punch_InOut] @UserId = 1;", "postgres_query": "SELECT * FROM dbo.punch_can_user_punch_inout(p_user_id := 1)", "result": "PREPARED"}, {"test_name": "invalid_punch_user", "parameters": "9999", "sql_server_query": "EXEC [dbo].[PUNCH_Can_User_Punch_InOut] @UserId = 9999;", "postgres_query": "SELECT * FROM dbo.punch_can_user_punch_inout(p_user_id := 9999)", "result": "PREPARED"}]}