{"procedure_name": "getpracticeworklist_v2", "test_date": "2025-09-12T12:50:22-07:00", "test_cases": [{"test_name": "practice_worklist", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetPracticeWorkList_v2] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getpracticeworklist_v2(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "empty_worklist", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetPracticeWorkList_v2] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getpracticeworklist_v2(p_practice_id := 9999)", "result": "PREPARED"}]}