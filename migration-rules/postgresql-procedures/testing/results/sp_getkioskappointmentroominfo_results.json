{"procedure_name": "sp_getkioskappointmentroominfo", "test_date": "2025-09-12T12:50:22-07:00", "test_cases": [{"test_name": "room_info", "parameters": "1", "sql_server_query": "EXEC [dbo].[SP_GetKioskAppointmentRoomInfo] @AppointmentId = 1;", "postgres_query": "SELECT * FROM dbo.sp_getkioskappointmentroominfo(p_appointment_id := 1)", "result": "PREPARED"}, {"test_name": "no_room", "parameters": "9999", "sql_server_query": "EXEC [dbo].[SP_GetKioskAppointmentRoomInfo] @AppointmentId = 9999;", "postgres_query": "SELECT * FROM dbo.sp_getkioskappointmentroominfo(p_appointment_id := 9999)", "result": "PREPARED"}]}