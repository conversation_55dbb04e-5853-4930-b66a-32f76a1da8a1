{"procedure_name": "getkioskappointmentinfo", "test_date": "2025-09-12T12:50:21-07:00", "test_cases": [{"test_name": "kiosk_appointment", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetKioskAppointmentInfo] @AppointmentId = 1;", "postgres_query": "SELECT * FROM dbo.getkioskappointmentinfo(p_appointment_id := 1)", "result": "PREPARED"}, {"test_name": "invalid_appointment", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetKioskAppointmentInfo] @AppointmentId = 9999;", "postgres_query": "SELECT * FROM dbo.getkioskappointmentinfo(p_appointment_id := 9999)", "result": "PREPARED"}]}