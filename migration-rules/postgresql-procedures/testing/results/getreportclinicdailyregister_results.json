{"procedure_name": "getreportclinicdailyregister", "test_date": "2025-09-12T12:50:23-07:00", "test_cases": [{"test_name": "daily_register", "parameters": "1:2024-01-01", "sql_server_query": "EXEC [dbo].[GetReportClinicDailyRegister] @PracticeId = 1, @ReportDate = '2024-01-01';", "postgres_query": "SELECT * FROM dbo.getreportclinicdailyregister(p_practice_id := 1, p_report_date := '2024-01-01')", "result": "PREPARED"}, {"test_name": "no_register", "parameters": "1:2025-01-01", "sql_server_query": "EXEC [dbo].[GetReportClinicDailyRegister] @PracticeId = 1, @ReportDate = '2025-01-01';", "postgres_query": "SELECT * FROM dbo.getreportclinicdailyregister(p_practice_id := 1, p_report_date := '2025-01-01')", "result": "PREPARED"}]}