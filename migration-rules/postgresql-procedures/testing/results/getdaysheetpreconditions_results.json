{"procedure_name": "getdaysheetpreconditions", "test_date": "2025-09-12T13:09:41-07:00", "test_cases": [{"test_name": "empty_list", "parameters": "EMPTY", "sql_server_query": "DECLARE @emptyList dbo.IntegerList; EXEC dbo.GetDaysheetPreconditions @appointmentIds = @emptyList;", "postgres_query": "SELECT * FROM dbo.getdaysheetpreconditions(ARRAY[]::INTEGER[])", "result": "PREPARED"}, {"test_name": "single_appointment", "parameters": "1", "sql_server_query": "DECLARE @appointmentList dbo.IntegerList; INSERT INTO @appointmentList (IntegerValue) VALUES (1); EXEC dbo.GetDaysheetPreconditions @appointmentIds = @appointmentList;", "postgres_query": "SELECT * FROM dbo.getdaysheetpreconditions(ARRAY[1])", "result": "PREPARED"}, {"test_name": "multiple_appointments", "parameters": "1,2,3", "sql_server_query": "DECLARE @appointmentList dbo.IntegerList; INSERT INTO @appointmentList (IntegerValue) VALUES (1),(2),(3); EXEC dbo.GetDaysheetPreconditions @appointmentIds = @appointmentList;", "postgres_query": "SELECT * FROM dbo.getdaysheetpreconditions(ARRAY[1, 2, 3])", "result": "PREPARED"}, {"test_name": "non_existent", "parameters": "9999", "sql_server_query": "DECLARE @appointmentList dbo.IntegerList; INSERT INTO @appointmentList (IntegerValue) VALUES (9999); EXEC dbo.GetDaysheetPreconditions @appointmentIds = @appointmentList;", "postgres_query": "SELECT * FROM dbo.getdaysheetpreconditions(ARRAY[9999])", "result": "PREPARED"}, {"test_name": "mixed_appointments", "parameters": "1,9999,3", "sql_server_query": "DECLARE @appointmentList dbo.IntegerList; INSERT INTO @appointmentList (IntegerValue) VALUES (1),(9999),(3); EXEC dbo.GetDaysheetPreconditions @appointmentIds = @appointmentList;", "postgres_query": "SELECT * FROM dbo.getdaysheetpreconditions(ARRAY[1, 9999, 3])", "result": "PREPARED"}]}