{"procedure_name": "getinventoryitems", "test_date": "2025-09-12T12:50:23-07:00", "test_cases": [{"test_name": "inventory_items", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetInventoryItems] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getinventoryitems(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_items", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetInventoryItems] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getinventoryitems(p_practice_id := 9999)", "result": "PREPARED"}]}