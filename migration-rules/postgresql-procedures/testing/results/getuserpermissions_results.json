{"procedure_name": "getuserpermissions", "test_date": "2025-09-12T12:50:16-07:00", "test_cases": [{"test_name": "valid_user", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetUserPermissions] @UserId = 1;", "postgres_query": "SELECT * FROM dbo.getuserpermissions(p_user_id := 1)", "result": "UNTESTED"}, {"test_name": "invalid_user", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetUserPermissions] @UserId = 9999;", "postgres_query": "SELECT * FROM dbo.getuserpermissions(p_user_id := 9999)", "result": "UNTESTED"}]}