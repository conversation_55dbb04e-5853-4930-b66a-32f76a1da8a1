{"procedure_name": "getreportphrasesavetextbylogids", "test_date": "2025-09-12T12:50:23-07:00", "test_cases": [{"test_name": "phrase_logs", "parameters": "1,2,3", "sql_server_query": "EXEC [dbo].[GetReportPhraseSaveTextByLogIds] @LogIds = '1,2,3';", "postgres_query": "SELECT * FROM dbo.getreportphrasesavetextbylogids(p_log_ids := '1,2,3')", "result": "PREPARED"}, {"test_name": "no_phrase_logs", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetReportPhraseSaveTextByLogIds] @LogIds = '9999';", "postgres_query": "SELECT * FROM dbo.getreportphrasesavetextbylogids(p_log_ids := '9999')", "result": "PREPARED"}]}