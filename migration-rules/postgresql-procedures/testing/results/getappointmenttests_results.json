{"procedure_name": "getappointmenttests", "test_date": "2025-09-12T12:50:19-07:00", "test_cases": [{"test_name": "practice_tests", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetAppointmentTests] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getappointmenttests(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_tests", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetAppointmentTests] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getappointmenttests(p_practice_id := 9999)", "result": "PREPARED"}]}