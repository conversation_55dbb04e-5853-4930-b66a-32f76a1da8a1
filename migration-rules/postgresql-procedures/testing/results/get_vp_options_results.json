{"procedure_name": "get_vp_options", "test_date": "2025-09-12T12:50:20-07:00", "test_cases": [{"test_name": "vp_options", "parameters": "", "sql_server_query": "EXEC [dbo].[Get_VP_Options];", "postgres_query": "SELECT * FROM dbo.get_vp_options()", "result": "PREPARED"}, {"test_name": "no_vp_options", "parameters": "", "sql_server_query": "EXEC [dbo].[Get_VP_Options];", "postgres_query": "SELECT * FROM dbo.get_vp_options()", "result": "PREPARED"}]}