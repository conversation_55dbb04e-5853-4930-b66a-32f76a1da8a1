{"procedure_name": "getpracticescheduleduserweekdays", "test_date": "2025-09-12T12:50:17-07:00", "test_cases": [{"test_name": "user_schedule", "parameters": "1:1", "sql_server_query": "EXEC [dbo].[GetPracticeScheduledUserWeekDays] @PracticeId = 1, @UserId = 1;", "postgres_query": "SELECT * FROM dbo.getpracticescheduleduserweekdays(p_practice_id := 1, p_user_id := 1)", "result": "PASS"}, {"test_name": "no_schedule", "parameters": "1:9999", "sql_server_query": "EXEC [dbo].[GetPracticeScheduledUserWeekDays] @PracticeId = 1, @UserId = 9999;", "postgres_query": "SELECT * FROM dbo.getpracticescheduleduserweekdays(p_practice_id := 1, p_user_id := 9999)", "result": "PASS"}]}