{"procedure_name": "sp_getpatientdemographicinfo", "test_date": "2025-09-12T12:50:19-07:00", "test_cases": [{"test_name": "patient_demo", "parameters": "1:2", "sql_server_query": "EXEC [dbo].[SP_GetPatientDemographicInfo] @PracticeId = 1, @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.sp_getpatientdemographicinfo(p_practice_id := 1, p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "invalid_patient", "parameters": "1:9999", "sql_server_query": "EXEC [dbo].[SP_GetPatientDemographicInfo] @PracticeId = 1, @PatientId = 9999;", "postgres_query": "SELECT * FROM dbo.sp_getpatientdemographicinfo(p_practice_id := 1, p_patient_id := 9999)", "result": "PREPARED"}]}