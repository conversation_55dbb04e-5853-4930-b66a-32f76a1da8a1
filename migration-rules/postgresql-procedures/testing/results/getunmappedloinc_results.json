{"procedure_name": "getunmappedloinc", "test_date": "2025-09-12T12:50:27-07:00", "test_cases": [{"test_name": "unmapped_loinc", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetUnmappedLOINC] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getunmappedloinc(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_unmapped", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetUnmappedLOINC] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getunmappedloinc(p_practice_id := 9999)", "result": "PREPARED"}]}