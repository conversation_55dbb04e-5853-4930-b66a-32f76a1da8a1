{"procedure_name": "geteconsultpatientreports", "test_date": "2025-09-12T12:50:26-07:00", "test_cases": [{"test_name": "econsult_reports", "parameters": "2:1", "sql_server_query": "EXEC [dbo].[GetEconsultPatientReports] @PatientId = 2, @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.geteconsultpatientreports(p_patient_id := 2, p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_econsult", "parameters": "9999:1", "sql_server_query": "EXEC [dbo].[GetEconsultPatientReports] @PatientId = 9999, @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.geteconsultpatientreports(p_patient_id := 9999, p_practice_id := 1)", "result": "PREPARED"}]}