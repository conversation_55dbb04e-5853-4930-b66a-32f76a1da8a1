{"procedure_name": "getdoctorinfo", "test_date": "2025-09-12T12:50:24-07:00", "test_cases": [{"test_name": "doctor_info", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetDoctorInfo] @DoctorId = 1;", "postgres_query": "SELECT * FROM dbo.getdoctorinfo(p_doctor_id := 1)", "result": "PREPARED"}, {"test_name": "invalid_doctor", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetDoctorInfo] @DoctorId = 9999;", "postgres_query": "SELECT * FROM dbo.getdoctorinfo(p_doctor_id := 9999)", "result": "PREPARED"}]}