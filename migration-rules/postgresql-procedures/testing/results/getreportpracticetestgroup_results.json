{"procedure_name": "getreportpracticetestgroup", "test_date": "2025-09-12T12:50:23-07:00", "test_cases": [{"test_name": "practice_testgroup", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetReportPracticeTestGroup] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getreportpracticetestgroup(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_testgroup", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetReportPracticeTestGroup] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getreportpracticetestgroup(p_practice_id := 9999)", "result": "PREPARED"}]}