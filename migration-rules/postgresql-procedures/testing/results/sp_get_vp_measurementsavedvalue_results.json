{"procedure_name": "sp_get_vp_measurementsavedvalue", "test_date": "2025-09-12T12:50:25-07:00", "test_cases": [{"test_name": "measurement_saved", "parameters": "1:2:3", "sql_server_query": "EXEC [dbo].[SP_Get_VP_MeasurementSavedValue] @PracticeId = 1, @PatientId = 2, @MeasurementId = 3;", "postgres_query": "SELECT * FROM dbo.sp_get_vp_measurementsavedvalue(p_practice_id := 1, p_patient_id := 2, p_measurement_id := 3)", "result": "PREPARED"}, {"test_name": "no_saved", "parameters": "9999:2:3", "sql_server_query": "EXEC [dbo].[SP_Get_VP_MeasurementSavedValue] @PracticeId = 9999, @PatientId = 2, @MeasurementId = 3;", "postgres_query": "SELECT * FROM dbo.sp_get_vp_measurementsavedvalue(p_practice_id := 9999, p_patient_id := 2, p_measurement_id := 3)", "result": "PREPARED"}]}