{"procedure_name": "getvplabresults", "test_date": "2025-09-12T12:50:25-07:00", "test_cases": [{"test_name": "vp_lab_results", "parameters": "1:2", "sql_server_query": "EXEC [dbo].[GetVPLabResults] @PracticeId = 1, @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.getvplabresults(p_practice_id := 1, p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "no_lab_results", "parameters": "9999:2", "sql_server_query": "EXEC [dbo].[GetVPLabResults] @PracticeId = 9999, @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.getvplabresults(p_practice_id := 9999, p_patient_id := 2)", "result": "PREPARED"}]}