{"procedure_name": "getdaysheetcohorts", "test_date": "2025-09-12T13:09:41-07:00", "test_cases": [{"test_name": "empty_list", "parameters": "EMPTY", "sql_server_query": "DECLARE @emptyList dbo.IntegerList; EXEC dbo.GetDaysheetCohorts @patientIds = @emptyList;", "postgres_query": "SELECT * FROM dbo.getdaysheetcohorts(ARRAY[]::INTEGER[])", "result": "PREPARED"}, {"test_name": "single_patient", "parameters": "1001", "sql_server_query": "DECLARE @patientList dbo.IntegerList; INSERT INTO @patientList (IntegerValue) VALUES (1001); EXEC dbo.GetDaysheetCohorts @patientIds = @patientList;", "postgres_query": "SELECT * FROM dbo.getdaysheetcohorts(ARRAY[1001])", "result": "PREPARED"}, {"test_name": "multiple_patients", "parameters": "1001,1002,1003", "sql_server_query": "DECLARE @patientList dbo.IntegerList; INSERT INTO @patientList (IntegerValue) VALUES (1001),(1002),(1003); EXEC dbo.GetDaysheetCohorts @patientIds = @patientList;", "postgres_query": "SELECT * FROM dbo.getdaysheetcohorts(ARRAY[1001, 1002, 1003])", "result": "PREPARED"}, {"test_name": "non_existent", "parameters": "9999", "sql_server_query": "DECLARE @patientList dbo.IntegerList; INSERT INTO @patientList (IntegerValue) VALUES (9999); EXEC dbo.GetDaysheetCohorts @patientIds = @patientList;", "postgres_query": "SELECT * FROM dbo.getdaysheetcohorts(ARRAY[9999])", "result": "PREPARED"}]}