{"procedure_name": "searchauditbydatenpatient", "test_date": "2025-09-12T12:50:25-07:00", "test_cases": [{"test_name": "audit_patient", "parameters": "2024-01-01:2024-01-31:2", "sql_server_query": "EXEC [dbo].[SearchAuditByDateNPatient] @StartDate = '2024-01-01', @EndDate = '2024-01-31', @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.searchauditbydatenpatient(p_start_date := '2024-01-01', p_end_date := '2024-01-31', p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "no_audit_patient", "parameters": "2025-01-01:2025-01-31:9999", "sql_server_query": "EXEC [dbo].[SearchAuditByDateNPatient] @StartDate = '2025-01-01', @EndDate = '2025-01-31', @PatientId = 9999;", "postgres_query": "SELECT * FROM dbo.searchauditbydatenpatient(p_start_date := '2025-01-01', p_end_date := '2025-01-31', p_patient_id := 9999)", "result": "PREPARED"}]}