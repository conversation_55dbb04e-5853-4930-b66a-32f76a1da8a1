{"procedure_name": "getdaysheetappointments", "test_date": "2025-09-12T12:50:19-07:00", "test_cases": [{"test_name": "daysheet_date", "parameters": "1:2024-01-01", "sql_server_query": "EXEC [dbo].[GetDaysheetAppointments] @PracticeId = 1, @DaysheetDate = '2024-01-01';", "postgres_query": "SELECT * FROM dbo.getdaysheetappointments(p_practice_id := 1, p_daysheet_date := '2024-01-01')", "result": "PREPARED"}, {"test_name": "no_daysheet", "parameters": "1:2025-01-01", "sql_server_query": "EXEC [dbo].[GetDaysheetAppointments] @PracticeId = 1, @DaysheetDate = '2025-01-01';", "postgres_query": "SELECT * FROM dbo.getdaysheetappointments(p_practice_id := 1, p_daysheet_date := '2025-01-01')", "result": "PREPARED"}]}