{"procedure_name": "getpatientappointmenttests", "test_date": "2025-09-12T12:50:19-07:00", "test_cases": [{"test_name": "patient_tests", "parameters": "2", "sql_server_query": "EXEC [dbo].[GetPatientAppointmentTests] @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.getpatientappointmenttests(p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "no_tests", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetPatientAppointmentTests] @PatientId = 9999;", "postgres_query": "SELECT * FROM dbo.getpatientappointmenttests(p_patient_id := 9999)", "result": "PREPARED"}]}