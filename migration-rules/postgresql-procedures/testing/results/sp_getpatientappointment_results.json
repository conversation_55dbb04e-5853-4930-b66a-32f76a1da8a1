{"procedure_name": "sp_getpatientappointment", "test_date": "2025-09-12T12:50:18-07:00", "test_cases": [{"test_name": "patient_appointment", "parameters": "2", "sql_server_query": "EXEC [dbo].[SP_GetPatientAppointment] @PatientId = 2;", "postgres_query": "SELECT * FROM dbo.sp_getpatientappointment(p_patient_id := 2)", "result": "PREPARED"}, {"test_name": "invalid_patient", "parameters": "9999", "sql_server_query": "EXEC [dbo].[SP_GetPatientAppointment] @PatientId = 9999;", "postgres_query": "SELECT * FROM dbo.sp_getpatientappointment(p_patient_id := 9999)", "result": "PREPARED"}]}