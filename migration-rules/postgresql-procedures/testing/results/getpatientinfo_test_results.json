{"procedure_name": "GetPatientInfo", "test_run_date": "2024-09-11T16:00:00Z", "test_cases": [{"test_name": "single_patient_basic", "parameters": "patientId=1002, appointmentId=0, testId=0, activeStatus=0", "sql_server_query": "EXEC [dbo].[GetPatientInfo] @patientId = 1002;", "postgres_query": "SELECT * FROM dbo.GetPatientInfo(1002, NULL, NULL, 0);", "description": "Basic patient info without appointment or test context", "result": "PENDING"}, {"test_name": "patient_with_appointment", "parameters": "patientId=1002, appointmentId=1, testId=0, activeStatus=0", "sql_server_query": "EXEC [dbo].[GetPatientInfo] @patientId = 1002, @appointmentId = 1;", "postgres_query": "SELECT * FROM dbo.GetPatientInfo(1002, 1, NULL, 0);", "description": "Patient info with appointment context", "result": "PENDING"}, {"test_name": "patient_with_test", "parameters": "patientId=1002, appointmentId=0, testId=1, activeStatus=0", "sql_server_query": "EXEC [dbo].[GetPatientInfo] @patientId = 1002, @testId = 1;", "postgres_query": "SELECT * FROM dbo.GetPatientInfo(1002, NULL, 1, 0);", "description": "Patient info with test context", "result": "PENDING"}, {"test_name": "patient_full_context", "parameters": "patientId=1002, appointmentId=1, testId=1, activeStatus=0", "sql_server_query": "EXEC [dbo].[GetPatientInfo] @patientId = 1002, @appointmentId = 1, @testId = 1;", "postgres_query": "SELECT * FROM dbo.GetPatientInfo(1002, 1, 1, 0);", "description": "Patient info with both appointment and test context", "result": "PENDING"}, {"test_name": "non_existent_patient", "parameters": "patientId=9999, appointmentId=0, testId=0, activeStatus=0", "sql_server_query": "EXEC [dbo].[GetPatientInfo] @patientId = 9999;", "postgres_query": "SELECT * FROM dbo.GetPatientInfo(9999, NULL, NULL, 0);", "description": "Non-existent patient should return no results", "result": "PENDING"}]}