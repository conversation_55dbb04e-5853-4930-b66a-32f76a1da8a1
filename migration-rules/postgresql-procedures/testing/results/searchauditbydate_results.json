{"procedure_name": "searchauditbydate", "test_date": "2025-09-12T12:50:25-07:00", "test_cases": [{"test_name": "audit_date", "parameters": "2024-01-01:2024-01-31", "sql_server_query": "EXEC [dbo].[SearchAuditByDate] @StartDate = '2024-01-01', @EndDate = '2024-01-31';", "postgres_query": "SELECT * FROM dbo.searchauditbydate(p_start_date := '2024-01-01', p_end_date := '2024-01-31')", "result": "PREPARED"}, {"test_name": "no_audit", "parameters": "2025-01-01:2025-01-31", "sql_server_query": "EXEC [dbo].[SearchAuditByDate] @StartDate = '2025-01-01', @EndDate = '2025-01-31';", "postgres_query": "SELECT * FROM dbo.searchauditbydate(p_start_date := '2025-01-01', p_end_date := '2025-01-31')", "result": "PREPARED"}]}