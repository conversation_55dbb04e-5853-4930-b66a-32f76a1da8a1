{"procedure_name": "getssrsreportbyappointmenttestid", "test_date": "2025-09-12T12:50:23-07:00", "test_cases": [{"test_name": "ssrs_report", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetSSRSReportByAppointmentTestId] @AppointmentTestId = 1;", "postgres_query": "SELECT * FROM dbo.getssrsreportbyappointmenttestid(p_appointment_test_id := 1)", "result": "PREPARED"}, {"test_name": "no_ssrs", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetSSRSReportByAppointmentTestId] @AppointmentTestId = 9999;", "postgres_query": "SELECT * FROM dbo.getssrsreportbyappointmenttestid(p_appointment_test_id := 9999)", "result": "PREPARED"}]}