{"procedure_name": "sch_getccdoctors", "test_date": "2025-09-12T13:09:42-07:00", "test_cases": [{"test_name": "single_patient", "parameters": "1:1001", "sql_server_query": "EXEC dbo.SCH_GetCCDoctors @practiceId = 1, @patientId = 1001, @appointmentId = 0;", "postgres_query": "SELECT * FROM dbo.SCH_GetCCDoctors(1, 1001, 0)", "result": "EXECUTED"}, {"test_name": "multiple_params", "parameters": "1:1001:100", "sql_server_query": "EXEC dbo.SCH_GetCCDoctors @practiceId = 1, @patientId = 1001, @appointmentId = 100;", "postgres_query": "SELECT * FROM dbo.SCH_GetCCDoctors(1, 1001, 100)", "result": "EXECUTED"}, {"test_name": "non_existent_patient", "parameters": "1:9999", "sql_server_query": "EXEC dbo.SCH_GetCCDoctors @practiceId = 1, @patientId = 9999, @appointmentId = 0;", "postgres_query": "SELECT * FROM dbo.SCH_GetCCDoctors(1, 9999, 0)", "result": "EXECUTED"}, {"test_name": "zero_appointment", "parameters": "1:1001:0", "sql_server_query": "EXEC dbo.SCH_GetCCDoctors @practiceId = 1, @patientId = 1001, @appointmentId = 0;", "postgres_query": "SELECT * FROM dbo.SCH_GetCCDoctors(1, 1001, 0)", "result": "EXECUTED"}]}