{"procedure_name": "getappointmentmodifiers", "test_date": "2025-09-12T12:50:19-07:00", "test_cases": [{"test_name": "appointment_mods", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetAppointmentModifiers] @AppointmentId = 1;", "postgres_query": "SELECT * FROM dbo.getappointmentmodifiers(p_appointment_id := 1)", "result": "PREPARED"}, {"test_name": "no_mods", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetAppointmentModifiers] @AppointmentId = 9999;", "postgres_query": "SELECT * FROM dbo.getappointmentmodifiers(p_appointment_id := 9999)", "result": "PREPARED"}]}