{"procedure_name": "sp_update_practicedoctor_olis_lastaccessdatetime", "test_date": "2025-09-12T12:50:27-07:00", "test_cases": [{"test_name": "olis_update", "parameters": "1:1", "sql_server_query": "EXEC [dbo].[SP_Update_PracticeDoctor_OLIS_LastAccessDateTime] @PracticeId = 1, @DoctorId = 1;", "postgres_query": "SELECT * FROM dbo.sp_update_practicedoctor_olis_lastaccessdatetime(p_practice_id := 1, p_doctor_id := 1)", "result": "PREPARED"}, {"test_name": "invalid_olis", "parameters": "9999:9999", "sql_server_query": "EXEC [dbo].[SP_Update_PracticeDoctor_OLIS_LastAccessDateTime] @PracticeId = 9999, @DoctorId = 9999;", "postgres_query": "SELECT * FROM dbo.sp_update_practicedoctor_olis_lastaccessdatetime(p_practice_id := 9999, p_doctor_id := 9999)", "result": "PREPARED"}]}