{"procedure_name": "get<PERSON>os<PERSON><PERSON><PERSON><PERSON>", "test_date": "2025-09-12T12:50:21-07:00", "test_cases": [{"test_name": "kiosk_checkins", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetKioskCheckins] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getkioskcheckins(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_checkins", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetKioskCheckins] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getkiosk<PERSON>ckins(p_practice_id := 9999)", "result": "PREPARED"}]}