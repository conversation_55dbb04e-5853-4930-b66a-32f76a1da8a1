{"procedure_name": "getcppcategoriesbydoctor", "test_date": "2025-09-12T12:50:21-07:00", "test_cases": [{"test_name": "doctor_cpp", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetCPPCategoriesByDoctor] @DoctorId = 1;", "postgres_query": "SELECT * FROM dbo.getcppcategoriesbydoctor(p_doctor_id := 1)", "result": "PREPARED"}, {"test_name": "no_cpp_doctor", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetCPPCategoriesByDoctor] @DoctorId = 9999;", "postgres_query": "SELECT * FROM dbo.getcppcategoriesbydoctor(p_doctor_id := 9999)", "result": "PREPARED"}]}