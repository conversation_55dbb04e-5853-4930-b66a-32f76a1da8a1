{"procedure_name": "getwaitlisttests", "test_date": "2025-09-12T12:50:22-07:00", "test_cases": [{"test_name": "waitlist_tests", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetWaitlistTests] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getwaitlisttests(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_waitlist_tests", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetWaitlistTests] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getwaitlisttests(p_practice_id := 9999)", "result": "PREPARED"}]}