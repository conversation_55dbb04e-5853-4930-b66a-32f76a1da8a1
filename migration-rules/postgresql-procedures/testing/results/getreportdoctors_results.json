{"procedure_name": "getreportdoctors", "test_date": "2025-09-12T12:50:23-07:00", "test_cases": [{"test_name": "report_doctors", "parameters": "1", "sql_server_query": "EXEC [dbo].[GetReportDoctors] @PracticeId = 1;", "postgres_query": "SELECT * FROM dbo.getreportdoctors(p_practice_id := 1)", "result": "PREPARED"}, {"test_name": "no_report_doctors", "parameters": "9999", "sql_server_query": "EXEC [dbo].[GetReportDoctors] @PracticeId = 9999;", "postgres_query": "SELECT * FROM dbo.getreportdoctors(p_practice_id := 9999)", "result": "PREPARED"}]}