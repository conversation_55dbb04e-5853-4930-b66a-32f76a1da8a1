# GetDaysheetPreconditions Runtime Error Fix

## Problem
The application was throwing a runtime error when trying to call the `GetDaysheetPreconditions` function:

```
System.Exception: storedProcedure:dbo.GetDaysheetPreconditions Parameters:System.Int32[] 
Npgsql.PostgresException (0x80004005): 42P01: relation "dbo.getdaysheetpreconditions" does not exist
```

## Root Cause Analysis

The issue occurred because there are **two different code paths** in the application:

### Code Path 1: Repository Pattern (✅ Working)
- **Location**: `Cerebrum.Data/Repositories/DaysheetRepository.cs:251`
- **Method**: Uses `GetDataWithDbParameters` with explicit SQL
- **Query**: `"SELECT * FROM dbo.GetDaysheetPreconditions(@p_appointment_ids)"`
- **Status**: ✅ This was working correctly

### Code Path 2: Legacy BLL Pattern (❌ Broken)  
- **Location**: `Cerebrum.BLL/Daysheet/DaysheetBLL.cs:762`
- **Method**: Uses `GetData` with stored procedure name
- **Query**: `"dbo.GetDaysheetPreconditions"` 
- **Problem**: The `GetData` method internally converts this to lowercase `"dbo.getdaysheetpreconditions"`

## Investigation Details

1. **Function Naming**: The PostgreSQL function was created as `dbo.GetDaysheetPreconditions` (PascalCase)
2. **Application Expectation**: The legacy `GetData` method expects lowercase function names
3. **Missing Link**: No lowercase alias existed for backward compatibility

## Solution Implemented

Converted to **lowercase-only PostgreSQL function** following PostgreSQL best practices:

```sql
-- Single lowercase function (PostgreSQL best practice)
CREATE OR REPLACE FUNCTION dbo.getdaysheetpreconditions(
    p_appointment_ids integer[] DEFAULT ARRAY[]::integer[]
)
RETURNS TABLE (
    id integer,
    type text,
    status boolean,
    appointmentid integer
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Return appointment preconditions for the specified appointment IDs
    RETURN QUERY
    SELECT 
        ap.id::integer,
        ap.type::text,
        ap.status,
        ap.appointmentid::integer
    FROM dbo.appointmentpreconditons ap
    WHERE ap.appointmentid = ANY(p_appointment_ids);
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.getdaysheetpreconditions TO postgres;
```

## Benefits of This Approach

1. **✅ PostgreSQL Best Practices**: Functions use lowercase naming convention
2. **✅ Backward Compatibility**: Legacy `GetData` calls work correctly  
3. **✅ Forward Compatibility**: Repository pattern calls work correctly
4. **✅ Simplified Maintenance**: Single function, no aliases to maintain
5. **✅ Consistency**: Both application code paths use same function

## Testing Verification

The lowercase function works correctly for both code paths:

```sql
-- Works for both legacy GetData and repository calls
SELECT * FROM dbo.getdaysheetpreconditions(ARRAY[1,2,3]);
```

Returns expected results for all test scenarios.

## Migration Strategy for Other Procedures

This same pattern should be applied to **all migrated PostgreSQL functions**:

1. **Function Naming**: Use lowercase only (PostgreSQL best practice)
2. **Repository Updates**: Update repository calls to use lowercase names
3. **Grants**: Ensure functions have proper permissions
4. **Documentation**: Document lowercase naming convention

### Template Pattern:
```sql
-- Single lowercase function (PostgreSQL best practice)
CREATE OR REPLACE FUNCTION dbo.procedurename(...) 
RETURNS TABLE (...) AS $$ 
BEGIN 
    RETURN QUERY
    SELECT ... FROM dbo.tablename ...;
END; 
$$;

-- Permissions
GRANT EXECUTE ON FUNCTION dbo.procedurename TO postgres;
```

## Updated Testing Framework

The automated test runner has been updated to use lowercase function names:
- Repository calls updated to use lowercase functions
- Legacy GetData calls work with lowercase functions
- Single consistent naming convention throughout

## Status
🎉 **RESOLVED** - Application runtime error fixed. Both code paths now work correctly.

---
*Fix implemented: $(date)*  
*Runtime error resolved for GetDaysheetPreconditions migration*