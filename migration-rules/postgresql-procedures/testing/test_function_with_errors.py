#!/usr/bin/env python3
"""
Test PostgreSQL functions with proper error reporting
This script attempts to connect to PostgreSQL and execute functions with detailed error reporting
"""

import sys
import subprocess
import json

def test_postgres_function_via_mcp(function_call):
    """
    Test using MCP but capture any potential error information
    """
    try:
        # For now, we'll use a subprocess to call the MCP tool
        # This is a workaround since MCP tools don't show errors properly
        print(f"Testing: {function_call}")
        print("Note: Using MCP tools - errors may not be visible")
        return True, "MCP execution completed (error status unknown)"
    except Exception as e:
        return False, str(e)

def test_postgres_function_direct(function_call, connection_params=None):
    """
    Test using direct PostgreSQL connection with error handling
    """
    try:
        # Try to construct a psql command
        if connection_params is None:
            # Try common connection parameters
            connection_params = {
                'host': 'localhost',
                'port': '5432', 
                'database': 'c3_dev',
                'user': 'postgres'
            }
        
        cmd = [
            'psql',
            '-h', connection_params['host'],
            '-p', connection_params['port'],
            '-d', connection_params['database'],
            '-U', connection_params['user'],
            '-c', function_call,
            '--no-password'  # Try without password first
        ]
        
        print(f"Attempting direct PostgreSQL connection...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            return True, result.stdout
        else:
            return False, f"Error (code {result.returncode}): {result.stderr}"
            
    except subprocess.TimeoutExpired:
        return False, "Query timed out"
    except FileNotFoundError:
        return False, "psql command not found - PostgreSQL client not installed"
    except Exception as e:
        return False, f"Unexpected error: {str(e)}"

def test_function_comprehensive(function_name, test_params):
    """
    Test a function using multiple methods to find errors
    """
    print(f"\n{'='*60}")
    print(f"COMPREHENSIVE TESTING: {function_name}")
    print(f"{'='*60}")
    
    results = []
    
    for i, param in enumerate(test_params, 1):
        print(f"\nTest {i}: {function_name}({param})")
        print("-" * 40)
        
        function_call = f"SELECT * FROM dbo.{function_name}({param});"
        
        # Method 1: Try direct PostgreSQL connection
        success, message = test_postgres_function_direct(function_call)
        print(f"Direct psql: {'SUCCESS' if success else 'FAILED'}")
        print(f"Result: {message}")
        
        results.append({
            'test': f"{function_name}({param})",
            'direct_psql': {'success': success, 'message': message}
        })
        
        if not success and "no password supplied" in message.lower():
            print("Note: Direct connection requires password setup")
        
    return results

def main():
    if len(sys.argv) < 2:
        print("Usage: test_function_with_errors.py <function_name> [param1] [param2] ...")
        print("Example: test_function_with_errors.py getusermenucount 1 9999")
        sys.exit(1)
    
    function_name = sys.argv[1]
    test_params = sys.argv[2:] if len(sys.argv) > 2 else ['1', '9999']
    
    print(f"Testing PostgreSQL function: {function_name}")
    print(f"Test parameters: {test_params}")
    
    results = test_function_comprehensive(function_name, test_params)
    
    # Summary
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['direct_psql']['success'])
    
    print(f"Total tests: {total_tests}")
    print(f"Successful: {successful_tests}")
    print(f"Failed: {total_tests - successful_tests}")
    
    if successful_tests == 0:
        print("\n⚠️  All tests failed - function likely has errors")
        return 1
    elif successful_tests == total_tests:
        print("\n✅ All tests passed")
        return 0
    else:
        print("\n⚠️  Some tests failed - investigate specific parameters")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)