# AI Prompt: Add Procedure to Tests and Fix Issues

## Objective
Add a PostgreSQL function and corresponding SQL Server stored procedure to the automated test framework (`run_all_tests.sh`), then run tests and fix any errors or data discrepancies in the PostgreSQL function.

## Input Required
- **PostgreSQL Function Name**: `{POSTGRES_FUNCTION_NAME}` (e.g., `sp_find_patients_v1`)
- **SQL Server Procedure Name**: `{SQLSERVER_PROCEDURE_NAME}` (e.g., `SP_Find_Patients_V1`)
- **Test Cases**: `{TEST_CASES}` (e.g., `"basic_search:param1 complex_search:param1:param2 empty_test:EMPTY"`)

## Task Steps

### 1. Add to run_all_tests.sh

**Add procedure to working list:**
```bash
# In WORKING_PROCEDURES array, add:
    "{POSTGRES_FUNCTION_NAME}"
```

**Add test cases:**
```bash
# In PROCEDURE_TEST_CASES array, add:
PROCEDURE_TEST_CASES["{POSTGRES_FUNCTION_NAME}"]="{TEST_CASES}"
```

**Add SQL Server execution case:**
```bash
# In execute_sql_server() function, before the *) case, add:
        "{POSTGRES_FUNCTION_NAME}")
            case "$test_case" in
                "test_case_1") echo "EXEC [dbo].[{SQLSERVER_PROCEDURE_NAME}] @Param1 = '$params';" ;;
                "test_case_2") echo "EXEC [dbo].[{SQLSERVER_PROCEDURE_NAME}] @Param1 = '$params', @Param2 = 'value';" ;;
                "empty_test") echo "EXEC [dbo].[{SQLSERVER_PROCEDURE_NAME}] @DefaultParam = 'default';" ;;
                *) echo "-- No SQL Server test case defined for $proc_name.$test_case" ;;
            esac
            ;;
```

**Add PostgreSQL execution case:**
```bash
# In execute_postgres() function, before the *) case, add:
        "{POSTGRES_FUNCTION_NAME}")
            case "$test_case" in
                "test_case_1") echo "SELECT * FROM dbo.{POSTGRES_FUNCTION_NAME}(p_param1 := '$params')" ;;
                "test_case_2") echo "SELECT * FROM dbo.{POSTGRES_FUNCTION_NAME}(p_param1 := '$params', p_param2 := 'value')" ;;
                "empty_test") echo "SELECT * FROM dbo.{POSTGRES_FUNCTION_NAME}(p_default_param := 'default')" ;;
                *) echo "-- No PostgreSQL test case defined for $func_name.$test_case" ;;
            esac
            ;;
```

### 2. Run Tests and Identify Issues

```bash
./run_all_tests.sh
```

**Look for:**
- ✅ Test cases execute without errors
- ❌ PostgreSQL syntax errors
- ❌ Different result counts between databases
- ❌ Different column values/types
- ❌ Different result ordering

### 3. Execute Actual Database Tests

Use MCP servers to run the generated queries and compare results:

**Test each case:**
```bash
# SQL Server
mcp__mssql__execute_sql: "EXEC [dbo].[{SQLSERVER_PROCEDURE_NAME}] @Param = 'test'"

# PostgreSQL  
mcp__postgres__execute_sql: "SELECT * FROM dbo.{POSTGRES_FUNCTION_NAME}(p_param := 'test')"
```

**Compare:**
- Row counts
- Column names and types  
- Data values
- Result ordering

### 4. Fix PostgreSQL Function Issues

**Common fixes needed:**

#### A. Parameter Issues
```sql
-- Fix parameter names (add p_ prefix)
-- Fix parameter types (INTEGER vs INT, VARCHAR vs TEXT)
CREATE OR REPLACE FUNCTION dbo.{POSTGRES_FUNCTION_NAME}(
    p_param1 INTEGER,           -- Was: param1 INT
    p_param2 VARCHAR(100),      -- Was: param2 TEXT
    ...
```

#### B. SQL Syntax Issues
```sql
-- Fix ILIKE vs CONTAINS
WHERE d.lastname ILIKE ('%' || p_search_term || '%')  -- Was: CONTAINS

-- Fix Boolean comparisons  
WHERE d.active = true                                  -- Was: d.active = 1

-- Fix schema references
FROM dbo.tablename t                                   -- Was: FROM tablename t
```

#### C. Result Format Issues
```sql
-- Fix column naming/casing
SELECT 
    d.patientrecordid as patientid,                    -- Match SQL Server output
    d.firstname::text,                                 -- Ensure text type
    d.dateofbirth AT TIME ZONE 'UTC',                 -- Match datetime format
    COALESCE(d.middlename, '')::text as middlename     -- Handle NULLs like SQL Server
```

#### D. Ordering Issues
```sql
-- Add ORDER BY to match SQL Server results
ORDER BY d.lastname, d.firstname, d.patientrecordid   -- Match SQL Server ordering
```

### 5. Deploy and Re-test

**Deploy changes:**
```bash
./deploy-postgres-functions.sh -f {POSTGRES_FUNCTION_NAME}.sql
```

**Re-run tests:**
```bash
./run_all_tests.sh
```

**Verify with MCP:**
- Run same test cases on both databases
- Confirm identical results (count, values, order)

## Success Criteria

✅ **run_all_tests.sh executes without errors**
✅ **All test cases generate valid SQL for both databases**  
✅ **PostgreSQL and SQL Server return identical results for all test cases**
✅ **Result counts match exactly**
✅ **Column names and data types are consistent**
✅ **Result ordering is identical**

## Example Implementation

**Input:**
- PostgreSQL Function: `get_patient_appointments`
- SQL Server Procedure: `GetPatientAppointments` 
- Test Cases: `"single_patient:1002 date_range:1002:2024-01-01 no_results:9999"`

**Output in run_all_tests.sh:**
```bash
WORKING_PROCEDURES=(
    "getdaysheetcohorts"
    "getdaysheetpreconditions"
    "sch_getccdoctors"
    "sp_find_patients_v1"
    "get_patient_appointments"    # ADDED
)

PROCEDURE_TEST_CASES["get_patient_appointments"]="single_patient:1002 date_range:1002:2024-01-01 no_results:9999"

# In execute_sql_server():
"get_patient_appointments")
    case "$test_case" in
        "single_patient") echo "EXEC [dbo].[GetPatientAppointments] @PatientId = $params;" ;;
        "date_range") 
            IFS=':' read -ra PARAMS <<< "$params"
            echo "EXEC [dbo].[GetPatientAppointments] @PatientId = ${PARAMS[0]}, @StartDate = '${PARAMS[1]}';" ;;
        "no_results") echo "EXEC [dbo].[GetPatientAppointments] @PatientId = $params;" ;;
        *) echo "-- No SQL Server test case defined for $proc_name.$test_case" ;;
    esac
    ;;

# In execute_postgres():
"get_patient_appointments")
    case "$test_case" in
        "single_patient") echo "SELECT * FROM dbo.get_patient_appointments(p_patient_id := $params)" ;;
        "date_range")
            IFS=':' read -ra PARAMS <<< "$params" 
            echo "SELECT * FROM dbo.get_patient_appointments(p_patient_id := ${PARAMS[0]}, p_start_date := '${PARAMS[1]}')" ;;
        "no_results") echo "SELECT * FROM dbo.get_patient_appointments(p_patient_id := $params)" ;;
        *) echo "-- No PostgreSQL test case defined for $func_name.$test_case" ;;
    esac
    ;;
```

## Tools Available
- `./run_all_tests.sh` - Test execution framework
- `./deploy-postgres-functions.sh -f <file>.sql` - Deploy function changes
- `mcp__mssql__execute_sql` - Execute SQL Server queries
- `mcp__postgres__execute_sql` - Execute PostgreSQL queries
- `mcp__mssql__list_tables` / `mcp__postgres__list_tables` - Database schema info

## Key Principles
1. **Test First**: Add to test framework before making changes
2. **Compare Everything**: Row counts, data values, column types, ordering
3. **Fix Systematically**: Address one issue at a time, re-test after each fix
4. **Match Exactly**: PostgreSQL results must be identical to SQL Server
5. **Use MCP Tools**: Always verify with actual database queries, not just assumptions