#!/usr/bin/env python3

"""
Automated test for SCH_GetCCDoctors procedure migration
Uses MCP tools to execute queries in both SQL Server and PostgreSQL
"""

import json
import sys

def test_sch_getccdoctors():
    """Test SCH_GetCCDoctors with multiple test cases"""
    
    print("Testing SCH_GetCCDoctors procedure migration...")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "single_patient",
            "params": {"practice_id": 1, "patient_id": 1001, "appointment_id": 0},
            "description": "Basic test with single patient"
        },
        {
            "name": "with_appointment",
            "params": {"practice_id": 1, "patient_id": 1001, "appointment_id": 100},
            "description": "Test with specific appointment ID"
        },
        {
            "name": "non_existent_patient",
            "params": {"practice_id": 1, "patient_id": 9999, "appointment_id": 0},
            "description": "Test with non-existent patient (should return empty)"
        },
        {
            "name": "different_practice",
            "params": {"practice_id": 2, "patient_id": 1001, "appointment_id": 0},
            "description": "Test practice isolation (should return different or no results)"
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"Running test: {test_case['name']}")
        print(f"Description: {test_case['description']}")
        
        params = test_case['params']
        practice_id = params['practice_id']
        patient_id = params['patient_id'] 
        appointment_id = params['appointment_id']
        
        # SQL Server query
        sql_server_query = f"EXEC dbo.SCH_GetCCDoctors @practiceId = {practice_id}, @patientId = {patient_id}, @appointmentId = {appointment_id};"
        
        # PostgreSQL query
        postgres_query = f"SELECT * FROM dbo.SCH_GetCCDoctors({practice_id}, {patient_id}, {appointment_id})"
        
        print(f"  SQL Server: {sql_server_query}")
        print(f"  PostgreSQL: {postgres_query}")
        
        test_result = {
            "test_name": test_case['name'],
            "description": test_case['description'],
            "parameters": params,
            "sql_server_query": sql_server_query,
            "postgres_query": postgres_query,
            "status": "prepared"
        }
        
        results.append(test_result)
        print(f"  Status: PREPARED")
        print()
    
    print("=" * 60)
    print("Test Summary:")
    print(f"Total test cases prepared: {len(results)}")
    print("All tests ready for execution with MCP tools")
    print()
    print("Next steps:")
    print("1. Use mcp__mssql__execute_sql for SQL Server queries")
    print("2. Use mcp__postgres__execute_sql for PostgreSQL queries")
    print("3. Compare results for data consistency")
    print()
    
    # Save results to file
    output_file = "sch_getccdoctors_test_results.json"
    with open(output_file, 'w') as f:
        json.dump({
            "test_run_date": "2025-09-11T16:53:00-07:00",
            "procedure_name": "SCH_GetCCDoctors",
            "test_cases": results,
            "status": "prepared"
        }, f, indent=2)
    
    print(f"Test cases saved to: {output_file}")
    
    return results

if __name__ == "__main__":
    test_sch_getccdoctors()