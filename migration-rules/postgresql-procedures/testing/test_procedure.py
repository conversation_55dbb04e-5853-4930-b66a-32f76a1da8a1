#!/usr/bin/env python3
"""
Stored Procedure Testing Framework
Compares results between SQL Server and PostgreSQL stored procedures
to ensure accurate migration of business logic.
"""

import json
import sys
import datetime
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from decimal import Decimal

@dataclass
class ComparisonResult:
    """Represents the result of comparing two procedure outputs"""
    procedure_name: str
    parameters: Dict[str, Any]
    sql_server_rows: int
    postgres_rows: int
    matches: bool
    differences: List[str]
    execution_time_sql: Optional[float] = None
    execution_time_postgres: Optional[float] = None

class ProcedureTestFramework:
    """Framework for testing stored procedures between SQL Server and PostgreSQL"""
    
    def __init__(self):
        self.results: List[ComparisonResult] = []
    
    def normalize_value(self, value: Any, data_type: str = None) -> Any:
        """
        Normalize values for comparison between different database systems
        """
        if value is None:
            return None
        
        # Handle datetime objects
        if isinstance(value, datetime.datetime):
            # Remove timezone info for comparison (both should be UTC)
            return value.replace(tzinfo=None).isoformat()
        
        # Handle strings
        if isinstance(value, str):
            # Trim whitespace that might differ between databases
            return value.strip()
        
        # Handle numeric types
        if isinstance(value, (int, float, Decimal)):
            # Convert to consistent type for comparison
            if data_type and 'int' in data_type.lower():
                return int(value)
            return float(value)
        
        # Handle boolean
        if isinstance(value, bool):
            return value
        
        return str(value)
    
    def normalize_column_name(self, name: str) -> str:
        """Normalize column names for comparison (case-insensitive)"""
        return name.lower().strip()
    
    def compare_rows(self, sql_server_data: List[Dict], postgres_data: List[Dict]) -> Tuple[bool, List[str]]:
        """
        Compare result sets from both databases
        Returns (matches, differences_list)
        """
        differences = []
        
        # Compare row counts
        if len(sql_server_data) != len(postgres_data):
            differences.append(f"Row count mismatch: SQL Server={len(sql_server_data)}, PostgreSQL={len(postgres_data)}")
            return False, differences
        
        if not sql_server_data:  # Both are empty
            return True, []
        
        # Normalize column names for both datasets
        sql_normalized = []
        postgres_normalized = []
        
        # Get all column names (case-insensitive)
        sql_columns = set()
        postgres_columns = set()
        
        for row in sql_server_data:
            sql_columns.update(self.normalize_column_name(k) for k in row.keys())
            sql_normalized.append({self.normalize_column_name(k): self.normalize_value(v) for k, v in row.items()})
        
        for row in postgres_data:
            postgres_columns.update(self.normalize_column_name(k) for k in row.keys())
            postgres_normalized.append({self.normalize_column_name(k): self.normalize_value(v) for k, v in row.items()})
        
        # Compare column sets
        if sql_columns != postgres_columns:
            missing_in_postgres = sql_columns - postgres_columns
            missing_in_sql = postgres_columns - sql_columns
            if missing_in_postgres:
                differences.append(f"Columns missing in PostgreSQL: {missing_in_postgres}")
            if missing_in_sql:
                differences.append(f"Columns missing in SQL Server: {missing_in_sql}")
        
        # Compare row data
        for i, (sql_row, pg_row) in enumerate(zip(sql_normalized, postgres_normalized)):
            for col in sql_columns:
                sql_val = sql_row.get(col)
                pg_val = pg_row.get(col)
                
                if sql_val != pg_val:
                    differences.append(f"Row {i+1}, Column '{col}': SQL Server='{sql_val}' vs PostgreSQL='{pg_val}'")
        
        return len(differences) == 0, differences
    
    def format_sql_server_parameters(self, procedure_name: str, params: Dict[str, Any]) -> str:
        """Format parameters for SQL Server stored procedure call"""
        if not params:
            return f"EXEC dbo.{procedure_name}"
        
        # Handle special parameter types
        formatted_params = []
        for key, value in params.items():
            if isinstance(value, list):
                # Handle table-valued parameters (like IntegerList)
                if all(isinstance(x, int) for x in value):
                    # Create a table variable for integer lists
                    table_values = ",".join(f"({x})" for x in value)
                    formatted_params.append(f"@{key} = (SELECT IntegerValue FROM (VALUES {table_values}) AS T(IntegerValue))")
                else:
                    formatted_params.append(f"@{key} = '{json.dumps(value)}'")
            elif isinstance(value, str):
                formatted_params.append(f"@{key} = '{value}'")
            elif value is None:
                formatted_params.append(f"@{key} = NULL")
            else:
                formatted_params.append(f"@{key} = {value}")
        
        return f"EXEC dbo.{procedure_name} {', '.join(formatted_params)}"
    
    def format_postgres_parameters(self, procedure_name: str, params: Dict[str, Any]) -> str:
        """Format parameters for PostgreSQL function call"""
        if not params:
            return f"SELECT * FROM dbo.{procedure_name.lower()}()"
        
        formatted_params = []
        for key, value in params.items():
            if isinstance(value, list):
                # Handle arrays
                if all(isinstance(x, int) for x in value):
                    array_str = "{" + ",".join(str(x) for x in value) + "}"
                    formatted_params.append(f"ARRAY{array_str}::INTEGER[]")
                else:
                    formatted_params.append(f"'{json.dumps(value)}'")
            elif isinstance(value, str):
                formatted_params.append(f"'{value}'")
            elif value is None:
                formatted_params.append("NULL")
            else:
                formatted_params.append(str(value))
        
        return f"SELECT * FROM dbo.{procedure_name.lower()}({', '.join(formatted_params)})"
    
    def test_procedure(self, procedure_name: str, parameters: Dict[str, Any] = None) -> ComparisonResult:
        """
        Test a single stored procedure with given parameters
        Returns comparison result
        """
        if parameters is None:
            parameters = {}
        
        result = ComparisonResult(
            procedure_name=procedure_name,
            parameters=parameters.copy(),
            sql_server_rows=0,
            postgres_rows=0,
            matches=False,
            differences=[]
        )
        
        try:
            # Execute on SQL Server
            sql_server_query = self.format_sql_server_parameters(procedure_name, parameters)
            print(f"SQL Server Query: {sql_server_query}")
            
            # Note: In a real implementation, these would execute via MCP
            # For now, we'll create a template that shows how it would work
            sql_server_data = []  # This would be: mcp__mssql__execute_sql(sql_server_query)
            
            # Execute on PostgreSQL
            postgres_query = self.format_postgres_parameters(procedure_name, parameters)
            print(f"PostgreSQL Query: {postgres_query}")
            
            postgres_data = []  # This would be: mcp__postgres__execute_sql(postgres_query)
            
            result.sql_server_rows = len(sql_server_data)
            result.postgres_rows = len(postgres_data)
            
            # Compare results
            matches, differences = self.compare_rows(sql_server_data, postgres_data)
            result.matches = matches
            result.differences = differences
            
        except Exception as e:
            result.differences.append(f"Execution error: {str(e)}")
        
        self.results.append(result)
        return result
    
    def generate_report(self, output_file: str = None) -> str:
        """Generate a detailed test report"""
        report = []
        report.append("=" * 60)
        report.append("STORED PROCEDURE MIGRATION TEST REPORT")
        report.append("=" * 60)
        report.append(f"Generated: {datetime.datetime.now().isoformat()}")
        report.append(f"Total Tests: {len(self.results)}")
        
        passed = sum(1 for r in self.results if r.matches)
        failed = len(self.results) - passed
        
        report.append(f"Passed: {passed}")
        report.append(f"Failed: {failed}")
        report.append("")
        
        # Detailed results
        for result in self.results:
            report.append("-" * 40)
            report.append(f"Procedure: {result.procedure_name}")
            report.append(f"Parameters: {result.parameters}")
            report.append(f"Status: {'PASS' if result.matches else 'FAIL'}")
            report.append(f"SQL Server Rows: {result.sql_server_rows}")
            report.append(f"PostgreSQL Rows: {result.postgres_rows}")
            
            if result.differences:
                report.append("Differences:")
                for diff in result.differences:
                    report.append(f"  - {diff}")
            report.append("")
        
        report_text = "\n".join(report)
        
        if output_file:
            with open(output_file, 'w') as f:
                f.write(report_text)
        
        return report_text

# Test script for GetDaysheetCohorts
def test_get_daysheet_cohorts():
    """Test GetDaysheetCohorts procedure with various scenarios"""
    framework = ProcedureTestFramework()
    
    # Test cases
    test_cases = [
        ("Empty list", {"patient_ids": []}),
        ("Single patient", {"patient_ids": [1001]}),
        ("Multiple patients", {"patient_ids": [1001, 1002, 1003]}),
        ("Non-existent patient", {"patient_ids": [9999]}),
        ("Mixed existing and non-existing", {"patient_ids": [1001, 9999, 1002]}),
    ]
    
    print("Testing GetDaysheetCohorts procedure...")
    
    for test_name, params in test_cases:
        print(f"\nRunning test: {test_name}")
        result = framework.test_procedure("GetDaysheetCohorts", params)
        
        if result.matches:
            print(f"✅ PASS: {test_name}")
        else:
            print(f"❌ FAIL: {test_name}")
            for diff in result.differences:
                print(f"   {diff}")
    
    # Generate report
    report = framework.generate_report("getdaysheetcohorts_test_report.txt")
    print("\nTest Summary:")
    print("=" * 40)
    print(report.split('\n')[4:8])  # Show summary section
    
    return framework

if __name__ == "__main__":
    # Run the test
    test_framework = test_get_daysheet_cohorts()