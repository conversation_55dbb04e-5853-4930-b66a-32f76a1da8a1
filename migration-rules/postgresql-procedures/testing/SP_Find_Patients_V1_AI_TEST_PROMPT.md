# AI Prompt: Test and Fix SP_Find_Patients_V1 PostgreSQL Function

## Objective
Test and fix the `dbo.sp_find_patients_v1` PostgreSQL function by comparing it against the SQL Server `[dbo].[SP_Find_Patients_V1]` stored procedure to ensure identical results and behavior.

## Key Files and Locations

### PostgreSQL Function
- **File**: `/home/<USER>/source/repos/Cerebrum3-upgrade/migration-rules/postgresql-procedures/SP_Find_Patients_V1.sql`
- **Function**: `dbo.SP_Find_Patients_V1(...)`

### SQL Server Stored Procedure  
- **File**: `/home/<USER>/source/repos/Cerebrum3-upgrade/original-source/C3.DbMigrator/StoredProcedures/SP_Find_Patients.sql`
- **Procedure**: `[dbo].[SP_Find_Patients_V1]`

### Repository Implementation
- **Interface**: `/home/<USER>/source/repos/Cerebrum3-upgrade/target-source/Cerebrum.Data/Repositories/IPatientRepository.cs`
- **Implementation**: `/home/<USER>/source/repos/Cerebrum3-upgrade/target-source/Cerebrum.Data/Repositories/PatientRepository.cs`
- **Method**: `SearchPatients()` and `SearchPatientsPostgreSql()`/`SearchPatientsSqlServer()`

### Dual Database Guide
- **File**: `/home/<USER>/source/repos/Cerebrum3-upgrade/migration-rules/dual-database-support-guide.md`

## Task Steps

### 1. Create Test Script (PRIORITY)

Create a comprehensive test script following the existing pattern:
- **File**: `test_sp_find_patients_v1.py` 
- **Pattern**: Follow `/home/<USER>/source/repos/Cerebrum3-upgrade/migration-rules/postgresql-procedures/testing/test_getpatientinfo.py`

#### Test Cases to Include:

```python
test_cases = [
    {
        "name": "ohip_search_exact",
        "practice_id": 1,
        "ohip": "**********", 
        "description": "OHIP number search - exact match"
    },
    {
        "name": "ohip_search_partial",
        "practice_id": 1,
        "ohip": "1234",
        "description": "OHIP number search - partial match"
    },
    {
        "name": "name_search_both",
        "practice_id": 1,
        "last_name": "Smith",
        "first_name": "John",
        "description": "Search by both first and last name"
    },
    {
        "name": "name_search_lastname_only",
        "practice_id": 1,
        "last_name": "Smith",
        "description": "Search by last name only"
    },
    {
        "name": "phone_search",
        "practice_id": 1,
        "phone_number": "************",
        "description": "Search by phone number"
    },
    {
        "name": "dob_search_exact",
        "practice_id": 1,
        "date_of_birth": "1980-05-15",
        "description": "Search by exact date of birth"
    },
    {
        "name": "year_of_birth",
        "practice_id": 1,
        "year_of_birth": 1980,
        "description": "Search by year of birth"
    },
    {
        "name": "patient_id_search",
        "practice_id": 1,
        "patient_id": 1002,
        "description": "Search by patient ID"
    },
    {
        "name": "active_patients_only",
        "practice_id": 1,
        "last_name": "Smith",
        "active": 1,
        "description": "Search active patients only"
    },
    {
        "name": "inactive_patients",
        "practice_id": 1,
        "last_name": "Smith", 
        "active": 0,
        "description": "Search inactive patients"
    },
    {
        "name": "top_results_limit",
        "practice_id": 1,
        "last_name": "Smith",
        "top_result": 5,
        "description": "Limit results to top 5"
    },
    {
        "name": "no_criteria",
        "practice_id": 1,
        "description": "No search criteria - should return empty"
    },
    {
        "name": "non_existent_patient",
        "practice_id": 1,
        "last_name": "NonExistentPatient999",
        "description": "Search for non-existent patient"
    }
]
```

### 2. Execute Database Tests Using MCP Servers

Use the available MCP servers to test both databases:

#### PostgreSQL Queries:
```sql
-- Example OHIP search
SELECT * FROM dbo.SP_Find_Patients_V1(
    p_practice_id := 1,
    p_ohip := '**********',
    p_top_result := 20
);

-- Example name search  
SELECT * FROM dbo.SP_Find_Patients_V1(
    p_practice_id := 1,
    p_last_name := 'Smith',
    p_first_name := 'John',
    p_top_result := 20
);
```

#### SQL Server Queries:
```sql
-- Example OHIP search
EXEC [dbo].[SP_Find_Patients_V1] 
    @PracticeId = 1,
    @OHIP = '**********',
    @TOPResult = 20;

-- Example name search
EXEC [dbo].[SP_Find_Patients_V1]
    @PracticeId = 1, 
    @LastName = 'Smith',
    @FirstName = 'John',
    @TOPResult = 20;
```

### 3. Compare Results and Identify Issues

#### Expected Result Structure:
```json
{
  "PatientId": 1002,
  "PatientFhirId": "uuid-here",
  "FirstName": "John", 
  "LastName": "Smith",
  "MiddleName": "",
  "DateOfBirth": "1980-05-15T00:00:00",
  "Gender": 1,
  "PracticeId": 1,
  "DefaultPaymentMethod": 0,
  "OHIP": "**********",
  "OHIPVersionCode": "AB",
  "LastReferringDoctorId": 0,
  "Active": 1
}
```

#### Common Issues to Check:
1. **OHIP Health Card Handling**: SQL Server concatenates number-version, PostgreSQL separates
2. **Name Search Logic**: SQL Server uses CONTAINS with full-text search, PostgreSQL uses ILIKE
3. **Phone Number Cleaning**: Both should strip special characters
4. **Date Handling**: Time zone differences
5. **Result Ordering**: Should match SQL Server ordering
6. **Parameter Handling**: NULL vs empty string differences

### 4. Fix PostgreSQL Function

If discrepancies are found, update the PostgreSQL function:

#### Deploy Changes:
```bash
./deploy-postgres-functions.sh -f SP_Find_Patients_V1.sql
```

#### Key Areas to Fix:
1. **Full-text search simulation**: Replace CONTAINS logic with proper ILIKE patterns
2. **OHIP concatenation**: Ensure health card number and version are handled consistently
3. **Parameter validation**: Match SQL Server parameter handling
4. **Result formatting**: Ensure identical column names and types
5. **Schema references**: All table references must use `dbo.` schema prefix

### 5. Update run_all_tests.sh

Add SP_Find_Patients_V1 to the working procedures list:

```bash
# Add to WORKING_PROCEDURES array
WORKING_PROCEDURES=(
    "getdaysheetcohorts"
    "getdaysheetpreconditions" 
    "sch_getccdoctors"
    "sp_find_patients_v1"  # ADD THIS LINE
)

# Add test cases
PROCEDURE_TEST_CASES["sp_find_patients_v1"]="ohip_search:********** name_search:Smith:John phone_search:********** patient_id:1002 no_criteria:EMPTY"
```

Add execution functions:

```bash
execute_sql_server() {
    # ... existing cases ...
    "sp_find_patients_v1")
        case "$test_case" in
            "ohip_search") echo "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @OHIP = '$params', @TOPResult = 20;" ;;
            "name_search") 
                IFS=':' read -ra NAMES <<< "$params"
                echo "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @LastName = '${NAMES[0]}', @FirstName = '${NAMES[1]}', @TOPResult = 20;" ;;
            "phone_search") echo "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @PhoneNumber = '$params', @TOPResult = 20;" ;;
            "patient_id") echo "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @PatientId = $params, @TOPResult = 20;" ;;
            "no_criteria") echo "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @TOPResult = 20;" ;;
        esac
        ;;
}

execute_postgres() {
    # ... existing cases ...
    "sp_find_patients_v1")
        case "$test_case" in
            "ohip_search") echo "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_ohip := '$params', p_top_result := 20)" ;;
            "name_search")
                IFS=':' read -ra NAMES <<< "$params"  
                echo "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := '${NAMES[0]}', p_first_name := '${NAMES[1]}', p_top_result := 20)" ;;
            "phone_search") echo "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_phone_number := '$params', p_top_result := 20)" ;;
            "patient_id") echo "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_patient_id := $params, p_top_result := 20)" ;;  
            "no_criteria") echo "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_top_result := 20)" ;;
        esac
        ;;
}
```

### 6. Verify Dual Database Support

Ensure the repository implementation works correctly:

1. **Check PatientRepository.cs**: Verify `SearchPatientsPostgreSql()` method calls the function correctly
2. **Parameter mapping**: Ensure PostgreSQL parameters match SQL Server parameters
3. **Result mapping**: Verify `PatientSearchResultDto` maps correctly from both databases

## Tools Available

1. **MCP PostgreSQL**: Use `mcp__postgres__execute_sql` and `mcp__postgres__list_tables`
2. **MCP SQL Server**: Use `mcp__mssql__execute_sql` and `mcp__mssql__list_tables`
3. **Deploy Script**: `./deploy-postgres-functions.sh -f <procedure>.sql`
4. **Test Runner**: `./run_all_tests.sh`

## Success Criteria

1. ✅ PostgreSQL function returns identical results to SQL Server procedure
2. ✅ All test cases pass with consistent data
3. ✅ Function properly handles all parameter combinations
4. ✅ OHIP, name, phone, and date searches work identically  
5. ✅ Repository implementation works for both databases
6. ✅ Tests are integrated into run_all_tests.sh
7. ✅ Function follows dual-database support guidelines

## Critical Notes

- **THINK RESEARCH UNDERSTAND**: Analyze both implementations thoroughly before making changes
- **Test incrementally**: Deploy and test after each significant change
- **MCP servers have same data**: Both databases contain identical test data for comparison
- **Schema prefixes**: All PostgreSQL table references must include `dbo.` schema
- **Parameter naming**: PostgreSQL uses `p_` prefix, SQL Server doesn't require prefixes

## Expected Issues and Solutions

### Issue 1: OHIP Health Card Format
- **SQL Server**: Concatenates number + "-" + version in query
- **PostgreSQL**: Should separate number and version in result set
- **Fix**: Ensure PostgreSQL returns OHIP and OHIPVersionCode separately

### Issue 2: Full-text Search vs ILIKE
- **SQL Server**: Uses `CONTAINS((lastname, firstname), @Slastname)`
- **PostgreSQL**: Uses `ILIKE ('%' || TRIM(p_last_name) || '%')`
- **Fix**: Ensure ILIKE patterns match CONTAINS behavior

### Issue 3: Phone Number Cleaning
- **Both**: Should remove special characters (-, ), (, space)
- **Fix**: Ensure identical phone number normalization

### Issue 4: Result Ordering
- **SQL Server**: Complex ORDER BY with row_number() partitioning
- **PostgreSQL**: Simpler ordering
- **Fix**: Match SQL Server result ordering exactly

Start with creating the test script and executing database queries to understand current discrepancies before making any changes to the PostgreSQL function.