#!/usr/bin/env python3
"""
Execute PostgreSQL queries using Docker with proper error reporting
This replaces the MCP approach that doesn't show errors
"""

import subprocess
import sys
import json

def execute_postgres_query(query, container_name="postgres-migration"):
    """
    Execute a PostgreSQL query using Docker exec with proper error handling
    """
    try:
        cmd = [
            'docker', 'exec', '-i', container_name,
            'psql', '-U', 'postgres', '-d', 'c3_dev', '-c', query
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            return {
                'success': True,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'rows': parse_psql_output(result.stdout)
            }
        else:
            return {
                'success': False,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'error': result.stderr or 'Unknown error'
            }
            
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'error': 'Query timed out after 30 seconds'
        }
    except Exception as e:
        return {
            'success': False,
            'error': f'Execution failed: {str(e)}'
        }

def parse_psql_output(output):
    """
    Parse psql output to extract row count and basic structure info
    """
    lines = output.strip().split('\n')
    row_count = 0
    
    for line in lines:
        if line.startswith('(') and 'row' in line:
            # Extract row count from "(N rows)" or "(1 row)"
            try:
                row_count = int(line.split()[0][1:])
                break
            except:
                pass
    
    return {
        'row_count': row_count,
        'has_data': row_count > 0
    }

def main():
    if len(sys.argv) != 2:
        print("Usage: execute_postgres_with_errors.py '<SQL_QUERY>'")
        sys.exit(1)
    
    query = sys.argv[1]
    result = execute_postgres_query(query)
    
    # Output as JSON for consumption by test scripts
    print(json.dumps(result, indent=2))
    
    # Exit with appropriate code
    sys.exit(0 if result['success'] else 1)

if __name__ == "__main__":
    main()