#!/usr/bin/env python3
"""
Test script specifically for GetDaysheetCohorts procedure
This script will be executed by the shell script that has access to MCP tools
"""

import json
import sys
from datetime import datetime

def compare_results(sql_server_result, postgres_result, test_name):
    """Compare results from both databases"""
    print(f"\n--- Testing: {test_name} ---")
    
    # Extract data from results - assuming they're JSON strings or dicts
    if isinstance(sql_server_result, str):
        try:
            sql_data = json.loads(sql_server_result) if sql_server_result.strip() else []
        except:
            sql_data = []
    else:
        sql_data = sql_server_result if sql_server_result else []
    
    if isinstance(postgres_result, str):
        try:
            pg_data = json.loads(postgres_result) if postgres_result.strip() else []
        except:
            pg_data = []
    else:
        pg_data = postgres_result if postgres_result else []
    
    # Convert to lists if they're single objects
    if isinstance(sql_data, dict):
        sql_data = [sql_data]
    if isinstance(pg_data, dict):
        pg_data = [pg_data]
    
    print(f"SQL Server returned: {len(sql_data)} rows")
    print(f"PostgreSQL returned: {len(pg_data)} rows")
    
    if len(sql_data) != len(pg_data):
        print(f"❌ FAIL: Row count mismatch")
        return False
    
    if len(sql_data) == 0:
        print(f"✅ PASS: Both returned empty results")
        return True
    
    # Compare first row structure (assuming all rows have same structure)
    sql_keys = set(k.lower() for k in sql_data[0].keys())
    pg_keys = set(k.lower() for k in pg_data[0].keys())
    
    if sql_keys != pg_keys:
        print(f"❌ FAIL: Column mismatch")
        print(f"  SQL Server columns: {sorted(sql_keys)}")
        print(f"  PostgreSQL columns: {sorted(pg_keys)}")
        return False
    
    # Compare data (simplified comparison)
    for i, (sql_row, pg_row) in enumerate(zip(sql_data, pg_data)):
        for col in sql_keys:
            sql_val = next((v for k, v in sql_row.items() if k.lower() == col), None)
            pg_val = next((v for k, v in pg_row.items() if k.lower() == col), None)
            
            # Normalize values for comparison
            if sql_val != pg_val:
                # Try string comparison for datetime/text fields
                if str(sql_val).strip() != str(pg_val).strip():
                    print(f"❌ FAIL: Row {i+1}, Column '{col}': '{sql_val}' vs '{pg_val}'")
                    return False
    
    print(f"✅ PASS: All data matches")
    return True

def main():
    """Main test function - expects results to be passed as command line arguments"""
    if len(sys.argv) != 4:
        print("Usage: python3 test_get_daysheet_cohorts.py <test_name> <sql_server_result> <postgres_result>")
        sys.exit(1)
    
    test_name = sys.argv[1]
    sql_server_result = sys.argv[2]
    postgres_result = sys.argv[3]
    
    # Decode results if they're base64 or need special handling
    try:
        success = compare_results(sql_server_result, postgres_result, test_name)
        return 0 if success else 1
    except Exception as e:
        print(f"❌ ERROR in {test_name}: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())