# PostgreSQL Procedure Validation Guide

Quick guide for validating migrated PostgreSQL functions using the new streamlined tools.

## Tools Available

- `./run_test.sh <function_name>` - Test individual function
- `./run_test.sh <function_name> --verbose` - Detailed output with SQL queries and differences  
- `./run_test.sh --all` - Test all configured functions
- `execute_real_database_test.py` - Direct real database testing (used by run_test.sh)

## Validation Process

### 1. Initial Test
```bash
./run_test.sh <function_name>
```

**Possible Results:**
- ✅ **PASS**: Function works correctly
- ❌ **FAIL**: Function has errors or differences  
- ⚠️ **UNKNOWN**: Parsing or execution issues

### 2. If PASS - Done!
Function is working correctly. Update checklist in `conversion-logs/add-postgres-tests.md`:

```markdown
- [x] ✅ `functionname|ProcedureName|test_cases` - **VALIDATED** (YYYY-MM-DD) - Brief description
```

### 3. If FAIL - Debug with <PERSON><PERSON><PERSON><PERSON>
```bash
./run_test.sh <function_name> --verbose
```

This shows:
- Actual SQL Server and PostgreSQL queries
- Specific data differences
- Detailed error messages

### 4. Common Issues & Fixes

#### Data Type Mismatches
**Problem**: `timestamp without time zone` vs `timestamp with time zone`
```sql
-- Fix: Match actual table column types
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_schema = 'dbo' AND table_name = 'yourtable';

-- Update function RETURNS TABLE to match exactly
```

**Problem**: Boolean vs integer comparisons
```sql
-- Wrong: rr.status = 1
-- Right: rr.status = true
```

#### Function Doesn't Exist
```bash
# Check if function exists
docker exec -i postgres-migration psql -U postgres -d c3_dev -c "SELECT proname FROM pg_proc WHERE proname = 'yourfunction';"

# Deploy function using the deployment script
../deploy-postgres-functions.sh -f YourFunction.sql
```

#### Acceptable vs Real Differences

**Acceptable (formatting only):**
- `2024-01-15 09:00:00.0000000` vs `2024-01-15 09:00:00+00` (same timestamp, different format)
- `NULL` vs `||` (both represent null values in output)
- `0` vs `f` (boolean false representation differences)
- `true` vs `t` (boolean true representation differences)  
- JSON spacing differences: `{"Id":1}` vs `{"Id": 1}` (same data, different formatting)
- Minor whitespace differences in string concatenation

**Real Issues (need fixing):**
- Different row counts
- Different actual data values (not just formatting)
- Function errors/exceptions
- Missing/extra columns with different data
- Logic errors producing wrong results

### 5. Deploy Fixes

```bash
# Deploy updated function using the deployment script
../deploy-postgres-functions.sh YourFunction.sql

# Test again
./run_test.sh <function_name>
```

### 6. Update Documentation

When validated successfully, update:

1. **`conversion-logs/add-postgres-tests.md`** - Mark as validated
2. **`execute_real_database_test.py`** - Add to PASS list if needed for automated testing

## Example: getdaysheetcohorts Fix

**Issue Found**: 
```
Data differences found in 2 rows
Row 1: SQL Server 'timestamp.0000000' vs PostgreSQL 'timestamp+00'
```

**Diagnosis**: Function returned `timestamp without time zone` but table has `timestamp with time zone`

**Fix**: Updated function return type to match table:
```sql
-- Before
started timestamp without time zone,

-- After  
started timestamp with time zone,
```

**Result**: ✅ Function now works (formatting differences are acceptable)

## Tips

- Use `--verbose` to see exact differences
- Always check actual table data types with `information_schema.columns`
- Focus on fixing real functional issues, not just formatting differences
- Test both valid data and edge cases (non-existent IDs)
- Update checklists promptly to track progress

## When to Accept "Differences"

**Accept these formatting differences:**
- Timestamp precision/timezone notation
- NULL representation in output  
- Column name casing (if data is same)

**Don't accept these real differences:**
- Different row counts
- Different actual data values
- Errors or exceptions
- Missing/extra columns with different data

The goal is functional equivalence, not identical string formatting.

## Interpreting Test Results

When `./run_test.sh` reports **❌ FAIL**, use `--verbose` to see the actual differences:

```bash
./run_test.sh <function_name> --verbose
```

**If differences are only formatting (see acceptable list above):**
- ✅ **Consider it VALIDATED** - Function works correctly  
- Update checklists to mark as validated
- No code changes needed

**If differences are real functional issues:**
- ❌ **Needs fixing** - Debug and fix the function
- Use the troubleshooting steps above