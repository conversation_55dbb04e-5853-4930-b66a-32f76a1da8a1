=========================================
POSTGRESQL PROCEDURE TESTING
Started: Fri 12 Sep 2025 04:45:41 PM PDT
=========================================
Running tests for procedure: getauditlogdata
=========================================
Testing procedure: getauditlogdata
=========================================
  Running test: audit_logdata (params: 1:10:2024-01-01:2024-12-31)
    SQL Server: EXEC [dbo].[GetAuditLogData] @page = 1, @pagesize = 10, @startdate = '2024-01-01', @enddate = '2024-12-31';
    PostgreSQL: SELECT * FROM dbo.getauditlogdata(1, 10, '2024-01-01', '2024-12-31')
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: PostgreSQL failed: ERROR:  relation "dbo.demographics" does not exist
LINE 13:     LEFT JOIN dbo.demographics d ON a.patientrecordid = d.pa...
                       ^
QUERY:  SELECT 
        a.userid::integer,
        COALESCE(u.lastname || ', ' || u.firstname, u.email, '')::varchar(200) as username,
        a.ipaddress::varchar(50),
        a.eventtype::varchar(50),
        a.eventdatetime AT TIME ZONE 'UTC',
        a.tablename::varchar(100),
        a.patientrecordid::integer,
        COALESCE(d.lastname || ', ' || d.firstname, '')::varchar(200) as patientfullname,
        a.changes
    FROM dbo.audits a
    LEFT JOIN dbo.usersyncs u ON a.userid = u.userid
    LEFT JOIN dbo.demographics d ON a.patientrecordid = d.patientrecordid
    LEFT JOIN LATERAL (
        SELECT 
            jsonb_array_elements(
                CASE 
                    WHEN a.changes IS NOT NULL AND jsonb_typeof(a.changes::jsonb) = 'object' 
                    THEN (a.changes::jsonb -> 'changes')
                    ELSE '[]'::jsonb
                END
            ) AS change_item
    ) changes_array ON (p_text_search IS NOT NULL AND TRIM(p_text_search) != '')
    WHERE a.eventdatetime >= p_startdate 
      AND a.eventdatetime <= p_enddate
      -- Optional user ID filter
      AND (p_user_id IS NULL OR p_user_id <= 0 OR a.userid = p_user_id)
      -- Optional patient record ID filter
      AND (p_patient_record_id IS NULL OR p_patient_record_id <= 0 OR a.patientrecordid = p_patient_record_id)
      -- Optional IP address filter
      AND (p_ip_address IS NULL OR TRIM(p_ip_address) = '' OR a.ipaddress ILIKE '%' || TRIM(p_ip_address) || '%')
      -- Optional text search in JSON changes
      AND (p_text_search IS NULL OR TRIM(p_text_search) = '' OR (
          a.changes IS NOT NULL
          AND a.changes != ''
          AND jsonb_typeof(a.changes::jsonb) = 'object'
          AND (a.changes::jsonb -> 'changes') IS NOT NULL
          AND jsonb_typeof(a.changes::jsonb -> 'changes') = 'array'
          AND (
              (changes_array.change_item ->> 'OV') ILIKE '%' || TRIM(p_text_search) || '%'
              OR (changes_array.change_item ->> 'NV') ILIKE '%' || TRIM(p_text_search) || '%'
          )
      ))
    ORDER BY a.eventdatetime DESC
    OFFSET v_offset
    LIMIT v_limit
CONTEXT:  PL/pgSQL function dbo.getauditlogdata(integer,integer,timestamp without time zone,timestamp without time zone,integer,integer,character varying,character varying) line 15 at RETURN QUERY
  Running test: no_logdata (params: 1:10:2025-01-01:2025-01-31)
    SQL Server: EXEC [dbo].[GetAuditLogData] @page = 1, @pagesize = 10, @startdate = '2025-01-01', @enddate = '2025-01-31';
    PostgreSQL: SELECT * FROM dbo.getauditlogdata(1, 10, '2025-01-01', '2025-01-31')
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: PostgreSQL failed: ERROR:  relation "dbo.demographics" does not exist
LINE 13:     LEFT JOIN dbo.demographics d ON a.patientrecordid = d.pa...
                       ^
QUERY:  SELECT 
        a.userid::integer,
        COALESCE(u.lastname || ', ' || u.firstname, u.email, '')::varchar(200) as username,
        a.ipaddress::varchar(50),
        a.eventtype::varchar(50),
        a.eventdatetime AT TIME ZONE 'UTC',
        a.tablename::varchar(100),
        a.patientrecordid::integer,
        COALESCE(d.lastname || ', ' || d.firstname, '')::varchar(200) as patientfullname,
        a.changes
    FROM dbo.audits a
    LEFT JOIN dbo.usersyncs u ON a.userid = u.userid
    LEFT JOIN dbo.demographics d ON a.patientrecordid = d.patientrecordid
    LEFT JOIN LATERAL (
        SELECT 
            jsonb_array_elements(
                CASE 
                    WHEN a.changes IS NOT NULL AND jsonb_typeof(a.changes::jsonb) = 'object' 
                    THEN (a.changes::jsonb -> 'changes')
                    ELSE '[]'::jsonb
                END
            ) AS change_item
    ) changes_array ON (p_text_search IS NOT NULL AND TRIM(p_text_search) != '')
    WHERE a.eventdatetime >= p_startdate 
      AND a.eventdatetime <= p_enddate
      -- Optional user ID filter
      AND (p_user_id IS NULL OR p_user_id <= 0 OR a.userid = p_user_id)
      -- Optional patient record ID filter
      AND (p_patient_record_id IS NULL OR p_patient_record_id <= 0 OR a.patientrecordid = p_patient_record_id)
      -- Optional IP address filter
      AND (p_ip_address IS NULL OR TRIM(p_ip_address) = '' OR a.ipaddress ILIKE '%' || TRIM(p_ip_address) || '%')
      -- Optional text search in JSON changes
      AND (p_text_search IS NULL OR TRIM(p_text_search) = '' OR (
          a.changes IS NOT NULL
          AND a.changes != ''
          AND jsonb_typeof(a.changes::jsonb) = 'object'
          AND (a.changes::jsonb -> 'changes') IS NOT NULL
          AND jsonb_typeof(a.changes::jsonb -> 'changes') = 'array'
          AND (
              (changes_array.change_item ->> 'OV') ILIKE '%' || TRIM(p_text_search) || '%'
              OR (changes_array.change_item ->> 'NV') ILIKE '%' || TRIM(p_text_search) || '%'
          )
      ))
    ORDER BY a.eventdatetime DESC
    OFFSET v_offset
    LIMIT v_limit
CONTEXT:  PL/pgSQL function dbo.getauditlogdata(integer,integer,timestamp without time zone,timestamp without time zone,integer,integer,character varying,character varying) line 15 at RETURN QUERY

  Procedure getauditlogdata summary: 0 passed, 2 failed (out of 2 tests)

