=========================================
POSTGRESQL PROCEDURE TESTING
Started: Fri 12 Sep 2025 05:28:45 PM PDT
=========================================
Running tests for all procedures...
=========================================
Testing procedure: getdaysheetcohorts
=========================================
  Running test: empty_list (params: EMPTY)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: single_patient (params: 1001)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: multiple_patients (params: 1001,1002,1003)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: non_existent (params: 9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getdaysheetcohorts summary: 4 passed, 0 failed (out of 4 tests)

=========================================
Testing procedure: getdaysheetpreconditions
=========================================
  Running test: empty_list (params: EMPTY)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: single_appointment (params: 1)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: multiple_appointments (params: 1,2,3)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: non_existent (params: 9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getdaysheetpreconditions summary: 4 passed, 0 failed (out of 4 tests)

=========================================
Testing procedure: sch_getccdoctors
=========================================
  Running test: single_patient (params: 1:16)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: multiple_params (params: 1:16:100)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: non_existent_patient (params: 1:9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: zero_appointment (params: 1:16:0)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure sch_getccdoctors summary: 4 passed, 0 failed (out of 4 tests)

=========================================
Testing procedure: sp_find_patients_v1
=========================================
  Running test: ohip_search (params: **********)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: name_search (params: PTLastOne)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: patient_id (params: 2)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure sp_find_patients_v1 summary: 3 passed, 0 failed (out of 3 tests)

=========================================
Testing procedure: getusermenucount
=========================================
  Running test: user_with_menu (params: 1)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: user_no_menu (params: 9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getusermenucount summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: getpracticescheduleduserweekdays
=========================================
  Running test: user_schedule (params: 1:1)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: no_schedule (params: 1:9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getpracticescheduleduserweekdays summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: sp_get_demographicenrolment
=========================================
  Running test: patient_enrolment (params: 2)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: Column count mismatch: SQL Server has 12 columns, PostgreSQL has 11 columns
  Running test: no_enrolment (params: 9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: Column count mismatch: SQL Server has 12 columns, PostgreSQL has 11 columns

  Procedure sp_get_demographicenrolment summary: 0 passed, 2 failed (out of 2 tests)

=========================================
Testing procedure: sp_getpatientdemographicinfo
=========================================
  Running test: valid_patient (params: 1:2)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: invalid_patient (params: 1:9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure sp_getpatientdemographicinfo summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: getpatientlocations
=========================================
  Running test: valid_patient (params: 2)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: valid_patient_with_doctor (params: 2:1)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: invalid_patient (params: 9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getpatientlocations summary: 3 passed, 0 failed (out of 3 tests)

=========================================
Testing procedure: getmaindoctorinfo
=========================================
  Running test: valid_patient (params: 2)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: invalid_patient (params: 9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getmaindoctorinfo summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: getpracticepatientinfo
=========================================
  Running test: valid_patient (params: 1:2)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: invalid_patient (params: 1:9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getpracticepatientinfo summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: getuserpermissions
=========================================
  Running test: valid_user (params: 1)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: invalid_user (params: 9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getuserpermissions summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: getuserroles
=========================================
  Running test: user_with_roles (params: 1)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: user_no_roles (params: 9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getuserroles summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: getuseroffices
=========================================
  Running test: user_with_offices (params: 1:1:false)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: user_no_offices (params: 9999:1:false)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getuseroffices summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: getpracticescheduledusers
=========================================
  Running test: practice_with_users (params: 1)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: practice_no_users (params: 9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getpracticescheduledusers summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: getappointmentreminders
=========================================
  Running test: email_reminders (params: emailreminder:1)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: Column name/schema mismatch: 1 differences found
  Running test: voice_reminders (params: voicereminder:1)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: Column name/schema mismatch: 1 differences found
  Running test: text_reminders (params: textreminder:1)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: Column name/schema mismatch: 1 differences found

  Procedure getappointmentreminders summary: 0 passed, 3 failed (out of 3 tests)

=========================================
Testing procedure: getinventoryitems
=========================================
  Running test: practice_items (params: 1)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: no_items (params: 9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getinventoryitems summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: getinventoryitem
=========================================
  Running test: single_item (params: 1:1)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: invalid_item (params: 1:9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getinventoryitem summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: getinventoryitemhistory
=========================================
  Running test: item_history (params: 1)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: Column name/schema mismatch: 1 differences found
  Running test: no_history (params: 9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: Column name/schema mismatch: 1 differences found

  Procedure getinventoryitemhistory summary: 0 passed, 2 failed (out of 2 tests)

=========================================
Testing procedure: getinventoryoverdue
=========================================
  Running test: overdue_items (params: 1:1:2024-01-01)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: no_overdue (params: 1:1:2025-01-01)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: Row count mismatch: SQL Server 0, PostgreSQL 1

  Procedure getinventoryoverdue summary: 1 passed, 1 failed (out of 2 tests)

=========================================
Testing procedure: getaudit
=========================================
  Running test: get_audit (params: 2024-01-01:2024-12-31)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: PostgreSQL failed: ERROR:  relation "dbo.aspnetusers" does not exist
LINE 12:     JOIN dbo.aspnetusers u ON u.userid = l.userid
                  ^
QUERY:  SELECT 
        l.id,
        l.userid,
        u.email as username,
        l.ipaddress,
        l.eventtype,
        l.eventdatetime,
        l.tablename,
        l.patientrecordid,
        l.changes
    FROM dbo.audits l
    JOIN dbo.aspnetusers u ON u.userid = l.userid
    LEFT JOIN LATERAL (
        SELECT 
            jsonb_array_elements(
                CASE 
                    WHEN l.changes IS NOT NULL AND jsonb_typeof(l.changes::jsonb) = 'object' 
                    THEN (l.changes::jsonb -> 'changes')
                    ELSE '[]'::jsonb
                END
            ) AS change_item
    ) changes_array ON (p_content IS NOT NULL AND p_content != '')
    WHERE l.eventdatetime > p_from_date 
      AND l.eventdatetime < p_to_date
      -- Optional IP address filter
      AND (p_ip_address IS NULL OR TRIM(p_ip_address) = '' OR l.ipaddress ILIKE '%' || TRIM(p_ip_address) || '%')
      -- Optional user ID filter
      AND (p_user_id IS NULL OR p_user_id <= 0 OR l.userid = p_user_id)
      -- Optional patient record ID filter
      AND (p_patient_record_id IS NULL OR p_patient_record_id <= 0 OR l.patientrecordid = p_patient_record_id)
      -- Optional content search in JSON changes
      AND (p_content IS NULL OR TRIM(p_content) = '' OR (
          l.changes IS NOT NULL
          AND l.changes != ''
          AND jsonb_typeof(l.changes::jsonb) = 'object'
          AND (l.changes::jsonb -> 'changes') IS NOT NULL
          AND jsonb_typeof(l.changes::jsonb -> 'changes') = 'array'
          AND (
              (changes_array.change_item ->> 'OV') ILIKE '%' || TRIM(p_content) || '%'
              OR (changes_array.change_item ->> 'NV') ILIKE '%' || TRIM(p_content) || '%'
          )
      ))
    ORDER BY l.eventdatetime
CONTEXT:  PL/pgSQL function dbo.getaudit(timestamp without time zone,timestamp without time zone,character varying,integer,integer,character varying) line 3 at RETURN QUERY
  Running test: no_get_audit (params: 2025-01-01:2025-01-31)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: PostgreSQL failed: ERROR:  relation "dbo.aspnetusers" does not exist
LINE 12:     JOIN dbo.aspnetusers u ON u.userid = l.userid
                  ^
QUERY:  SELECT 
        l.id,
        l.userid,
        u.email as username,
        l.ipaddress,
        l.eventtype,
        l.eventdatetime,
        l.tablename,
        l.patientrecordid,
        l.changes
    FROM dbo.audits l
    JOIN dbo.aspnetusers u ON u.userid = l.userid
    LEFT JOIN LATERAL (
        SELECT 
            jsonb_array_elements(
                CASE 
                    WHEN l.changes IS NOT NULL AND jsonb_typeof(l.changes::jsonb) = 'object' 
                    THEN (l.changes::jsonb -> 'changes')
                    ELSE '[]'::jsonb
                END
            ) AS change_item
    ) changes_array ON (p_content IS NOT NULL AND p_content != '')
    WHERE l.eventdatetime > p_from_date 
      AND l.eventdatetime < p_to_date
      -- Optional IP address filter
      AND (p_ip_address IS NULL OR TRIM(p_ip_address) = '' OR l.ipaddress ILIKE '%' || TRIM(p_ip_address) || '%')
      -- Optional user ID filter
      AND (p_user_id IS NULL OR p_user_id <= 0 OR l.userid = p_user_id)
      -- Optional patient record ID filter
      AND (p_patient_record_id IS NULL OR p_patient_record_id <= 0 OR l.patientrecordid = p_patient_record_id)
      -- Optional content search in JSON changes
      AND (p_content IS NULL OR TRIM(p_content) = '' OR (
          l.changes IS NOT NULL
          AND l.changes != ''
          AND jsonb_typeof(l.changes::jsonb) = 'object'
          AND (l.changes::jsonb -> 'changes') IS NOT NULL
          AND jsonb_typeof(l.changes::jsonb -> 'changes') = 'array'
          AND (
              (changes_array.change_item ->> 'OV') ILIKE '%' || TRIM(p_content) || '%'
              OR (changes_array.change_item ->> 'NV') ILIKE '%' || TRIM(p_content) || '%'
          )
      ))
    ORDER BY l.eventdatetime
CONTEXT:  PL/pgSQL function dbo.getaudit(timestamp without time zone,timestamp without time zone,character varying,integer,integer,character varying) line 3 at RETURN QUERY

  Procedure getaudit summary: 0 passed, 2 failed (out of 2 tests)

=========================================
Testing procedure: getauditlogdata
=========================================
  Running test: audit_logdata (params: 1:10:2024-01-01:2024-12-31)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: PostgreSQL failed: ERROR:  relation "dbo.demographics" does not exist
LINE 13:     LEFT JOIN dbo.demographics d ON a.patientrecordid = d.pa...
                       ^
QUERY:  SELECT 
        a.userid::integer,
        COALESCE(u.lastname || ', ' || u.firstname, u.email, '')::varchar(200) as username,
        a.ipaddress::varchar(50),
        a.eventtype::varchar(50),
        a.eventdatetime AT TIME ZONE 'UTC',
        a.tablename::varchar(100),
        a.patientrecordid::integer,
        COALESCE(d.lastname || ', ' || d.firstname, '')::varchar(200) as patientfullname,
        a.changes
    FROM dbo.audits a
    LEFT JOIN dbo.usersyncs u ON a.userid = u.userid
    LEFT JOIN dbo.demographics d ON a.patientrecordid = d.patientrecordid
    LEFT JOIN LATERAL (
        SELECT 
            jsonb_array_elements(
                CASE 
                    WHEN a.changes IS NOT NULL AND jsonb_typeof(a.changes::jsonb) = 'object' 
                    THEN (a.changes::jsonb -> 'changes')
                    ELSE '[]'::jsonb
                END
            ) AS change_item
    ) changes_array ON (p_text_search IS NOT NULL AND TRIM(p_text_search) != '')
    WHERE a.eventdatetime >= p_startdate 
      AND a.eventdatetime <= p_enddate
      -- Optional user ID filter
      AND (p_user_id IS NULL OR p_user_id <= 0 OR a.userid = p_user_id)
      -- Optional patient record ID filter
      AND (p_patient_record_id IS NULL OR p_patient_record_id <= 0 OR a.patientrecordid = p_patient_record_id)
      -- Optional IP address filter
      AND (p_ip_address IS NULL OR TRIM(p_ip_address) = '' OR a.ipaddress ILIKE '%' || TRIM(p_ip_address) || '%')
      -- Optional text search in JSON changes
      AND (p_text_search IS NULL OR TRIM(p_text_search) = '' OR (
          a.changes IS NOT NULL
          AND a.changes != ''
          AND jsonb_typeof(a.changes::jsonb) = 'object'
          AND (a.changes::jsonb -> 'changes') IS NOT NULL
          AND jsonb_typeof(a.changes::jsonb -> 'changes') = 'array'
          AND (
              (changes_array.change_item ->> 'OV') ILIKE '%' || TRIM(p_text_search) || '%'
              OR (changes_array.change_item ->> 'NV') ILIKE '%' || TRIM(p_text_search) || '%'
          )
      ))
    ORDER BY a.eventdatetime DESC
    OFFSET v_offset
    LIMIT v_limit
CONTEXT:  PL/pgSQL function dbo.getauditlogdata(integer,integer,timestamp without time zone,timestamp without time zone,integer,integer,character varying,character varying) line 15 at RETURN QUERY
  Running test: no_logdata (params: 1:10:2025-01-01:2025-01-31)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: PostgreSQL failed: ERROR:  relation "dbo.demographics" does not exist
LINE 13:     LEFT JOIN dbo.demographics d ON a.patientrecordid = d.pa...
                       ^
QUERY:  SELECT 
        a.userid::integer,
        COALESCE(u.lastname || ', ' || u.firstname, u.email, '')::varchar(200) as username,
        a.ipaddress::varchar(50),
        a.eventtype::varchar(50),
        a.eventdatetime AT TIME ZONE 'UTC',
        a.tablename::varchar(100),
        a.patientrecordid::integer,
        COALESCE(d.lastname || ', ' || d.firstname, '')::varchar(200) as patientfullname,
        a.changes
    FROM dbo.audits a
    LEFT JOIN dbo.usersyncs u ON a.userid = u.userid
    LEFT JOIN dbo.demographics d ON a.patientrecordid = d.patientrecordid
    LEFT JOIN LATERAL (
        SELECT 
            jsonb_array_elements(
                CASE 
                    WHEN a.changes IS NOT NULL AND jsonb_typeof(a.changes::jsonb) = 'object' 
                    THEN (a.changes::jsonb -> 'changes')
                    ELSE '[]'::jsonb
                END
            ) AS change_item
    ) changes_array ON (p_text_search IS NOT NULL AND TRIM(p_text_search) != '')
    WHERE a.eventdatetime >= p_startdate 
      AND a.eventdatetime <= p_enddate
      -- Optional user ID filter
      AND (p_user_id IS NULL OR p_user_id <= 0 OR a.userid = p_user_id)
      -- Optional patient record ID filter
      AND (p_patient_record_id IS NULL OR p_patient_record_id <= 0 OR a.patientrecordid = p_patient_record_id)
      -- Optional IP address filter
      AND (p_ip_address IS NULL OR TRIM(p_ip_address) = '' OR a.ipaddress ILIKE '%' || TRIM(p_ip_address) || '%')
      -- Optional text search in JSON changes
      AND (p_text_search IS NULL OR TRIM(p_text_search) = '' OR (
          a.changes IS NOT NULL
          AND a.changes != ''
          AND jsonb_typeof(a.changes::jsonb) = 'object'
          AND (a.changes::jsonb -> 'changes') IS NOT NULL
          AND jsonb_typeof(a.changes::jsonb -> 'changes') = 'array'
          AND (
              (changes_array.change_item ->> 'OV') ILIKE '%' || TRIM(p_text_search) || '%'
              OR (changes_array.change_item ->> 'NV') ILIKE '%' || TRIM(p_text_search) || '%'
          )
      ))
    ORDER BY a.eventdatetime DESC
    OFFSET v_offset
    LIMIT v_limit
CONTEXT:  PL/pgSQL function dbo.getauditlogdata(integer,integer,timestamp without time zone,timestamp without time zone,integer,integer,character varying,character varying) line 15 at RETURN QUERY

  Procedure getauditlogdata summary: 0 passed, 2 failed (out of 2 tests)

=========================================
Testing procedure: getdoctorcomments
=========================================
  Running test: doctor_comments (params: 1:true)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: no_comments (params: 9999:true)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getdoctorcomments summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: getcustommeasurements
=========================================
  Running test: custom_measurements (params: 1:0)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: no_custom (params: 9999:0)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getcustommeasurements summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: getdoctorinfo
=========================================
  Running test: doctor_info (params: 1)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: invalid_doctor (params: 9999)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure getdoctorinfo summary: 2 passed, 0 failed (out of 2 tests)

=========================================
Testing procedure: sp_getpatientappointment
=========================================
  Running test: patient_appointment (params: 1:2:2024-01-01:2024-12-31)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: Column count mismatch: SQL Server has 2 columns, PostgreSQL has 17 columns
  Running test: invalid_patient (params: 9999:9999:2024-01-01:2024-12-31)
    🔄 EXECUTING REAL DATABASE TEST...
    ❌ FAIL
       ❌ REAL TEST FAILED: Column count mismatch: SQL Server has 2 columns, PostgreSQL has 17 columns

  Procedure sp_getpatientappointment summary: 0 passed, 2 failed (out of 2 tests)

=========================================
Testing procedure: sp_getpatientpreviousappointments
=========================================
  Running test: patient_history (params: 2:1:10)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS
  Running test: no_history (params: 9999:1:10)
    🔄 EXECUTING REAL DATABASE TEST...
    ✅ PASS

  Procedure sp_getpatientpreviousappointments summary: 2 passed, 0 failed (out of 2 tests)

=========================================
FINAL SUMMARY
=========================================
Procedures tested: 27
Procedures with failures: 7
Procedures fully passing: 20
=========================================
Testing completed: Fri 12 Sep 2025 05:29:19 PM PDT
=========================================
