#!/bin/bash

# Test script for GetDaysheetCohorts stored procedure
# Compares results between SQL Server and PostgreSQL implementations

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_LOG="$SCRIPT_DIR/getdaysheetcohorts_test.log"
RESULTS_FILE="$SCRIPT_DIR/getdaysheetcohorts_results.json"

# Initialize log file
echo "=======================================" > "$TEST_LOG"
echo "GetDaysheetCohorts Test Results" >> "$TEST_LOG"
echo "Started: $(date)" >> "$TEST_LOG"
echo "=======================================" >> "$TEST_LOG"

# Function to log messages
log_message() {
    echo "$1" | tee -a "$TEST_LOG"
}

# Function to execute SQL Server procedure call and return JSON results
execute_sql_server_proc() {
    local patient_ids="$1"
    local test_name="$2"
    
    log_message "Executing SQL Server GetDaysheetCohorts with: $patient_ids"
    
    # Create the SQL command based on parameter format
    if [ "$patient_ids" = "EMPTY" ]; then
        # For empty list, we need to handle the table-valued parameter correctly
        sql_cmd="DECLARE @emptyList dbo.IntegerList; EXEC dbo.GetDaysheetCohorts @patientIds = @emptyList;"
    else
        # Create a proper table-valued parameter
        values_clause=$(echo "$patient_ids" | sed 's/,/),(/g' | sed 's/^/(/; s/$/)/')
        sql_cmd="DECLARE @patientList dbo.IntegerList; INSERT INTO @patientList (IntegerValue) VALUES $values_clause; EXEC dbo.GetDaysheetCohorts @patientIds = @patientList;"
    fi
    
    echo "$sql_cmd"
}

# Function to execute PostgreSQL function call and return JSON results  
execute_postgres_func() {
    local patient_ids="$1"
    local test_name="$2"
    
    log_message "Executing PostgreSQL GetDaysheetCohorts with: $patient_ids"
    
    # Create the SQL command for PostgreSQL
    if [ "$patient_ids" = "EMPTY" ]; then
        sql_cmd="SELECT * FROM dbo.getdaysheetcohorts(ARRAY[]::INTEGER[])"
    else
        # Convert comma-separated list to PostgreSQL array format
        array_elements=$(echo "$patient_ids" | sed 's/,/, /g')
        sql_cmd="SELECT * FROM dbo.getdaysheetcohorts(ARRAY[$array_elements])"
    fi
    
    echo "$sql_cmd"
}

# Test cases - format: "test_name:patient_ids"
TEST_CASES=(
    "empty_list:EMPTY"
    "single_patient:1001" 
    "multiple_patients:1001,1002"
    "all_test_patients:1001,1002,1003,1004"
    "non_existent:9999"
    "mixed:1001,9999,1002"
)

# Track results
total_tests=0
passed_tests=0
failed_tests=0

log_message "Starting GetDaysheetCohorts procedure tests..."
echo "" >> "$TEST_LOG"

# Initialize results JSON
echo "{" > "$RESULTS_FILE"
echo "  \"test_run\": \"$(date -Iseconds)\"," >> "$RESULTS_FILE"
echo "  \"procedure\": \"GetDaysheetCohorts\"," >> "$RESULTS_FILE"
echo "  \"results\": [" >> "$RESULTS_FILE"

first_test=true

# Run each test case
for test_case in "${TEST_CASES[@]}"; do
    IFS=':' read -r test_name patient_ids <<< "$test_case"
    
    log_message "Running test: $test_name"
    total_tests=$((total_tests + 1))
    
    # Execute SQL Server version
    sql_server_query=$(execute_sql_server_proc "$patient_ids" "$test_name")
    log_message "SQL Server query: $sql_server_query"
    
    # Execute PostgreSQL version
    postgres_query=$(execute_postgres_func "$patient_ids" "$test_name")
    log_message "PostgreSQL query: $postgres_query"
    
    # For now, we'll just log the queries that would be executed
    # In a real implementation, these would call the MCP functions
    
    log_message "Test case prepared: $test_name"
    
    # Add to results JSON
    if [ "$first_test" = false ]; then
        echo "    }," >> "$RESULTS_FILE"
    fi
    first_test=false
    
    echo "    {" >> "$RESULTS_FILE"
    echo "      \"test_name\": \"$test_name\"," >> "$RESULTS_FILE"
    echo "      \"parameters\": \"$patient_ids\"," >> "$RESULTS_FILE"
    echo "      \"sql_server_query\": \"$sql_server_query\"," >> "$RESULTS_FILE"
    echo "      \"postgres_query\": \"$postgres_query\"," >> "$RESULTS_FILE"
    echo "      \"status\": \"prepared\"" >> "$RESULTS_FILE"
    
    log_message "---"
    echo "" >> "$TEST_LOG"
done

# Close results JSON
echo "    }" >> "$RESULTS_FILE"
echo "  ]," >> "$RESULTS_FILE"
echo "  \"summary\": {" >> "$RESULTS_FILE"
echo "    \"total_tests\": $total_tests," >> "$RESULTS_FILE"
echo "    \"prepared\": $total_tests" >> "$RESULTS_FILE"
echo "  }" >> "$RESULTS_FILE"
echo "}" >> "$RESULTS_FILE"

# Final summary
log_message "======================================="
log_message "TEST PREPARATION COMPLETE"
log_message "======================================="
log_message "Total test cases prepared: $total_tests"
log_message "Results saved to: $RESULTS_FILE"
log_message "Log saved to: $TEST_LOG"

echo ""
echo "Test queries have been prepared. To execute them:"
echo "1. Review the queries in $RESULTS_FILE"
echo "2. Execute them manually using the MCP tools"
echo "3. Compare the results"

exit 0