#!/bin/bash

# Improved test runner for PostgreSQL stored procedures
# Usage: 
#   ./run_test.sh --all                    # Run all configured tests
#   ./run_test.sh <procedure_name>         # Run tests for specific procedure
#   ./run_test.sh <procedure_name> --verbose  # Run with verbose output

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROCEDURES_DIR="$(dirname "$SCRIPT_DIR")"
TEST_RESULTS_DIR="$SCRIPT_DIR/results"
MASTER_LOG="$SCRIPT_DIR/master_test_log.txt"

# Create results directory
mkdir -p "$TEST_RESULTS_DIR"

# Function to log messages
log_message() {
    echo "$1" | tee -a "$MASTER_LOG"
}

# Show usage
show_usage() {
    echo "Usage:"
    echo "  $0 --all                    # Run all configured tests"
    echo "  $0 <procedure_name>         # Run tests for specific procedure"
    echo "  $0 <procedure_name> --verbose  # Run with verbose output"
    echo ""
    echo "Examples:"
    echo "  $0 getusermenucount"
    echo "  $0 sp_get_demographicenrolment --verbose"
    echo "  $0 --all"
    echo ""
    echo "Available procedures:"
    echo "  getusermenucount, sp_get_demographicenrolment, getpracticescheduleduserweekdays"
    echo "  sch_getccdoctors, getdaysheetcohorts, getdaysheetpreconditions, sp_find_patients_v1"
    echo "  sp_getpatientdemographicinfo, getpatientlocations, getmaindoctorinfo, getpracticepatientinfo"
}

# Procedures that are actually working and can be tested
WORKING_PROCEDURES=(
    "getdaysheetcohorts"
    "getdaysheetpreconditions"
    "sch_getccdoctors"
    "sp_find_patients_v1"
    "getusermenucount"
    "getpracticescheduleduserweekdays"
    "sp_get_demographicenrolment"
    "sp_getpatientdemographicinfo"
    "getpatientlocations"
    "getmaindoctorinfo"
    "getpracticepatientinfo"
    "getuserpermissions"
    "getuserroles"
    "getuseroffices"
    "getpracticescheduledusers"
    "getappointmentreminders"
    "getinventoryitems"
    "getinventoryitem"
    "getinventoryitemhistory"
    "getinventoryoverdue"
    "getaudit"
    "getauditlogdata"
    "getdoctorcomments"
    "getcustommeasurements"
    "getdoctorinfo"
    "sp_getpatientappointment"
    "sp_getpatientpreviousappointments"
)

# Test case definitions for each procedure
declare -A PROCEDURE_TEST_CASES

# Define test cases
PROCEDURE_TEST_CASES["getdaysheetcohorts"]="empty_list:EMPTY single_patient:1001 multiple_patients:1001,1002,1003 non_existent:9999"
PROCEDURE_TEST_CASES["getdaysheetpreconditions"]="empty_list:EMPTY single_appointment:1 multiple_appointments:1,2,3 non_existent:9999"
PROCEDURE_TEST_CASES["sch_getccdoctors"]="single_patient:1:16 multiple_params:1:16:100 non_existent_patient:1:9999 zero_appointment:1:16:0"
PROCEDURE_TEST_CASES["sp_find_patients_v1"]="ohip_search:********** name_search:PTLastOne patient_id:2"
PROCEDURE_TEST_CASES["getusermenucount"]="user_with_menu:1 user_no_menu:9999"
PROCEDURE_TEST_CASES["getpracticescheduleduserweekdays"]="user_schedule:1:1 no_schedule:1:9999"
PROCEDURE_TEST_CASES["sp_get_demographicenrolment"]="patient_enrolment:2 no_enrolment:9999"
PROCEDURE_TEST_CASES["sp_getpatientdemographicinfo"]="valid_patient:1:2 invalid_patient:1:9999"
PROCEDURE_TEST_CASES["getpatientlocations"]="valid_patient:2 valid_patient_with_doctor:2:1 invalid_patient:9999"
PROCEDURE_TEST_CASES["getmaindoctorinfo"]="valid_patient:2 invalid_patient:9999"
PROCEDURE_TEST_CASES["getpracticepatientinfo"]="valid_patient:1:2 invalid_patient:1:9999"
PROCEDURE_TEST_CASES["getuserpermissions"]="valid_user:1 invalid_user:9999"
PROCEDURE_TEST_CASES["getuserroles"]="user_with_roles:1 user_no_roles:9999"
PROCEDURE_TEST_CASES["getuseroffices"]="user_with_offices:1:1:false user_no_offices:9999:1:false"
PROCEDURE_TEST_CASES["getpracticescheduledusers"]="practice_with_users:1 practice_no_users:9999"
PROCEDURE_TEST_CASES["getappointmentreminders"]="email_reminders:emailreminder:1 voice_reminders:voicereminder:1 text_reminders:textreminder:1"
PROCEDURE_TEST_CASES["getinventoryitems"]="practice_items:1 no_items:9999"
PROCEDURE_TEST_CASES["getinventoryitem"]="single_item:1:1 invalid_item:1:9999"
PROCEDURE_TEST_CASES["getinventoryitemhistory"]="item_history:1 no_history:9999"
PROCEDURE_TEST_CASES["getinventoryoverdue"]="overdue_items:1:1:2024-01-01 no_overdue:1:1:2025-01-01"
PROCEDURE_TEST_CASES["getaudit"]="get_audit:2024-01-01:2024-12-31 no_get_audit:2025-01-01:2025-01-31"
PROCEDURE_TEST_CASES["getauditlogdata"]="audit_logdata:1:10:2024-01-01:2024-12-31 no_logdata:1:10:2025-01-01:2025-01-31"
PROCEDURE_TEST_CASES["getdoctorcomments"]="doctor_comments:1:true no_comments:9999:true"
PROCEDURE_TEST_CASES["getcustommeasurements"]="custom_measurements:1:0 no_custom:9999:0"
PROCEDURE_TEST_CASES["getdoctorinfo"]="doctor_info:1 invalid_doctor:9999"
PROCEDURE_TEST_CASES["sp_getpatientappointment"]="patient_appointment:1:2:2024-01-01:2024-12-31 invalid_patient:9999:9999:2024-01-01:2024-12-31"
PROCEDURE_TEST_CASES["sp_getpatientpreviousappointments"]="patient_history:2:1:10 no_history:9999:1:10"

# Function to generate SQL Server query
execute_sql_server() {
    local proc_name=$1
    local test_name=$2
    local params=$3
    
    case "$proc_name" in
        "getdaysheetcohorts")
            case "$test_name" in
                "empty_list") echo "DECLARE @emptyList dbo.IntegerList; EXEC dbo.GetDaysheetCohorts @patientIds = @emptyList;" ;;
                "single_patient") echo "DECLARE @patientList dbo.IntegerList; INSERT INTO @patientList (IntegerValue) VALUES ($params); EXEC dbo.GetDaysheetCohorts @patientIds = @patientList;" ;;
                "multiple_patients") 
                    IFS=',' read -r -a param_array <<< "$params"
                    query="DECLARE @patientList dbo.IntegerList; "
                    for param in "${param_array[@]}"; do
                        query+="INSERT INTO @patientList (IntegerValue) VALUES ($param); "
                    done
                    query+="EXEC dbo.GetDaysheetCohorts @patientIds = @patientList;"
                    echo "$query" ;;
                "non_existent") echo "DECLARE @patientList dbo.IntegerList; INSERT INTO @patientList (IntegerValue) VALUES ($params); EXEC dbo.GetDaysheetCohorts @patientIds = @patientList;" ;;
            esac ;;
        "getdaysheetpreconditions")
            case "$test_name" in
                "empty_list") echo "DECLARE @emptyList dbo.IntegerList; EXEC dbo.GetDaysheetPreconditions @appointmentIds = @emptyList;" ;;
                "multiple_appointments")
                    IFS=',' read -r -a param_array <<< "$params"
                    query="DECLARE @appointmentList dbo.IntegerList; "
                    for param in "${param_array[@]}"; do
                        query+="INSERT INTO @appointmentList (IntegerValue) VALUES ($param); "
                    done
                    query+="EXEC dbo.GetDaysheetPreconditions @appointmentIds = @appointmentList;"
                    echo "$query" ;;
                *) echo "DECLARE @appointmentList dbo.IntegerList; INSERT INTO @appointmentList (IntegerValue) VALUES ($params); EXEC dbo.GetDaysheetPreconditions @appointmentIds = @appointmentList;" ;;
            esac ;;
        "sch_getccdoctors")
            IFS=':' read -r -a param_array <<< "$params"
            case "${#param_array[@]}" in
                2) echo "EXEC dbo.SCH_GetCCDoctors @practiceId = ${param_array[0]}, @patientId = ${param_array[1]}, @appointmentId = 0;" ;;
                3) echo "EXEC dbo.SCH_GetCCDoctors @practiceId = ${param_array[0]}, @patientId = ${param_array[1]}, @appointmentId = ${param_array[2]};" ;;
            esac ;;
        "sp_find_patients_v1")
            case "$test_name" in
                "ohip_search") echo "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @OHIP = '$params', @TOPResult = 20;" ;;
                "name_search") echo "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @LastName = '$params', @TOPResult = 20;" ;;
                "patient_id") echo "EXEC [dbo].[SP_Find_Patients_V1] @PracticeId = 1, @PatientId = $params, @TOPResult = 20;" ;;
            esac ;;
        "getusermenucount")
            echo "EXEC [dbo].[GetUserMenuCount] @UserId = $params;" ;;
        "getpracticescheduleduserweekdays")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[GetPracticeScheduledUserWeekDays] @practiceId = ${param_array[0]}, @officeId = ${param_array[1]};" ;;
        "sp_get_demographicenrolment")
            echo "EXEC [dbo].[SP_Get_DemographicEnrolment] @PatientRecordId = $params;" ;;
        "sp_getpatientdemographicinfo")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[SP_GetPatientDemographicInfo] @PracticeId = ${param_array[0]}, @PatientId = ${param_array[1]};" ;;
        "getpatientlocations")
            case "$test_name" in
                "valid_patient") echo "EXEC [dbo].[GetPatientLocations] @PatientId = $params;" ;;
                "valid_patient_with_doctor") 
                    IFS=':' read -r -a param_array <<< "$params"
                    echo "EXEC [dbo].[GetPatientLocations] @PatientId = ${param_array[0]}, @ExternalDoctorId = ${param_array[1]};" ;;
                *) echo "EXEC [dbo].[GetPatientLocations] @PatientId = $params;" ;;
            esac ;;
        "getmaindoctorinfo")
            echo "EXEC [dbo].[GetMainDoctorInfo] @PatientId = $params;" ;;
        "getpracticepatientinfo")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[GetPracticePatientInfo] @PracticeId = ${param_array[0]}, @PatientRecordId = ${param_array[1]};" ;;
        "getuserpermissions")
            echo "EXEC [dbo].[GetUserPermissions] @UserId = $params;" ;;
        "getuserroles")
            echo "EXEC [dbo].[GetUserRoles] @UserId = $params;" ;;
        "getuseroffices")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[GetUserOffices] @UserId = ${param_array[0]}, @PracticeId = ${param_array[1]}, @IsAdmin = ${param_array[2]};" ;;
        "getpracticescheduledusers")
            echo "EXEC [dbo].[GetPracticeScheduledUsers] @PracticeId = $params;" ;;
        "getappointmentreminders")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[GetAppointmentReminders_v2] @reminderType = '${param_array[0]}', @officeId = ${param_array[1]};" ;;
        "getinventoryitems")
            echo "EXEC [dbo].[GetInventoryItems] @PracticeId = $params;" ;;
        "getinventoryitem")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[GetInventoryItem] @PracticeId = ${param_array[0]}, @InventoryId = ${param_array[1]};" ;;
        "getinventoryitemhistory")
            echo "EXEC [dbo].[GetInventoryItemHistory] @InventoryId = $params;" ;;
        "getinventoryoverdue")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[GetInventoryOverDue] @PracticeId = ${param_array[0]}, @OfficeId = ${param_array[1]}, @SelectedDate = '${param_array[2]}';" ;;
        "getaudit")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[GetAudit] @fromDate = '${param_array[0]}', @toDate = '${param_array[1]}';" ;;
        "getauditlogdata")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[GetAuditLogData] @page = ${param_array[0]}, @pagesize = ${param_array[1]}, @startdate = '${param_array[2]}', @enddate = '${param_array[3]}';" ;;
        "getdoctorcomments")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[GetDoctorComments] @patientId = ${param_array[0]}, @allComments = ${param_array[1]};" ;;
        "getcustommeasurements")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[GetCustomMeasurements] @externalDoctorId = ${param_array[0]}, @customType = ${param_array[1]};" ;;
        "getdoctorinfo")
            echo "EXEC [dbo].[GetDoctorInfo] @UserId = $params;" ;;
        "sp_getpatientappointment")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[SP_GetPatientAppointment] @practiceId = ${param_array[0]}, @patientRecordId = ${param_array[1]}, @fromDate = '${param_array[2]}', @toDate = '${param_array[3]}';" ;;
        "sp_getpatientpreviousappointments")
            IFS=':' read -r -a param_array <<< "$params"
            echo "EXEC [dbo].[SP_GetPatientPreviousAppointments] @PatientId = ${param_array[0]}, @PracticeId = ${param_array[1]}, @Limit = ${param_array[2]};" ;;
    esac
}

# Function to generate PostgreSQL query
execute_postgres() {
    local proc_name=$1
    local test_name=$2
    local params=$3
    
    case "$proc_name" in
        "getdaysheetcohorts")
            case "$test_name" in
                "empty_list") echo "SELECT * FROM dbo.getdaysheetcohorts(ARRAY[]::INTEGER[])" ;;
                "single_patient") echo "SELECT * FROM dbo.getdaysheetcohorts(ARRAY[$params])" ;;
                "multiple_patients") echo "SELECT * FROM dbo.getdaysheetcohorts(ARRAY[$params])" ;;
                "non_existent") echo "SELECT * FROM dbo.getdaysheetcohorts(ARRAY[$params])" ;;
            esac ;;
        "getdaysheetpreconditions")
            case "$test_name" in
                "empty_list") echo "SELECT * FROM dbo.getdaysheetpreconditions(ARRAY[]::INTEGER[])" ;;
                *) echo "SELECT * FROM dbo.getdaysheetpreconditions(ARRAY[$params])" ;;
            esac ;;
        "sch_getccdoctors")
            IFS=':' read -r -a param_array <<< "$params"
            case "${#param_array[@]}" in
                2) echo "SELECT * FROM dbo.SCH_GetCCDoctors(${param_array[0]}, ${param_array[1]}, 0)" ;;
                3) echo "SELECT * FROM dbo.SCH_GetCCDoctors(${param_array[0]}, ${param_array[1]}, ${param_array[2]})" ;;
            esac ;;
        "sp_find_patients_v1")
            case "$test_name" in
                "ohip_search") echo "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := '$params', p_top_result := 20, p_active := NULL)" ;;
                "name_search") echo "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := '$params', p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := NULL, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL)" ;;
                "patient_id") echo "SELECT * FROM dbo.SP_Find_Patients_V1(p_practice_id := 1, p_last_name := NULL, p_first_name := NULL, p_date_of_birth := NULL, p_year_of_birth := NULL, p_patient_id := $params, p_phone_number := NULL, p_ohip := NULL, p_top_result := 20, p_active := NULL)" ;;
            esac ;;
        "getusermenucount")
            # FIXED: Use correct parameter name p_userId (not p_user_id)
            echo "SELECT * FROM dbo.getusermenucount($params)" ;;
        "getpracticescheduleduserweekdays")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.getpracticescheduleduserweekdays(${param_array[0]}, ${param_array[1]})" ;;
        "sp_get_demographicenrolment")
            echo "SELECT * FROM dbo.sp_get_demographicenrolment($params)" ;;
        "sp_getpatientdemographicinfo")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.sp_getpatientdemographicinfo(${param_array[0]}, ${param_array[1]})" ;;
        "getpatientlocations")
            case "$test_name" in
                "valid_patient") echo "SELECT * FROM dbo.getpatientlocations($params)" ;;
                "valid_patient_with_doctor") 
                    IFS=':' read -r -a param_array <<< "$params"
                    echo "SELECT * FROM dbo.getpatientlocations(${param_array[0]}, ${param_array[1]})" ;;
                *) echo "SELECT * FROM dbo.getpatientlocations($params)" ;;
            esac ;;
        "getmaindoctorinfo")
            echo "SELECT * FROM dbo.getmaindoctorinfo($params)" ;;
        "getpracticepatientinfo")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.getpracticepatientinfo(${param_array[0]}, ${param_array[1]})" ;;
        "getuserpermissions")
            echo "SELECT * FROM dbo.getuserpermissions($params)" ;;
        "getuserroles")
            echo "SELECT * FROM dbo.getuserroles($params)" ;;
        "getuseroffices")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.getuseroffices(${param_array[0]}, ${param_array[1]}, ${param_array[2]})" ;;
        "getpracticescheduledusers")
            echo "SELECT * FROM dbo.getpracticescheduledusers($params)" ;;
        "getappointmentreminders")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.getappointmentreminders('${param_array[0]}', ${param_array[1]})" ;;
        "getinventoryitems")
            echo "SELECT * FROM dbo.getinventoryitems($params)" ;;
        "getinventoryitem")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.getinventoryitem(${param_array[0]}, ${param_array[1]})" ;;
        "getinventoryitemhistory")
            echo "SELECT * FROM dbo.getinventoryitemhistory($params)" ;;
        "getinventoryoverdue")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.getinventoryoverdue(${param_array[0]}, ${param_array[1]}, '${param_array[2]}')" ;;
        "getaudit")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.getaudit('${param_array[0]}', '${param_array[1]}')" ;;
        "getauditlogdata")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.getauditlogdata(${param_array[0]}, ${param_array[1]}, '${param_array[2]}', '${param_array[3]}')" ;;
        "getdoctorcomments")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.getdoctorcomments(${param_array[0]}, ${param_array[1]})" ;;
        "getcustommeasurements")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.getcustommeasurements(${param_array[0]}, ${param_array[1]})" ;;
        "getdoctorinfo")
            echo "SELECT * FROM dbo.getdoctorinfo($params)" ;;
        "sp_getpatientappointment")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.sp_getpatientappointment(${param_array[0]}, ${param_array[1]})" ;;
        "sp_getpatientpreviousappointments")
            IFS=':' read -r -a param_array <<< "$params"
            echo "SELECT * FROM dbo.sp_getpatientpreviousappointments(${param_array[0]}, ${param_array[1]}, ${param_array[2]})" ;;
    esac
}

# Function to test a single procedure
test_procedure() {
    local proc_name=$1
    local verbose=${2:-false}
    
    log_message "========================================="
    log_message "Testing procedure: $proc_name"
    log_message "========================================="
    
    local proc_test_cases=${PROCEDURE_TEST_CASES[$proc_name]}
    
    if [ -z "$proc_test_cases" ]; then
        log_message "❌ No test cases defined for $proc_name"
        return 1
    fi
    
    local proc_passed=0
    local proc_failed=0
    local total_tests=0
    
    # Parse and execute test cases
    IFS=' ' read -r -a test_cases <<< "$proc_test_cases"
    
    for test_case in "${test_cases[@]}"; do
        IFS=':' read -r test_name params <<< "$test_case"
        
        log_message "  Running test: $test_name (params: $params)"
        total_tests=$((total_tests + 1))
        
        # Get SQL queries
        sql_server_query=$(execute_sql_server "$proc_name" "$test_name" "$params")
        postgres_query=$(execute_postgres "$proc_name" "$test_name" "$params")
        
        if [ "$verbose" = true ]; then
            log_message "    SQL Server: $sql_server_query"
            log_message "    PostgreSQL: $postgres_query"
        fi
        
        # Execute the real database test
        log_message "    🔄 EXECUTING REAL DATABASE TEST..."
        
        # Run test with direct Python call (timeout handled internally if needed)
        # Temporarily disable set -e to handle test failures gracefully
        set +e
        test_output=$(python3 "$SCRIPT_DIR/execute_real_database_test.py" \
            "$proc_name" "$test_name" "$sql_server_query" "$postgres_query" 2>/dev/null)
        test_exit_code=$?
        set -e
        
        # Check for error
        if [ "$test_exit_code" -ne 0 ] && [ "$test_exit_code" -ne 1 ]; then
            log_message "    ⚠️ ERROR - Test execution failed with code $test_exit_code"
            proc_failed=$((proc_failed + 1))
            continue
        fi
        
        # Parse the JSON output (which should now be clean)
        if echo "$test_output" | python3 -m json.tool > /dev/null 2>&1; then
            test_status=$(echo "$test_output" | python3 -c "import sys, json; print(json.load(sys.stdin)['status'])" 2>/dev/null || echo "UNKNOWN")
            test_message=$(echo "$test_output" | python3 -c "import sys, json; print(json.load(sys.stdin)['message'])" 2>/dev/null || echo "No message")
            
            if [ "$test_status" = "PASS" ]; then
                proc_passed=$((proc_passed + 1))
                log_message "    ✅ PASS"
                if [ "$verbose" = true ]; then
                    log_message "       $test_message"
                fi
            elif [ "$test_status" = "FAIL" ]; then
                proc_failed=$((proc_failed + 1))
                log_message "    ❌ FAIL"
                log_message "       $test_message"
                if [ "$verbose" = true ]; then
                    echo "$test_output" | python3 -c "
import sys, json
data = json.load(sys.stdin)
if 'differences' in data and data['differences']:
    print('       Differences:')
    for diff in data['differences']:
        print(f'         - {diff}')
" 2>/dev/null || true
                fi
            else
                log_message "    ⚠️  UNKNOWN STATUS: $test_status"
                log_message "       $test_message"
            fi
        else
            proc_failed=$((proc_failed + 1))
            log_message "    ❌ TEST EXECUTION FAILED"
            log_message "       Could not parse test output as JSON"
            if [ "$verbose" = true ]; then
                log_message "       Raw output: $test_output"
            fi
        fi
    done
    
    log_message ""
    log_message "  Procedure $proc_name summary: $proc_passed passed, $proc_failed failed (out of $total_tests tests)"
    log_message ""
    
    return $proc_failed
}

# Main execution
main() {
    local run_all=false
    local target_procedure=""
    local verbose=false
    
    # Parse arguments
    case "${1:-}" in
        "--all")
            run_all=true
            ;;
        "--help"|"-h"|"")
            show_usage
            exit 0
            ;;
        *)
            target_procedure="$1"
            if [ "${2:-}" = "--verbose" ]; then
                verbose=true
            fi
            ;;
    esac
    
    # Initialize master log
    echo "=========================================" > "$MASTER_LOG"
    echo "POSTGRESQL PROCEDURE TESTING" >> "$MASTER_LOG"
    echo "Started: $(date)" >> "$MASTER_LOG"
    echo "=========================================" >> "$MASTER_LOG"
    
    local total_failed=0
    local total_procedures=0
    
    if [ "$run_all" = true ]; then
        log_message "Running tests for all procedures..."
        for proc_name in "${WORKING_PROCEDURES[@]}"; do
            total_procedures=$((total_procedures + 1))
            if ! test_procedure "$proc_name" "$verbose"; then
                total_failed=$((total_failed + 1))
            fi
        done
        
        log_message "========================================="
        log_message "FINAL SUMMARY"
        log_message "========================================="
        log_message "Procedures tested: $total_procedures"
        log_message "Procedures with failures: $total_failed"
        log_message "Procedures fully passing: $((total_procedures - total_failed))"
        
    else
        # Test specific procedure
        if [[ " ${WORKING_PROCEDURES[*]} " =~ " $target_procedure " ]]; then
            log_message "Running tests for procedure: $target_procedure"
            test_procedure "$target_procedure" "$verbose"
            total_failed=$?
        else
            log_message "❌ Procedure '$target_procedure' is not in the list of working procedures."
            log_message "Available procedures: ${WORKING_PROCEDURES[*]}"
            exit 1
        fi
    fi
    
    log_message "========================================="
    log_message "Testing completed: $(date)"
    log_message "========================================="
    
    exit $total_failed
}

main "$@"