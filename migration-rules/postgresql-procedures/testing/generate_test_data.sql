-- Test data generator for stored procedure testing
-- This script should be run on both SQL Server and PostgreSQL databases
-- to ensure identical test data for comparison testing

-- ==========================================================
-- SQL SERVER VERSION
-- ==========================================================

-- Clean existing test data
IF DB_NAME() LIKE '%test%' OR DB_NAME() LIKE '%Test%'
BEGIN
    DELETE FROM PatientCohorts WHERE Id IN (1001, 1002, 1003, 1004, 1005);
    DELETE FROM Cohorts WHERE Id IN (101, 102, 103, 104);
END
GO

-- Insert test cohorts
SET IDENTITY_INSERT Cohorts ON;

INSERT INTO Cohorts (Id, Description, UserId, practiceId, PracticeDoctorId, DateCreated, DateLastModified, CohortClassId)
VALUES 
    (101, 'Test Cardiology Cohort', '<EMAIL>', 1, 1, '2024-01-01T10:00:00', '2024-01-01T10:00:00', 1),
    (102, 'Test Diabetes Cohort', '<EMAIL>', 1, 1, '2024-01-02T11:00:00', '2024-01-02T11:00:00', 1),
    (103, 'Test Hypertension Cohort', '<EMAIL>', 1, 2, '2024-01-03T12:00:00', '2024-01-03T12:00:00', 1),
    (104, 'Test Empty Cohort', '<EMAIL>', 1, 1, '2024-01-04T13:00:00', NULL, 1);

SET IDENTITY_INSERT Cohorts OFF;
GO

-- Insert test patient cohorts
SET IDENTITY_INSERT PatientCohorts ON;

INSERT INTO PatientCohorts (Id, PatientId, Started, Terminated, DoctorId, OfficeId, Notes, CohortId)
VALUES 
    (1001, 1001, '2024-01-15T09:00:00', '2024-06-15T09:00:00', 1, 1, 'Patient enrolled in cardiology program', 101),
    (1002, 1002, '2024-01-20T10:30:00', NULL, 1, 1, 'Active diabetes management', 102),
    (1003, 1003, '2024-02-01T14:00:00', '2024-08-01T14:00:00', 2, 1, NULL, 103),
    (1004, 1001, '2024-02-15T08:00:00', NULL, 1, 1, 'Dual enrollment - hypertension monitoring', 103),
    (1005, 1004, '2024-03-01T16:00:00', '2024-03-31T16:00:00', 1, 1, 'Short-term monitoring', 102);

SET IDENTITY_INSERT PatientCohorts OFF;
GO

-- ==========================================================
-- POSTGRESQL VERSION
-- ==========================================================

-- Clean existing test data
DELETE FROM dbo.patientcohorts WHERE id IN (1001, 1002, 1003, 1004, 1005);
DELETE FROM dbo.cohorts WHERE id IN (101, 102, 103, 104);

-- Insert test cohorts
INSERT INTO dbo.cohorts (id, description, userid, practiceid, practicedoctorid, datecreated, datelastmodified, cohortclassid)
VALUES 
    (101, 'Test Cardiology Cohort', '<EMAIL>', 1, 1, '2024-01-01T10:00:00+00'::timestamptz, '2024-01-01T10:00:00+00'::timestamptz, 1),
    (102, 'Test Diabetes Cohort', '<EMAIL>', 1, 1, '2024-01-02T11:00:00+00'::timestamptz, '2024-01-02T11:00:00+00'::timestamptz, 1),
    (103, 'Test Hypertension Cohort', '<EMAIL>', 1, 2, '2024-01-03T12:00:00+00'::timestamptz, '2024-01-03T12:00:00+00'::timestamptz, 1),
    (104, 'Test Empty Cohort', '<EMAIL>', 1, 1, '2024-01-04T13:00:00+00'::timestamptz, NULL, 1);

-- Reset sequence to ensure proper auto-increment
SELECT setval('dbo.cohorts_id_seq', 1000);

-- Insert test patient cohorts
INSERT INTO dbo.patientcohorts (id, patientid, started, terminated, doctorid, officeid, notes, cohortid)
VALUES 
    (1001, 1001, '2024-01-15T09:00:00+00'::timestamptz, '2024-06-15T09:00:00+00'::timestamptz, 1, 1, 'Patient enrolled in cardiology program', 101),
    (1002, 1002, '2024-01-20T10:30:00+00'::timestamptz, NULL, 1, 1, 'Active diabetes management', 102),
    (1003, 1003, '2024-02-01T14:00:00+00'::timestamptz, '2024-08-01T14:00:00+00'::timestamptz, 2, 1, NULL, 103),
    (1004, 1001, '2024-02-15T08:00:00+00'::timestamptz, NULL, 1, 1, 'Dual enrollment - hypertension monitoring', 103),
    (1005, 1004, '2024-03-01T16:00:00+00'::timestamptz, '2024-03-31T16:00:00+00'::timestamptz, 1, 1, 'Short-term monitoring', 102);

-- Reset sequence to ensure proper auto-increment
SELECT setval('dbo.patientcohorts_id_seq', 2000);

-- ==========================================================
-- VERIFICATION QUERIES (run on both databases)
-- ==========================================================

-- Check cohorts data
-- SELECT COUNT(*) as CohortCount FROM dbo.Cohorts WHERE Id BETWEEN 101 AND 104; -- SQL Server
-- SELECT COUNT(*) as CohortCount FROM dbo.cohorts WHERE id BETWEEN 101 AND 104; -- PostgreSQL

-- Check patient cohorts data  
-- SELECT COUNT(*) as PatientCohortCount FROM dbo.PatientCohorts WHERE Id BETWEEN 1001 AND 1005; -- SQL Server
-- SELECT COUNT(*) as PatientCohortCount FROM dbo.patientcohorts WHERE id BETWEEN 1001 AND 1005; -- PostgreSQL

-- Test data overview
-- SQL Server: SELECT pc.Id, pc.PatientId, c.Description FROM PatientCohorts pc JOIN Cohorts c ON pc.CohortId = c.Id WHERE pc.Id BETWEEN 1001 AND 1005;
-- PostgreSQL: SELECT pc.id, pc.patientid, c.description FROM dbo.patientcohorts pc JOIN dbo.cohorts c ON pc.cohortid = c.id WHERE pc.id BETWEEN 1001 AND 1005;