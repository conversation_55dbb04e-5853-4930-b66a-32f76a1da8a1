#!/usr/bin/env python3
"""
Test script for SP_Find_Patients_V1 stored procedure migration
Tests the SP_Find_Patients_V1 procedure/function between SQL Server and PostgreSQL
"""

import json
import sys
import os

def test_sp_find_patients_v1():
    """Test SP_Find_Patients_V1 procedure with various parameter combinations"""
    
    test_cases = [
        {
            "name": "ohip_search_exact",
            "practice_id": 1,
            "ohip": "**********",
            "description": "OHIP number search - exact match"
        },
        {
            "name": "ohip_search_partial",
            "practice_id": 1,
            "ohip": "1234",
            "description": "OHIP number search - partial match"
        },
        {
            "name": "name_search_both",
            "practice_id": 1,
            "last_name": "<PERSON>",
            "first_name": "<PERSON>",
            "description": "Search by both first and last name"
        },
        {
            "name": "name_search_lastname_only",
            "practice_id": 1,
            "last_name": "<PERSON>",
            "description": "Search by last name only"
        },
        {
            "name": "name_search_firstname_only",
            "practice_id": 1,
            "first_name": "<PERSON>",
            "description": "Search by first name only"
        },
        {
            "name": "phone_search",
            "practice_id": 1,
            "phone_number": "************",
            "description": "Search by phone number"
        },
        {
            "name": "phone_search_digits_only",
            "practice_id": 1,
            "phone_number": "**********",
            "description": "Search by phone number - digits only"
        },
        {
            "name": "dob_search_exact",
            "practice_id": 1,
            "date_of_birth": "1980-05-15",
            "description": "Search by exact date of birth"
        },
        {
            "name": "year_of_birth",
            "practice_id": 1,
            "year_of_birth": 1980,
            "description": "Search by year of birth"
        },
        {
            "name": "patient_id_search",
            "practice_id": 1,
            "patient_id": 1002,
            "description": "Search by patient ID"
        },
        {
            "name": "active_patients_only",
            "practice_id": 1,
            "last_name": "Smith",
            "active": 1,
            "description": "Search active patients only"
        },
        {
            "name": "inactive_patients",
            "practice_id": 1,
            "last_name": "Smith",
            "active": 0,
            "description": "Search inactive patients"
        },
        {
            "name": "top_results_limit",
            "practice_id": 1,
            "last_name": "Smith",
            "top_result": 5,
            "description": "Limit results to top 5"
        },
        {
            "name": "combined_search",
            "practice_id": 1,
            "last_name": "Smith",
            "first_name": "John",
            "year_of_birth": 1980,
            "active": 1,
            "description": "Combined search criteria"
        },
        {
            "name": "no_criteria",
            "practice_id": 1,
            "description": "No search criteria - should return empty"
        },
        {
            "name": "non_existent_patient",
            "practice_id": 1,
            "last_name": "NonExistentPatient999",
            "description": "Search for non-existent patient"
        }
    ]
    
    results = {
        "procedure_name": "SP_Find_Patients_V1",
        "test_run_date": "2024-09-12T00:00:00Z",
        "test_cases": []
    }
    
    print("=== SP_Find_Patients_V1 Procedure Test ===")
    print(f"Testing {len(test_cases)} test cases...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}/{len(test_cases)}: {test_case['name']}")
        print(f"Description: {test_case['description']}")
        
        # Build SQL Server query
        sql_server_query = build_sql_server_query(test_case)
        
        # Build PostgreSQL query
        postgres_query = build_postgres_query(test_case)
        
        print(f"SQL Server: {sql_server_query}")
        print(f"PostgreSQL: {postgres_query}")
        
        # Add to results
        test_result = {
            "test_name": test_case['name'],
            "parameters": format_parameters(test_case),
            "sql_server_query": sql_server_query,
            "postgres_query": postgres_query,
            "description": test_case['description'],
            "result": "PENDING"  # Will be updated when actual tests run
        }
        
        results["test_cases"].append(test_result)
    
    # Save results to file
    output_file = "results/sp_find_patients_v1_test_results.json"
    os.makedirs("results", exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n=== Test Configuration Complete ===")
    print(f"Test cases saved to: {output_file}")
    print("\nTo execute these tests:")
    print("1. Run SQL queries against SQL Server database using MCP")
    print("2. Run PostgreSQL queries against PostgreSQL database using MCP")
    print("3. Compare results for consistency")
    
    return results

def format_parameters(test_case):
    """Format test case parameters for display"""
    params = []
    for key, value in test_case.items():
        if key not in ['name', 'description'] and value is not None:
            params.append(f"{key}={value}")
    return ", ".join(params)

def build_sql_server_query(test_case):
    """Build SQL Server stored procedure call"""
    params = [f"@PracticeId = {test_case['practice_id']}"]
    
    if test_case.get('last_name'):
        params.append(f"@LastName = '{test_case['last_name']}'")
    if test_case.get('first_name'):
        params.append(f"@FirstName = '{test_case['first_name']}'")
    if test_case.get('date_of_birth'):
        params.append(f"@DateOfBirth = '{test_case['date_of_birth']}'")
    if test_case.get('year_of_birth'):
        params.append(f"@YearOfBirth = {test_case['year_of_birth']}")
    if test_case.get('patient_id'):
        params.append(f"@PatientId = {test_case['patient_id']}")
    if test_case.get('phone_number'):
        params.append(f"@PhoneNumber = '{test_case['phone_number']}'")
    if test_case.get('ohip'):
        params.append(f"@OHIP = '{test_case['ohip']}'")
    
    # Default to 20 unless specified
    top_result = test_case.get('top_result', 20)
    params.append(f"@TOPResult = {top_result}")
    
    if test_case.get('active') is not None:
        params.append(f"@Active = {test_case['active']}")
    
    return f"EXEC [dbo].[SP_Find_Patients_V1] {', '.join(params)};"

def build_postgres_query(test_case):
    """Build PostgreSQL function call"""
    params = []
    param_names = []
    
    # Practice ID is required
    params.append(f"p_practice_id := {test_case['practice_id']}")
    
    if test_case.get('last_name'):
        params.append(f"p_last_name := '{test_case['last_name']}'")
    else:
        params.append("p_last_name := NULL")
        
    if test_case.get('first_name'):
        params.append(f"p_first_name := '{test_case['first_name']}'")
    else:
        params.append("p_first_name := NULL")
        
    if test_case.get('date_of_birth'):
        params.append(f"p_date_of_birth := '{test_case['date_of_birth']}'::date")
    else:
        params.append("p_date_of_birth := NULL")
        
    if test_case.get('year_of_birth'):
        params.append(f"p_year_of_birth := {test_case['year_of_birth']}")
    else:
        params.append("p_year_of_birth := NULL")
        
    if test_case.get('patient_id'):
        params.append(f"p_patient_id := {test_case['patient_id']}")
    else:
        params.append("p_patient_id := NULL")
        
    if test_case.get('phone_number'):
        params.append(f"p_phone_number := '{test_case['phone_number']}'")
    else:
        params.append("p_phone_number := NULL")
        
    if test_case.get('ohip'):
        params.append(f"p_ohip := '{test_case['ohip']}'")
    else:
        params.append("p_ohip := NULL")
    
    # Default to 20 unless specified
    top_result = test_case.get('top_result', 20)
    params.append(f"p_top_result := {top_result}")
    
    if test_case.get('active') is not None:
        params.append(f"p_active := {test_case['active']}")
    else:
        params.append("p_active := NULL")
    
    return f"SELECT * FROM dbo.SP_Find_Patients_V1({', '.join(params)});"

if __name__ == "__main__":
    test_sp_find_patients_v1()