=======================================
Procedure Migration Analysis
Started: Thu 11 Sep 2025 02:10:09 PM PDT
=======================================
Starting procedure metadata extraction...
Getting SQL Server procedures...
SQL Server query prepared: SELECT 
    ROUTINE_NAME as procedure_name,
    ROUTINE_TYPE as routine_type,
    DATA_TYPE as return_type,
    ROUTINE_DEFINITION as definition
FROM INFORMATION_SCHEMA.ROUTINES 
WHERE ROUTINE_SCHEMA = 'dbo' 
AND ROUTINE_TYPE = 'PROCEDURE'
ORDER BY ROUTINE_NAME
Getting PostgreSQL functions...
PostgreSQL query prepared: SELECT 
    routine_name as function_name,
    routine_type,
    data_type as return_type,
    routine_definition as definition
FROM information_schema.routines 
WHERE routine_schema = 'dbo'
AND routine_type = 'FUNCTION'
ORDER BY routine_name
Scanning PostgreSQL procedure files...
Found PostgreSQL file: GetPracticeScheduledUserWeekDays
Found PostgreSQL file: GetRootCategorySavedValuesLogIds
Found PostgreSQL file: GetPracticeRootCategoryPhrases
Found PostgreSQL file: P_Get_Patient_Appointments
Found PostgreSQL file: SearchAuditByDate
Found PostgreSQL file: VP_TemplateDetailsByPatient
Found PostgreSQL file: SP_Get_Patient_Appointments_For_HCV_Response_Update
Found PostgreSQL file: GetReportMedications
Found PostgreSQL file: GetPatientTestHistory
Found PostgreSQL file: Get_VP_DoctorOptions
Found PostgreSQL file: GetReportPracticeTestGroup
Found PostgreSQL file: GetAppointmentTestInfo
Found PostgreSQL file: GetPracticePatientInfo
Found PostgreSQL file: SP_GetPatientAppointmentsForRAD
Found PostgreSQL file: GetScheduleAppointments
Found PostgreSQL file: recreate-GetUserMenuCount
Found PostgreSQL file: SP_VP_GetDoctorByUserId
Found PostgreSQL file: GetUnmappedLOINC
Found PostgreSQL file: SP_AppointmentInfoForRAD
Found PostgreSQL file: usp_GetRadStudyC3
Found PostgreSQL file: GetCPPCategoriesByDoctor
Found PostgreSQL file: APP_PrepareMWL
Found PostgreSQL file: fn_CalculateAge
Found PostgreSQL file: Get_RAD_Image_List_by_Accession
Found PostgreSQL file: Get_VP_CPP_Skipped
Found PostgreSQL file: fn_GetPatientInfo
Found PostgreSQL file: GetUserMenuCount
Found PostgreSQL file: GetDoctorComments
Found PostgreSQL file: Get_VP_ReportPhrasesSavedText
Found PostgreSQL file: GetReportDoctors
Found PostgreSQL file: fn_ShowSignatureSubReport
Found PostgreSQL file: HC_CleanupTables
Found PostgreSQL file: GetLogsAllLevels
Found PostgreSQL file: GetKioskOfficeInfo
Found PostgreSQL file: TAPP_GetPracticeDoctors
Found PostgreSQL file: GetExternalDoctorLocations
Found PostgreSQL file: HL7_Mapped_Codes
Found PostgreSQL file: SP_OfficeStaffNotes
Found PostgreSQL file: GetDaysheetAppointments
Found PostgreSQL file: GetPatientInfo
Found PostgreSQL file: GetAppointmentReminders
Found PostgreSQL file: GetPatientLocations
Found PostgreSQL file: SP_GetPatientDemographicInfo
Found PostgreSQL file: SCH_GetCCDoctors
Found PostgreSQL file: GetInventoryItemHistory
Found PostgreSQL file: GetPracticePhrasesAdmin
Found PostgreSQL file: GetPracticeRootCategories
Found PostgreSQL file: GetPracticeTemplateDoctors
Found PostgreSQL file: GetAllPracticeDoctorsForOLIS
Found PostgreSQL file: SearchAuditByDateNPatientNip
Found PostgreSQL file: SP_Get_Patient_VP_CPP_Immunization_Types
Found PostgreSQL file: usp_GetRadStudy
Found PostgreSQL file: GetInventoryItem
Found PostgreSQL file: SP_VP_LabResults_Acc
Found PostgreSQL file: SearchAuditByDateNIP
Found PostgreSQL file: SP_VP_TemplateDetailsWithLOINCValues
Found PostgreSQL file: GetPatientAppointmentTests
Found PostgreSQL file: usp_SearchStudyUID
Found PostgreSQL file: GetReportAllergies
Found PostgreSQL file: GetBillingEdtErrorDoctors
Found PostgreSQL file: HCV_Get_Practice_Doctor_Credential
Found PostgreSQL file: GetInventoryOverDue
Found PostgreSQL file: Get_VP_PrivacyNotes
Found PostgreSQL file: fn_GetReportMedications
Found PostgreSQL file: Get_VP_ReportPhrases_Custom
Found PostgreSQL file: GetAppointmentReminders_v2
Found PostgreSQL file: GetDoctorPhrasesByPhraseId
Found PostgreSQL file: HC_UpdateBRAND_NAME
Found PostgreSQL file: RequisitionExist
Found PostgreSQL file: GetInventoryItems
Found PostgreSQL file: GetReportPracticeDoctorFooter
Found PostgreSQL file: GetWaitlistAppointments_v2
Found PostgreSQL file: GetDoctorRootCategoryTemplates
Found PostgreSQL file: GetPracticeScheduledUsers
Found PostgreSQL file: GetDoctorInfo
Found PostgreSQL file: fn_ConvertUnits
Found PostgreSQL file: SP_Get_VP_MeasurementSavedValue
Found PostgreSQL file: GetAPIPatientDetails
Found PostgreSQL file: GetDoctorRootCategoryPhraseSubItems
Found PostgreSQL file: GetUserRoles
Found PostgreSQL file: GetAudit
Found PostgreSQL file: GetEConsults
Found PostgreSQL file: GetKioskAppointmentInfo
Found PostgreSQL file: UpdateImmunizationRecallList
Found PostgreSQL file: SP_Batch_HCV_Responses
Found PostgreSQL file: SP_Get_DemographicEnrolment
Found PostgreSQL file: GetDaysheetAppointmentTests_v4
Found PostgreSQL file: Get_VP_OpeningStatement
Found PostgreSQL file: GetPatientPreviousTests
Found PostgreSQL file: GetSSRSReportByAppointmentTestId
Found PostgreSQL file: GetUserOffices
Found PostgreSQL file: PrepareMWL
Found PostgreSQL file: GetDoctorRootCategoryPhrases
Found PostgreSQL file: fn_GetSharedPath
Found PostgreSQL file: SP_Get_Patient_ImmunizationType
Found PostgreSQL file: deploy-functions
Found PostgreSQL file: SP_GetKioskAppointmentRoomInfo
Found PostgreSQL file: GetPracticeRootCategoryTemplates
Found PostgreSQL file: VP_TestResultByLOINC
Found PostgreSQL file: GetAppointmentTests
Found PostgreSQL file: GetReportsSent_V2
Found PostgreSQL file: SP_RAD_PatientSearch
Found PostgreSQL file: PUNCH_Can_User_Punch_InOut
Found PostgreSQL file: SearchAuditByDateNPatientNipNuser
Found PostgreSQL file: usp_SearchImage
Found PostgreSQL file: usp_SearchSeriesUID
Found PostgreSQL file: sp_GenerateBonusReport
Found PostgreSQL file: GetAppointmentTestSavedLogs
Found PostgreSQL file: GetMainDoctorInfo
Found PostgreSQL file: Get_VP_CPP_Setting
Found PostgreSQL file: GetAppointmentModifiers
Found PostgreSQL file: Get_VP_Summary
Found PostgreSQL file: getDoctorSignaturePath
Found PostgreSQL file: GetEconsultMetadata
Found PostgreSQL file: VP_TemplatePatientData
Found PostgreSQL file: GetExternalDocumentPracticePatients
Found PostgreSQL file: SP_Get_RADStudyByStudyUID
Found PostgreSQL file: SP_ContactListForSendLetter
Found PostgreSQL file: SP_HRM_ClassMapping
Found PostgreSQL file: GetDaysheetPreconditions
Found PostgreSQL file: SearchAuditByDateNPatient
Found PostgreSQL file: GetVPReportPhrasesByRootCategoryId
Found PostgreSQL file: GetPracticeWorkList_v2
Found PostgreSQL file: GetReportClinicDailyRegister
Found PostgreSQL file: SearchPatientsByOldChartNumber
Found PostgreSQL file: GetReportPhraseSaveTextByLogIds
Found PostgreSQL file: GetDoctorRootCategories
Found PostgreSQL file: GetReportQueueSearch
Found PostgreSQL file: GetWaitlistTests
Found PostgreSQL file: fn_TrimCharacter
Found PostgreSQL file: GetUserPermissions
Found PostgreSQL file: SP_UserAssociateBillingDoctors
Found PostgreSQL file: GetVPLabResults
Found PostgreSQL file: Get_VP_ReportPhrases_Skipped
Found PostgreSQL file: sp_GetRecallList
Found PostgreSQL file: Get_VP_AssociatedDocs
Found PostgreSQL file: Get_VP_Options
Found PostgreSQL file: fn_GetBillStatusId
Found PostgreSQL file: Get_VP_Logs
Found PostgreSQL file: SP_Find_Patients_V1
Found PostgreSQL file: GetDaysheetCohorts
Found PostgreSQL file: SP_ScheduledAppointmentsForBatchHCV
Found PostgreSQL file: SP_Update_PracticeDoctor_OLIS_LastAccessDateTime
Found PostgreSQL file: SP_VP_GetVitalsAndLabs_Acc
Found PostgreSQL file: GetCustomMeasurements
Found PostgreSQL file: SP_GetPatientAppointment
Found PostgreSQL file: fn_SuperTrimRight
Found PostgreSQL file: GetKioskCheckins
Found PostgreSQL file: GetBillingEdtErrorAppointments
Found PostgreSQL file: SearchAuditByDateNIPNuser
Found PostgreSQL file: SCH_ExternalDoctorSearch
Found PostgreSQL file: fn_GetTechnicianTypes
Found PostgreSQL file: SP_PracticeAccessionTestForRAD
Found PostgreSQL file: GetEconsultPatientReports
Found PostgreSQL file: SP_GetPatientPreviousAppointments
Found PostgreSQL file: GetLogs
Found PostgreSQL file: fn_HasFaxNumber
Found PostgreSQL file: GetBillingRADoctors
Found PostgreSQL file: GetAuditLogData
Found 159 PostgreSQL procedure files
=======================================
PROCEDURE EXTRACTION ANALYSIS COMPLETE
=======================================
Results saved to: /home/<USER>/source/repos/Cerebrum3-upgrade/migration-rules/postgresql-procedures/testing/procedure_migration_analysis.json
Log saved to: /home/<USER>/source/repos/Cerebrum3-upgrade/migration-rules/postgresql-procedures/testing/extract_procedures.log
PostgreSQL files found: 159
