#!/usr/bin/env python3
"""
Test script for GetPatientInfo stored procedure migration
Tests the GetPatientInfo procedure/function between SQL Server and PostgreSQL
"""

import json
import sys
import os

def test_getpatientinfo_procedure():
    """Test GetPatientInfo procedure with various parameter combinations"""
    
    test_cases = [
        {
            "name": "single_patient_basic",
            "patient_id": 1002,
            "appointment_id": 0,
            "test_id": 0,
            "active_status": 0,
            "description": "Basic patient info without appointment or test context"
        },
        {
            "name": "patient_with_appointment",
            "patient_id": 1002,
            "appointment_id": 1,
            "test_id": 0,
            "active_status": 0,
            "description": "Patient info with appointment context"
        },
        {
            "name": "patient_with_test",
            "patient_id": 1002,
            "appointment_id": 0,
            "test_id": 1,
            "active_status": 0,
            "description": "Patient info with test context"
        },
        {
            "name": "patient_full_context",
            "patient_id": 1002,
            "appointment_id": 1,
            "test_id": 1,
            "active_status": 0,
            "description": "Patient info with both appointment and test context"
        },
        {
            "name": "non_existent_patient",
            "patient_id": 9999,
            "appointment_id": 0,
            "test_id": 0,
            "active_status": 0,
            "description": "Non-existent patient should return no results"
        }
    ]
    
    results = {
        "procedure_name": "GetPatientInfo",
        "test_run_date": "2024-09-11T16:00:00Z",
        "test_cases": []
    }
    
    print("=== GetPatientInfo Procedure Test ===")
    print(f"Testing {len(test_cases)} test cases...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}/{len(test_cases)}: {test_case['name']}")
        print(f"Description: {test_case['description']}")
        
        # Build SQL Server query
        sql_server_query = build_sql_server_query(
            test_case['patient_id'], 
            test_case['appointment_id'], 
            test_case['test_id'], 
            test_case['active_status']
        )
        
        # Build PostgreSQL query
        postgres_query = build_postgres_query(
            test_case['patient_id'], 
            test_case['appointment_id'], 
            test_case['test_id'], 
            test_case['active_status']
        )
        
        print(f"SQL Server: {sql_server_query}")
        print(f"PostgreSQL: {postgres_query}")
        
        # Add to results
        test_result = {
            "test_name": test_case['name'],
            "parameters": f"patientId={test_case['patient_id']}, appointmentId={test_case['appointment_id']}, testId={test_case['test_id']}, activeStatus={test_case['active_status']}",
            "sql_server_query": sql_server_query,
            "postgres_query": postgres_query,
            "description": test_case['description'],
            "result": "PENDING"  # Will be updated when actual tests run
        }
        
        results["test_cases"].append(test_result)
    
    # Save results to file
    output_file = "results/getpatientinfo_test_results.json"
    os.makedirs("results", exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n=== Test Configuration Complete ===")
    print(f"Test cases saved to: {output_file}")
    print("\nTo execute these tests:")
    print("1. Run SQL queries against SQL Server database")  
    print("2. Run PostgreSQL queries against PostgreSQL database")
    print("3. Compare results for consistency")
    
    return results

def build_sql_server_query(patient_id, appointment_id, test_id, active_status):
    """Build SQL Server stored procedure call"""
    params = [f"@patientId = {patient_id}"]
    
    if appointment_id > 0:
        params.append(f"@appointmentId = {appointment_id}")
    if test_id > 0:  
        params.append(f"@testId = {test_id}")
    if active_status > 0:
        params.append(f"@activeStatus = {active_status}")
    
    return f"EXEC [dbo].[GetPatientInfo] {', '.join(params)};"

def build_postgres_query(patient_id, appointment_id, test_id, active_status):
    """Build PostgreSQL function call"""
    params = [str(patient_id)]
    
    # PostgreSQL function expects all parameters (using NULL for optional ones)
    if appointment_id > 0:
        params.append(str(appointment_id))
    else:
        params.append("NULL")
        
    if test_id > 0:
        params.append(str(test_id))
    else:
        params.append("NULL")
        
    params.append(str(active_status))
    
    return f"SELECT * FROM dbo.GetPatientInfo({', '.join(params)});"

if __name__ == "__main__":
    test_getpatientinfo_procedure()