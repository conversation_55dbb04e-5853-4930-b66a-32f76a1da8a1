{
    "analysis_date": "2025-09-11T14:10:10-07:00",
    "file_scan_results": {
        "postgres_files_found": 159,
        "postgres_file_list": [
  "getpracticescheduleduserweekdays",
  "getrootcategorysavedvalueslogids",
  "getpracticerootcategoryphrases",
  "p_get_patient_appointments",
  "searchauditbydate",
  "vp_templatedetailsbypatient",
  "sp_get_patient_appointments_for_hcv_response_update",
  "getreportmedications",
  "getpatienttesthistory",
  "get_vp_doctoroptions",
  "getreportpracticetestgroup",
  "getappointmenttestinfo",
  "getpracticepatientinfo",
  "sp_getpatientappointmentsforrad",
  "getscheduleappointments",
  "recreate-getusermenucount",
  "sp_vp_getdoctorbyuserid",
  "getunmappedloinc",
  "sp_appointmentinfoforrad",
  "usp_getradstudyc3",
  "getcppcategoriesbydoctor",
  "app_preparemwl",
  "fn_calculateage",
  "get_rad_image_list_by_accession",
  "get_vp_cpp_skipped",
  "fn_getpatientinfo",
  "getusermenucount",
  "getdoctorcomments",
  "get_vp_reportphrasessavedtext",
  "getreportdoctors",
  "fn_showsignaturesubreport",
  "hc_cleanuptables",
  "getlogsalllevels",
  "getkioskofficeinfo",
  "tapp_getpracticedoctors",
  "getexternaldoctorlocations",
  "hl7_mapped_codes",
  "sp_officestaffnotes",
  "getdaysheetappointments",
  "getpatientinfo",
  "getappointmentreminders",
  "getpatientlocations",
  "sp_getpatientdemographicinfo",
  "sch_getccdoctors",
  "getinventoryitemhistory",
  "getpracticephrasesadmin",
  "getpracticerootcategories",
  "getpracticetemplatedoctors",
  "getallpracticedoctorsforolis",
  "searchauditbydatenpatientnip",
  "sp_get_patient_vp_cpp_immunization_types",
  "usp_getradstudy",
  "getinventoryitem",
  "sp_vp_labresults_acc",
  "searchauditbydatenip",
  "sp_vp_templatedetailswithloincvalues",
  "getpatientappointmenttests",
  "usp_searchstudyuid",
  "getreportallergies",
  "getbillingedterrordoctors",
  "hcv_get_practice_doctor_credential",
  "getinventoryoverdue",
  "get_vp_privacynotes",
  "fn_getreportmedications",
  "get_vp_reportphrases_custom",
  "getappointmentreminders_v2",
  "getdoctorphrasesbyphraseid",
  "hc_updatebrand_name",
  "requisitionexist",
  "getinventoryitems",
  "getreportpracticedoctorfooter",
  "getwaitlistappointments_v2",
  "getdoctorrootcategorytemplates",
  "getpracticescheduledusers",
  "getdoctorinfo",
  "fn_convertunits",
  "sp_get_vp_measurementsavedvalue",
  "getapipatientdetails",
  "getdoctorrootcategoryphrasesubitems",
  "getuserroles",
  "getaudit",
  "geteconsults",
  "getkioskappointmentinfo",
  "updateimmunizationrecalllist",
  "sp_batch_hcv_responses",
  "sp_get_demographicenrolment",
  "getdaysheetappointmenttests_v4",
  "get_vp_openingstatement",
  "getpatientprevioustests",
  "getssrsreportbyappointmenttestid",
  "getuseroffices",
  "preparemwl",
  "getdoctorrootcategoryphrases",
  "fn_getsharedpath",
  "sp_get_patient_immunizationtype",
  "deploy-functions",
  "sp_getkioskappointmentroominfo",
  "getpracticerootcategorytemplates",
  "vp_testresultbyloinc",
  "getappointmenttests",
  "getreportssent_v2",
  "sp_rad_patientsearch",
  "punch_can_user_punch_inout",
  "searchauditbydatenpatientnipnuser",
  "usp_searchimage",
  "usp_searchseriesuid",
  "sp_generatebonusreport",
  "getappointmenttestsavedlogs",
  "getmaindoctorinfo",
  "get_vp_cpp_setting",
  "getappointmentmodifiers",
  "get_vp_summary",
  "getdoctorsignaturepath",
  "geteconsultmetadata",
  "vp_templatepatientdata",
  "getexternaldocumentpracticepatients",
  "sp_get_radstudybystudyuid",
  "sp_contactlistforsendletter",
  "sp_hrm_classmapping",
  "getdaysheetpreconditions",
  "searchauditbydatenpatient",
  "getvpreportphrasesbyrootcategoryid",
  "getpracticeworklist_v2",
  "getreportclinicdailyregister",
  "searchpatientsbyoldchartnumber",
  "getreportphrasesavetextbylogids",
  "getdoctorrootcategories",
  "getreportqueuesearch",
  "getwaitlisttests",
  "fn_trimcharacter",
  "getuserpermissions",
  "sp_userassociatebillingdoctors",
  "getvplabresults",
  "get_vp_reportphrases_skipped",
  "sp_getrecalllist",
  "get_vp_associateddocs",
  "get_vp_options",
  "fn_getbillstatusid",
  "get_vp_logs",
  "sp_find_patients_v1",
  "getdaysheetcohorts",
  "sp_scheduledappointmentsforbatchhcv",
  "sp_update_practicedoctor_olis_lastaccessdatetime",
  "sp_vp_getvitalsandlabs_acc",
  "getcustommeasurements",
  "sp_getpatientappointment",
  "fn_supertrimright",
  "getkioskcheckins",
  "getbillingedterrorappointments",
  "searchauditbydatenipnuser",
  "sch_externaldoctorsearch",
  "fn_gettechniciantypes",
  "sp_practiceaccessiontestforrad",
  "geteconsultpatientreports",
  "sp_getpatientpreviousappointments",
  "getlogs",
  "fn_hasfaxnumber",
  "getbillingradoctors",
  "getauditlogdata"
]
    },
    "queries_to_execute": {
        "sql_server_query": "SELECT 
    ROUTINE_NAME as procedure_name,
    ROUTINE_TYPE as routine_type,
    DATA_TYPE as return_type,
    ROUTINE_DEFINITION as definition
FROM INFORMATION_SCHEMA.ROUTINES 
WHERE ROUTINE_SCHEMA = 'dbo' 
AND ROUTINE_TYPE = 'PROCEDURE'
ORDER BY ROUTINE_NAME",
        "postgres_query": "SELECT 
    routine_name as function_name,
    routine_type,
    data_type as return_type,
    routine_definition as definition
FROM information_schema.routines 
WHERE routine_schema = 'dbo'
AND routine_type = 'FUNCTION'
ORDER BY routine_name"
    },
    "next_steps": [
        "Execute the SQL Server query to get procedure metadata",
        "Execute the PostgreSQL query to get function metadata", 
        "Compare the results to identify missing migrations",
        "Analyze parameter differences between procedures and functions"
    ]
}
