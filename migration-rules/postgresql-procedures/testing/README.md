# PostgreSQL Stored Procedure Testing Framework

This directory contains a comprehensive testing framework for validating the migration of SQL Server stored procedures to PostgreSQL functions.

## Overview

The framework was developed to systematically test the `GetDaysheetCohorts` procedure and is designed to be reusable for all stored procedure migrations in the Cerebrum3 project.

## Framework Components

### 1. Test Data Generation
- **`generate_test_data.sql`**: SQL scripts to create identical test data in both SQL Server and PostgreSQL databases
- **`apply_test_data.sh`**: Shell script to apply test data to both databases

### 2. Testing Framework
- **`test_procedure.py`**: Python framework for comparing procedure results between databases
- **`test_get_daysheet_cohorts.py`**: Specific test script for GetDaysheetCohorts procedure
- **`test_getdaysheetcohorts.sh`**: Shell script for comprehensive GetDaysheetCohorts testing

### 3. Procedure Analysis
- **`extract_procedures.sh`**: Script to analyze and compare SQL Server procedures vs PostgreSQL functions
- **`procedure_migration_analysis.json`**: Output from procedure extraction analysis

### 4. Automated Testing
- **`run_all_tests.sh`**: Automated test runner for all migrated procedures

## Quick Start

### Step 1: Setup Test Data
```bash
# Apply test data to both databases
./apply_test_data.sh
```

### Step 2: Test a Specific Procedure
```bash
# Test GetDaysheetCohorts specifically
./test_getdaysheetcohorts.sh
```

### Step 3: Run All Tests
```bash
# Run automated tests for all migrated procedures
./run_all_tests.sh
```

### Step 4: Analyze Migration Status
```bash
# Extract metadata about all procedures vs functions
./extract_procedures.sh
```

## GetDaysheetCohorts Test Results

✅ **Status**: FIXED and TESTED

### Issues Found and Fixed:
1. **NULL handling**: PostgreSQL function was converting NULL notes to empty strings
2. **Timezone handling**: PostgreSQL was returning timezone-aware timestamps instead of UTC
3. **Output format**: Function returns needed to match stored procedure column structure

### Test Cases Verified:
- Empty patient list: ✅ Both return no results
- Single patient (1001): ✅ Returns 2 cohort records 
- Multiple patients (1001,1002): ✅ Returns 3 cohort records
- Multiple patients with NULL notes (1003): ✅ NULL handling works correctly
- Non-existent patient (9999): ✅ Both return no results

### Final SQL Server vs PostgreSQL Comparison:
```sql
-- SQL Server
DECLARE @patientList dbo.IntegerList
INSERT INTO @patientList (IntegerValue) VALUES (1001), (1003)  
EXEC dbo.GetDaysheetCohorts @patientIds = @patientList

-- PostgreSQL  
SELECT * FROM dbo.getdaysheetcohorts(ARRAY[1001, 1003])
```

Both queries now return identical results with matching:
- Column names (case-insensitive comparison)
- Data types
- NULL value handling
- Timestamp formatting (UTC)

## Framework Architecture

### Data Comparison Logic
The framework normalizes data for comparison:
- Column names are case-insensitive
- Timestamps are converted to consistent formats
- NULL values are handled consistently
- String values are trimmed

### Test Result Structure
```json
{
  "procedure_name": "GetDaysheetCohorts",
  "test_cases": [
    {
      "test_name": "single_patient",
      "parameters": "1001",
      "sql_server_query": "DECLARE @patientList...",
      "postgres_query": "SELECT * FROM dbo.getdaysheetcohorts...",
      "result": "PASS"
    }
  ]
}
```

## Adding New Procedures

To add a new procedure for testing:

1. **Add test data** to `generate_test_data.sql`
2. **Define test cases** in `run_all_tests.sh`:
   ```bash
   WORKING_PROCEDURES+="newprocedurename"
   PROCEDURE_TEST_CASES["newprocedurename"]="test1:param1 test2:param2"
   ```
3. **Add query builders** in both `execute_sql_server()` and `execute_postgres()` functions
4. **Run tests** with `./run_all_tests.sh`

## Common Issues and Solutions

### 1. Timezone Handling
**Problem**: PostgreSQL returns timezone-aware timestamps, SQL Server returns UTC
**Solution**: Cast PostgreSQL timestamps to `timestamp without time zone`

### 2. NULL Coalescing
**Problem**: PostgreSQL `COALESCE(field, '')` differs from SQL Server NULL behavior  
**Solution**: Remove COALESCE and let NULLs pass through naturally

### 3. Parameter Handling
**Problem**: SQL Server table-valued parameters vs PostgreSQL arrays
**Solution**: Map `dbo.IntegerList` → `INTEGER[]` arrays

### 4. Column Naming
**Problem**: Case sensitivity differences between databases
**Solution**: Framework performs case-insensitive column comparison

## Migration Progress Tracking

The framework has identified:
- **200+** SQL Server stored procedures in the `dbo` schema
- **159** PostgreSQL function files created
- **1** procedure fully tested and verified (GetDaysheetCohorts)

### Next Steps:
1. Apply this testing methodology to remaining procedures
2. Fix issues identified through systematic testing
3. Build automated CI/CD integration for continuous testing
4. Document common migration patterns and solutions

## Files Generated

During testing, the framework creates:
- `results/` - Individual procedure test results
- `*_test.log` - Detailed execution logs  
- `*_results.json` - Structured test outcomes
- `migration_test_summary.json` - Overall testing summary

## Dependencies

- Access to both SQL Server and PostgreSQL databases via MCP
- Python 3.x for data processing scripts
- Bash shell for automation scripts
- `jq` for JSON processing (optional, for pretty formatting)

## Notes

This framework provides a foundation for systematic stored procedure migration testing. It can be extended to handle more complex procedures with multiple parameters, result sets, and error conditions.

The GetDaysheetCohorts migration serves as a reference implementation for the testing methodology and can guide migration of the remaining 200+ procedures.