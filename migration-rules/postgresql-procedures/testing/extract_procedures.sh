#!/bin/bash

# Procedure metadata extraction script
# Analyzes SQL Server stored procedures and matches them with PostgreSQL equivalents

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROCEDURES_DIR="$(dirname "$SCRIPT_DIR")"
OUTPUT_FILE="$SCRIPT_DIR/procedure_migration_analysis.json"
LOG_FILE="$SCRIPT_DIR/extract_procedures.log"

# Initialize log file
echo "=======================================" > "$LOG_FILE"
echo "Procedure Migration Analysis" >> "$LOG_FILE"
echo "Started: $(date)" >> "$LOG_FILE"
echo "=======================================" >> "$LOG_FILE"

# Function to log messages
log_message() {
    echo "$1" | tee -a "$LOG_FILE"
}

log_message "Starting procedure metadata extraction..."

# Initialize JSON output
cat > "$OUTPUT_FILE" << 'EOF'
{
    "analysis_date": "",
    "sql_server_procedures": [],
    "postgres_functions": [],
    "migration_status": {
        "total_procedures": 0,
        "migrated_functions": 0,
        "missing_functions": 0,
        "extra_functions": 0
    },
    "missing_procedures": [],
    "extra_functions": [],
    "parameter_analysis": []
}
EOF

# Update analysis date
python3 -c "
import json
import datetime
with open('$OUTPUT_FILE', 'r') as f:
    data = json.load(f)
data['analysis_date'] = datetime.datetime.now().isoformat()
with open('$OUTPUT_FILE', 'w') as f:
    json.dump(data, f, indent=2)
"

log_message "Getting SQL Server procedures..."

# Create SQL query to get all stored procedures in dbo schema
SQL_SERVER_QUERY="SELECT 
    ROUTINE_NAME as procedure_name,
    ROUTINE_TYPE as routine_type,
    DATA_TYPE as return_type,
    ROUTINE_DEFINITION as definition
FROM INFORMATION_SCHEMA.ROUTINES 
WHERE ROUTINE_SCHEMA = 'dbo' 
AND ROUTINE_TYPE = 'PROCEDURE'
ORDER BY ROUTINE_NAME"

log_message "SQL Server query prepared: $SQL_SERVER_QUERY"

log_message "Getting PostgreSQL functions..."

# Create SQL query to get all functions in dbo schema
POSTGRES_QUERY="SELECT 
    routine_name as function_name,
    routine_type,
    data_type as return_type,
    routine_definition as definition
FROM information_schema.routines 
WHERE routine_schema = 'dbo'
AND routine_type = 'FUNCTION'
ORDER BY routine_name"

log_message "PostgreSQL query prepared: $POSTGRES_QUERY"

# Get list of PostgreSQL procedure files
log_message "Scanning PostgreSQL procedure files..."
POSTGRES_FILES=()
if [ -d "$PROCEDURES_DIR" ]; then
    while IFS= read -r -d '' file; do
        filename=$(basename "$file" .sql)
        # Convert to lowercase for consistent comparison
        POSTGRES_FILES+=("$(echo "$filename" | tr '[:upper:]' '[:lower:]')")
        log_message "Found PostgreSQL file: $filename"
    done < <(find "$PROCEDURES_DIR" -name "*.sql" -not -path "*/testing/*" -print0)
fi

log_message "Found ${#POSTGRES_FILES[@]} PostgreSQL procedure files"

# Create summary of findings
cat >> "$OUTPUT_FILE.temp" << EOF
{
    "analysis_date": "$(date -Iseconds)",
    "file_scan_results": {
        "postgres_files_found": ${#POSTGRES_FILES[@]},
        "postgres_file_list": $(printf '%s\n' "${POSTGRES_FILES[@]}" | jq -R . | jq -s .)
    },
    "queries_to_execute": {
        "sql_server_query": "$SQL_SERVER_QUERY",
        "postgres_query": "$POSTGRES_QUERY"
    },
    "next_steps": [
        "Execute the SQL Server query to get procedure metadata",
        "Execute the PostgreSQL query to get function metadata", 
        "Compare the results to identify missing migrations",
        "Analyze parameter differences between procedures and functions"
    ]
}
EOF

mv "$OUTPUT_FILE.temp" "$OUTPUT_FILE"

log_message "======================================="
log_message "PROCEDURE EXTRACTION ANALYSIS COMPLETE"
log_message "======================================="
log_message "Results saved to: $OUTPUT_FILE"
log_message "Log saved to: $LOG_FILE"
log_message "PostgreSQL files found: ${#POSTGRES_FILES[@]}"

echo ""
echo "Next steps:"
echo "1. Review the queries in $OUTPUT_FILE"
echo "2. Execute them using MCP tools to get actual database metadata"
echo "3. Use the results to identify missing procedure migrations"
echo ""
echo "To get SQL Server procedures:"
echo "  Execute the sql_server_query from $OUTPUT_FILE"
echo ""
echo "To get PostgreSQL functions:"  
echo "  Execute the postgres_query from $OUTPUT_FILE"

exit 0