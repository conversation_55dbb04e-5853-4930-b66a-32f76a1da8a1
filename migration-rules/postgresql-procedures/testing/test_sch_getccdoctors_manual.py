#!/usr/bin/env python3

"""
Manual test for SCH_GetCCDoctors procedure migration
Tests the SQL Server procedure against the PostgreSQL function
"""

import json
import sys

def test_sch_getccdoctors():
    """Test SCH_GetCCDoctors with basic parameters"""
    
    print("Testing SCH_GetCCDoctors procedure migration...")
    print("=" * 60)
    
    # Test parameters
    practice_id = 1
    patient_id = 1001
    appointment_id = 0
    
    # SQL Server query
    sql_server_query = f"EXEC dbo.SCH_GetCCDoctors @practiceId = {practice_id}, @patientId = {patient_id}, @appointmentId = {appointment_id};"
    
    # PostgreSQL query  
    postgres_query = f"SELECT * FROM dbo.SCH_GetCCDoctors({practice_id}, {patient_id}, {appointment_id})"
    
    print(f"SQL Server Query:")
    print(f"  {sql_server_query}")
    print()
    print(f"PostgreSQL Query:")
    print(f"  {postgres_query}")
    print()
    
    print("Expected Result Structure:")
    print("  externaldoctorid, firstname, lastname, cpso, ohipphysicianid,")
    print("  fax, email, hrm, hrmid, emailaddress, isvip,") 
    print("  externaldoctoraddressid, externaldoctorlocationid,")
    print("  addressline1, addressline2, city, province, postalcode,")
    print("  externaldoctorphonenumberid, faxnumber, phonenumber,")
    print("  hasfaxnumber, isactive")
    print()
    
    print("Key Issues Fixed:")
    print("  ✓ Column order matches SQL Server procedure output")
    print("  ✓ Practice ID parameter is now used for security filtering") 
    print("  ✓ RETURN TABLE structure matches expected columns")
    print("  ✓ Boolean casting for bit fields")
    print("  ✓ COALESCE for NULL handling on ID fields")
    print("  ✓ Complex fax number check logic preserved")
    print()
    
    print("Test Status: READY FOR MANUAL EXECUTION")
    print("Run the queries above in their respective databases to compare results")
    
    return {
        "test_name": "sch_getccdoctors_basic",
        "sql_server_query": sql_server_query,
        "postgres_query": postgres_query,
        "status": "prepared"
    }

if __name__ == "__main__":
    result = test_sch_getccdoctors()
    print(json.dumps(result, indent=2))