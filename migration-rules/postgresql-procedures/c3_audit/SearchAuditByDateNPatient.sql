-- PostgreSQL function for SearchAuditByDateNPatient
-- Migrated from SQL Server stored procedure
-- Searches audit logs by date range and specific patient record ID

CREATE OR REPLACE FUNCTION dbo.SearchAuditByDateNPatient(
    p_from_date timestamp,
    p_to_date timestamp,
    p_patient_record_id integer
)
RETURNS TABLE(
    id integer,
    userid integer,
    username varchar(256),
    ipaddress varchar(45),
    eventtype varchar(50),
    eventdatetime timestamp,
    tablename varchar(100),
    patientrecordid integer,
    changes text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.id::integer,
        l.userid::integer,
        u.email::varchar(256) as username,
        l.ipaddress::varchar(45),
        l.eventtype::varchar(50),
        l.eventdatetime AT TIME ZONE 'UTC',
        l.tablename::varchar(100),
        l.patientrecordid::integer,
        l.changes
    FROM dbo.audits l
    JOIN dbo.usersyncs u ON u.userid = l.userid
    CROSS JOIN LATERAL (
        SELECT 
            jsonb_array_elements(
                CASE 
                    WHEN l.changes IS NOT NULL AND jsonb_typeof(l.changes::jsonb) = 'object' 
                    THEN (l.changes::jsonb -> 'changes')
                    ELSE '[]'::jsonb
                END
            ) AS change_item
    ) changes_array
    WHERE l.eventdatetime > p_from_date 
      AND l.eventdatetime < p_to_date
      AND l.changes IS NOT NULL
      AND l.changes != ''
      AND jsonb_typeof(l.changes::jsonb) = 'object'
      AND (l.changes::jsonb -> 'changes') IS NOT NULL
      AND jsonb_typeof(l.changes::jsonb -> 'changes') = 'array'
      AND (
          (changes_array.change_item ->> 'CN') = 'PatientRecordId' 
          AND (changes_array.change_item ->> 'NV') = p_patient_record_id::text
      )
    ORDER BY l.eventdatetime;

END;
$$ LANGUAGE plpgsql;