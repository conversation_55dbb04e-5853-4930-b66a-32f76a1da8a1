-- PostgreSQL function migration of SearchAuditByDateNIP stored procedure
-- Original: SearchAuditByDateNIP(@fromDate DATETIME, @toDate DATETIME, @IPAddress nvarchar(200) = NULL)
-- Purpose: Searches audit logs by date range and IP address filter

CREATE OR REPLACE FUNCTION dbo.SearchAuditByDateNIP(
    p_from_date TIMESTAMP,
    p_to_date TIMESTAMP,
    p_ip_address TEXT DEFAULT NULL
)
RETURNS TABLE(
    id integer,
    userid integer,
    username text,
    ipaddress text,
    eventtype text,
    eventdatetime timestamp,
    tablename text,
    patientrecordid integer,
    changes text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.id,
        l.userid,
        u.email as username,
        l.ipaddress,
        l.eventtype,
        l.eventdatetime AT TIME ZONE 'UTC' as eventdatetime,
        l.tablename,
        l.patientrecordid,
        l.changes
    FROM dbo.audits l
    JOIN dbo.usersyncs u ON u.userid = l.userid
    WHERE l.eventdatetime > p_from_date 
      AND l.eventdatetime < p_to_date
      AND (p_ip_address IS NULL OR l.ipaddress ILIKE '%' || p_ip_address || '%')
    ORDER BY l.eventdatetime;
END;
$$ LANGUAGE plpgsql;