-- PostgreSQL function for GetAuditLogData
-- Migrated from SQL Server stored procedure
-- Paginated audit log data with user and patient information

CREATE OR REPLACE FUNCTION dbo.GetAuditLogData(
    p_page integer,
    p_pagesize integer,
    p_startdate timestamp,
    p_enddate timestamp,
    p_user_id integer DEFAULT NULL,
    p_patient_record_id integer DEFAULT NULL,
    p_ip_address varchar(50) DEFAULT NULL,
    p_text_search varchar(1000) DEFAULT NULL
)
RETURNS TABLE(
    userid integer,
    username varchar(200),
    ipaddress varchar(50),
    eventtype varchar(50),
    eventdatetime timestamp,
    tablename varchar(100),
    patientrecordid integer,
    patientfullname varchar(200),
    changes text
) AS $$
DECLARE
    v_offset integer;
    v_limit integer;
BEGIN
    -- Calculate pagination
    IF p_page > 1 THEN
        v_offset := (p_page - 1) * p_pagesize;
    ELSE
        v_offset := 0;
    END IF;
    
    v_limit := p_pagesize;

    RETURN QUERY
    SELECT 
        a.userid::integer,
        COALESCE(u.lastname || ', ' || u.firstname, u.email, '')::varchar(200) as username,
        a.ipaddress::varchar(50),
        a.eventtype::varchar(50),
        a.eventdatetime AT TIME ZONE 'UTC',
        a.tablename::varchar(100),
        a.patientrecordid::integer,
        COALESCE(d.lastname || ', ' || d.firstname, '')::varchar(200) as patientfullname,
        a.changes
    FROM dbo.audits a
    LEFT JOIN dbo.usersyncs u ON a.userid = u.userid
    LEFT JOIN dbo.demographics d ON a.patientrecordid = d.patientrecordid
    LEFT JOIN LATERAL (
        SELECT 
            jsonb_array_elements(
                CASE 
                    WHEN a.changes IS NOT NULL AND jsonb_typeof(a.changes::jsonb) = 'object' 
                    THEN (a.changes::jsonb -> 'changes')
                    ELSE '[]'::jsonb
                END
            ) AS change_item
    ) changes_array ON (p_text_search IS NOT NULL AND TRIM(p_text_search) != '')
    WHERE a.eventdatetime >= p_startdate 
      AND a.eventdatetime <= p_enddate
      -- Optional user ID filter
      AND (p_user_id IS NULL OR p_user_id <= 0 OR a.userid = p_user_id)
      -- Optional patient record ID filter
      AND (p_patient_record_id IS NULL OR p_patient_record_id <= 0 OR a.patientrecordid = p_patient_record_id)
      -- Optional IP address filter
      AND (p_ip_address IS NULL OR TRIM(p_ip_address) = '' OR a.ipaddress ILIKE '%' || TRIM(p_ip_address) || '%')
      -- Optional text search in JSON changes
      AND (p_text_search IS NULL OR TRIM(p_text_search) = '' OR (
          a.changes IS NOT NULL
          AND a.changes != ''
          AND jsonb_typeof(a.changes::jsonb) = 'object'
          AND (a.changes::jsonb -> 'changes') IS NOT NULL
          AND jsonb_typeof(a.changes::jsonb -> 'changes') = 'array'
          AND (
              (changes_array.change_item ->> 'OV') ILIKE '%' || TRIM(p_text_search) || '%'
              OR (changes_array.change_item ->> 'NV') ILIKE '%' || TRIM(p_text_search) || '%'
          )
      ))
    ORDER BY a.eventdatetime DESC
    OFFSET v_offset
    LIMIT v_limit;

END;
$$ LANGUAGE plpgsql;