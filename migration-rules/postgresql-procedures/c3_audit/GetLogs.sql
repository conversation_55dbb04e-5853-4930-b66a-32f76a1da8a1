-- PostgreSQL function equivalent of SQL Server GetLogs stored procedure (simplified version)
-- This function returns log entries for a specific date and practice

CREATE OR REPLACE FUNCTION dbo.GetLogs(
    p_search_date DATE,
    p_practice_id INTEGER,
    p_ip_address TEXT DEFAULT NULL,
    p_user_name TEXT DEFAULT NULL,
    p_page_num INTEGER DEFAULT 1,
    p_page_size INTEGER DEFAULT 100
)
RETURNS TABLE(
    id integer,
    logdate timestamp,
    level text,
    logger text,
    message text,
    exception text,
    ipaddress text,
    username text,
    practiceid integer
) AS $$
DECLARE
    offset_rows INTEGER;
BEGIN
    -- Calculate offset for pagination
    offset_rows := (p_page_num - 1) * p_page_size;
    
    RETURN QUERY
    SELECT 
        l.id::integer,
        l.date::timestamp as logdate,
        l.level::text,
        l.logger::text,
        l.message::text,
        l.exception::text,
        COALESCE(l.ipaddress, '')::text as ipaddress,
        COALESCE(l.username, '')::text as username,
        COALESCE(l.practiceid, 0)::integer as practiceid
    FROM dbo.logs l
    WHERE l.date >= p_search_date::timestamp
      AND l.date < (p_search_date + INTERVAL '1 day')::timestamp
      AND (p_practice_id = 0 OR l.practiceid = p_practice_id)
      AND (p_ip_address IS NULL OR l.ipaddress ILIKE '%' || p_ip_address || '%')
      AND (p_user_name IS NULL OR l.username ILIKE '%' || p_user_name || '%')
    ORDER BY l.date DESC
    LIMIT p_page_size
    OFFSET offset_rows;
END;
$$ LANGUAGE plpgsql;