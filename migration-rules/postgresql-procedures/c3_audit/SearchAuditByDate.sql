-- PostgreSQL function migration of SearchAuditByDate stored procedure
-- Original: SearchAuditByDate(@fromDate DATETIME, @toDate DATETIME)
-- Purpose: Searches audit logs by date range

CREATE OR REPLACE FUNCTION dbo.SearchAuditByDate(
    p_from_date TIMESTAMP,
    p_to_date TIMESTAMP
)
RETURNS TABLE(
    id integer,
    userid integer,
    username text,
    ipaddress text,
    eventtype text,
    eventdatetime timestamp,
    tablename text,
    patientrecordid integer,
    changes text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.id,
        l.userid,
        u.email as username,
        l.ipaddress,
        l.eventtype,
        l.eventdatetime AT TIME ZONE 'UTC' as eventdatetime,
        l.tablename,
        l.patientrecordid,
        l.changes
    FROM dbo.audits l
    JOIN dbo.usersyncs u ON u.userid = l.userid
    WHERE l.eventdatetime > p_from_date 
      AND l.eventdatetime < p_to_date
    ORDER BY l.eventdatetime;
END;
$$ LANGUAGE plpgsql;