-- PostgreSQL function for GetAudit
-- Migrated from SQL Server stored procedure
-- General audit search with multiple optional filters

CREATE OR REPLACE FUNCTION dbo.GetAudit(
    p_from_date timestamp,
    p_to_date timestamp,
    p_ip_address varchar(200) DEFAULT NULL,
    p_user_id integer DEFAULT NULL,
    p_patient_record_id integer DEFAULT NULL,
    p_content varchar(200) DEFAULT NULL
)
RETURNS TABLE(
    id bigint,
    userid integer,
    username text,
    ipaddress text,
    eventtype text,
    eventdatetime timestamp with time zone,
    tablename text,
    patientrecordid integer,
    changes text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.id,
        l.userid,
        u.email as username,
        l.ipaddress,
        l.eventtype,
        l.eventdatetime,
        l.tablename,
        l.patientrecordid,
        l.changes
    FROM dbo.audits l
    JOIN dbo.aspnetusers u ON u.userid = l.userid
    LEFT JOIN LATERAL (
        SELECT 
            jsonb_array_elements(
                CASE 
                    WHEN l.changes IS NOT NULL AND jsonb_typeof(l.changes::jsonb) = 'object' 
                    THEN (l.changes::jsonb -> 'changes')
                    ELSE '[]'::jsonb
                END
            ) AS change_item
    ) changes_array ON (p_content IS NOT NULL AND p_content != '')
    WHERE l.eventdatetime > p_from_date 
      AND l.eventdatetime < p_to_date
      -- Optional IP address filter
      AND (p_ip_address IS NULL OR TRIM(p_ip_address) = '' OR l.ipaddress ILIKE '%' || TRIM(p_ip_address) || '%')
      -- Optional user ID filter
      AND (p_user_id IS NULL OR p_user_id <= 0 OR l.userid = p_user_id)
      -- Optional patient record ID filter
      AND (p_patient_record_id IS NULL OR p_patient_record_id <= 0 OR l.patientrecordid = p_patient_record_id)
      -- Optional content search in JSON changes
      AND (p_content IS NULL OR TRIM(p_content) = '' OR (
          l.changes IS NOT NULL
          AND l.changes != ''
          AND jsonb_typeof(l.changes::jsonb) = 'object'
          AND (l.changes::jsonb -> 'changes') IS NOT NULL
          AND jsonb_typeof(l.changes::jsonb -> 'changes') = 'array'
          AND (
              (changes_array.change_item ->> 'OV') ILIKE '%' || TRIM(p_content) || '%'
              OR (changes_array.change_item ->> 'NV') ILIKE '%' || TRIM(p_content) || '%'
          )
      ))
    ORDER BY l.eventdatetime;

END;
$$ LANGUAGE plpgsql;