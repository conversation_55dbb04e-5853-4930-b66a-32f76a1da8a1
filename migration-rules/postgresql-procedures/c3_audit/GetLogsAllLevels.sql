-- PostgreSQL function for GetLogsAllLevels
-- Migrated from SQL Server stored procedure
-- Retrieves log entries with pagination and optional filtering by username and IP address
-- Note: PostgreSQL doesn't have table partitioning like SQL Server, so using standard date filtering

CREATE OR REPLACE FUNCTION dbo.GetLogsAllLevels(
    p_search_date DATE,
    p_practice_id INTEGER,
    p_ip_address VARCHAR(200) DEFAULT NULL,
    p_user_name VARCHAR(200) DEFAULT NULL,
    p_page_num INTEGER DEFAULT 1,
    p_page_size INTEGER DEFAULT 100
)
RETURNS TABLE(
    id INTEGER,
    date TIMESTAMP,
    username VARCHAR(150),
    ipaddress VARCHAR(15),
    page VARCHAR(1000),
    thread VARCHAR(5),
    level VARCHAR(50),
    logger VARCHAR(255),
    message VARCHAR(8000),
    loggedin VARCHAR(1),
    practiceid INTEGER
) AS $$
DECLARE
    query_sql TEXT;
    offset_rows INTEGER;
    from_date TIMESTAMP;
    to_date TIMESTAMP;
BEGIN
    -- Calculate pagination offset
    offset_rows := (p_page_num - 1) * p_page_size;
    
    -- Set date range for the search day
    from_date := p_search_date::timestamp;
    to_date := (p_search_date + INTERVAL '1 day')::timestamp - INTERVAL '1 second';
    
    -- Build base query
    query_sql := 'SELECT 
        l.Id as id,
        l.Date as date,
        l.userName as username,
        l.ipaddress as ipaddress,
        l.page as page,
        l.Thread as thread,
        l.Level as level,
        l.Logger as logger,
        l.Message as message,
        l.LoggedIn as loggedin,
        l.PracticeId as practiceid
    FROM dbo.Logs l
    WHERE l.PracticeId = $1
      AND l.Date >= $2 
      AND l.Date <= $3';
    
    -- Add optional username filter
    IF p_user_name IS NOT NULL AND TRIM(p_user_name) <> '' THEN
        query_sql := query_sql || ' AND l.userName ILIKE ''%' || p_user_name || '%''';
    END IF;
    
    -- Add optional IP address filter
    IF p_ip_address IS NOT NULL AND TRIM(p_ip_address) <> '' THEN
        query_sql := query_sql || ' AND l.ipaddress ILIKE ''%' || p_ip_address || '%''';
    END IF;
    
    -- Add ordering and pagination
    query_sql := query_sql || ' ORDER BY l.Id OFFSET $4 LIMIT $5';
    
    -- Execute the dynamic query
    RETURN QUERY EXECUTE query_sql 
    USING p_practice_id, from_date, to_date, offset_rows, p_page_size;
    
END;
$$ LANGUAGE plpgsql;