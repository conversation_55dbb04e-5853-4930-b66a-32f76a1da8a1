# PostgreSQL Functions Deployment Script

This directory contains PostgreSQL functions/procedures that have been migrated from SQL Server stored procedures.

## Usage

The `deploy-postgres-functions.sh` script can be used to deploy these functions to a PostgreSQL database.

### Prerequisites

- PostgreSQL client (`psql`) must be installed
- Database connection parameters must be configured
- Target PostgreSQL database must be running and accessible

### Basic Usage

```bash
# List all available functions with deployment status (default behavior)
./deploy-postgres-functions.sh

# Deploy all functions
./deploy-postgres-functions.sh --deploy-all

# Deploy a specific function
./deploy-postgres-functions.sh -f GetAPIPatientDetails.sql

# List all available functions with status (explicit)
./deploy-postgres-functions.sh -l

# Show help
./deploy-postgres-functions.sh -h
```

### Configuration

Database connection parameters can be set via environment variables:

```bash
export PGHOST=localhost
export PGPORT=5432
export PGDATABASE=c3_dev
export PGUSER=postgres
export PGPASSWORD=your_password

# Then run the script
./deploy-postgres-functions.sh --deploy-all
```

### Features

- **Deployment Status**: Shows which functions are deployed (✓) or not deployed (✗) when database is accessible
- **Safe Default**: Shows all available functions when run without arguments with their deployment status
- **Automatic Schema Creation**: Ensures the `dbo` schema exists before deploying functions
- **Function Replacement**: Drops existing functions before creating new ones
- **Error Handling**: Comprehensive error checking and reporting
- **Progress Tracking**: Shows deployment progress and summary
- **Flexible Deployment**: Deploy all functions or specific ones
- **Offline Mode**: Works without database connection (shows files only)
- **Verification**: Confirms functions are deployed successfully

### Files

- `deploy-postgres-functions.sh` - Main deployment script
- `*.sql` - Individual PostgreSQL function definitions
- `deploy-functions.sql` - Legacy deployment script (not used by main script)
- `DEPLOY_INSTRUCTIONS.md` - Manual deployment instructions

### Script Output

The script provides colored output for easy reading:
- Blue: Informational messages
- Green: Success messages
- Red: Error messages
- Yellow: Warning messages

Example output (default listing with database accessible):
```
Available SQL files in /path/to/postgresql-procedures:

✓ Database connection available - showing deployment status

  ✓ APP_PrepareMWL.sql (deployed)
  ✗ fn_CalculateAge.sql (not deployed)
  ✓ fn_ConvertUnits.sql (deployed)
  ✗ GetAPIPatientDetails.sql (not deployed)
  ...

Total: 158 SQL files
  ✓ Deployed: 45
  ✗ Not deployed: 113

To deploy functions:
  All functions:     ./deploy-postgres-functions.sh --deploy-all
  Specific function: ./deploy-postgres-functions.sh -f <filename>
  Show help:         ./deploy-postgres-functions.sh -h
```

Example output (without database connection):
```
Available SQL files in /path/to/postgresql-procedures:

⚠ Database not accessible - showing files only

  - APP_PrepareMWL.sql
  - fn_CalculateAge.sql
  - fn_ConvertUnits.sql
  - GetAPIPatientDetails.sql
  ...

Total: 158 SQL files

To deploy functions:
  All functions:     ./deploy-postgres-functions.sh --deploy-all
  Specific function: ./deploy-postgres-functions.sh -f <filename>
  Show help:         ./deploy-postgres-functions.sh -h
```

Example deployment output (with --deploy-all):
```
=== PostgreSQL Functions Deployment Script ===

Checking prerequisites...
✓ Prerequisites check passed
✓ Schema 'dbo' is ready

Deploying all functions...

  Deploying: GetAPIPatientDetails.sql
    ✓ Successfully deployed GetAPIPatientDetails.sql

Deployment Summary:
  ✓ Success: 158
  Total: 158

✓ Found 158 functions/procedures in schema 'dbo'

✓ All deployments completed successfully!
```

### Troubleshooting

1. **Connection errors**: Check database connection parameters and ensure PostgreSQL is running
2. **Permission errors**: Ensure the database user has sufficient privileges to create functions
3. **Function conflicts**: The script automatically drops existing functions before creating new ones
4. **File not found**: Ensure you're running the script from the correct directory

### Notes

- **Deployment Status**: The script shows deployment status when database is accessible (✓ deployed, ✗ not deployed)
- **Safe Default**: The script lists available functions by default rather than deploying them
- **Offline Mode**: Works without database connection, showing file list only
- All functions are created in the `dbo` schema to maintain compatibility with SQL Server patterns
- The script excludes the `deploy-functions.sql` file from automatic deployment
- Functions are deployed in alphabetical order when using `--deploy-all`
- The script uses `CREATE OR REPLACE FUNCTION` syntax where possible
- Use `--deploy-all` to deploy all functions, `-f <filename>` for specific functions
- Function existence is checked by name (case insensitive) in the `dbo` schema