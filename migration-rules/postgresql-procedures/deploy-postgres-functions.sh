#!/bin/bash

# PostgreSQL Functions Deployment Script
# Deploys SQL functions/procedures to PostgreSQL database and shows deployment status
# Usage: 
#   ./deploy-postgres-functions.sh          # List functions with deployment status (default)
#   ./deploy-postgres-functions.sh --deploy-all # Deploy all functions
#   ./deploy-postgres-functions.sh -f <file> # Deploy specific function file
#   ./deploy-postgres-functions.sh -h       # Show help

set -e  # Exit on error
shopt -s nullglob  # Handle empty glob patterns gracefully

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database connection parameters
# These can be overridden by environment variables
DB_HOST="${PGHOST:-localhost}"
DB_PORT="${PGPORT:-5432}"
DB_USER="${PGUSER:-postgres}"
DB_SCHEMA="dbo"
PGPASSWORD="postgres123"

# Database configurations for different function sets
declare -A DB_CONFIGS
DB_CONFIGS[c3_dev]="c3_dev"
DB_CONFIGS[c3_audit]="c3_audit"

# Script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Function to print colored output
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show help
show_help() {
    cat << EOF
PostgreSQL Functions Deployment Script

This script manages PostgreSQL functions across multiple databases:
- Functions in c3_dev/ directory are deployed to the c3_dev database
- Functions in c3_audit/ directory are deployed to the c3_audit database

Usage:
    $(basename "$0") [OPTIONS]

Default Behavior:
    Running without arguments lists all available SQL files with deployment status (same as --list)

Options:
    -h, --help              Show this help message
    -f, --file <filename>   Deploy a specific SQL file (searches all database directories)
    -l, --list              List all available SQL files with deployment status (default)
    --deploy-all            Deploy all SQL files from all database directories
    -v, --verbose           Show detailed output

Database Connection:
    Connection parameters can be set via environment variables:
    PGHOST     - Database host (default: localhost)
    PGPORT     - Database port (default: 5432)
    PGUSER     - Database user (default: postgres)
    PGPASSWORD - Database password (default: postgres123)

Database Configuration:
    c3_dev/     - Functions deployed to c3_dev database
    c3_audit/   - Functions deployed to c3_audit database

Examples:
    # List available functions with deployment status (default behavior)
    ./$(basename "$0")
    
    # Deploy all functions from all database directories
    ./$(basename "$0") --deploy-all
    
    # Deploy a specific function (searches c3_dev/ and c3_audit/ directories)
    ./$(basename "$0") -f GetAPIPatientDetails.sql
    
    # List available functions with status explicitly
    ./$(basename "$0") -l
    
    # Deploy with custom connection settings
    PGHOST=myhost PGUSER=myuser ./$(basename "$0") --deploy-all

EOF
}

# Function to check prerequisites
check_prerequisites() {
    print_message "$BLUE" "Checking prerequisites..."
    
    # Check if psql is installed
    if ! command -v psql &> /dev/null; then
        print_message "$RED" "Error: psql command not found. Please install PostgreSQL client."
        exit 1
    fi
    
    # Test database connections for all configured databases
    local connection_errors=0
    for db_dir in "${!DB_CONFIGS[@]}"; do
        local db_name="${DB_CONFIGS[$db_dir]}"
        print_message "$BLUE" "  Testing connection to database: $db_name"
        
        if ! PGPASSWORD="${PGPASSWORD:-}" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$db_name" -c '\q' 2>/dev/null; then
            print_message "$RED" "  ✗ Cannot connect to database: $db_name"
            ((connection_errors++))
        else
            print_message "$GREEN" "  ✓ Connection to $db_name successful"
        fi
    done
    
    if [ $connection_errors -gt 0 ]; then
        print_message "$RED" "Error: Failed to connect to one or more databases."
        print_message "$YELLOW" "Connection parameters:"
        echo "  Host: $DB_HOST"
        echo "  Port: $DB_PORT"
        echo "  User: $DB_USER"
        echo ""
        print_message "$YELLOW" "Please check your connection parameters and ensure all databases are running."
        exit 1
    fi
    
    print_message "$GREEN" "✓ Prerequisites check passed"
}

# Function to ensure schema exists for all databases
ensure_schema() {
    print_message "$BLUE" "Ensuring schema '$DB_SCHEMA' exists in all databases..."
    
    for db_dir in "${!DB_CONFIGS[@]}"; do
        local db_name="${DB_CONFIGS[$db_dir]}"
        print_message "$BLUE" "  Creating schema in database: $db_name"
        
        PGPASSWORD="${PGPASSWORD:-}" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$db_name" -q -c "CREATE SCHEMA IF NOT EXISTS $DB_SCHEMA;" 2>/dev/null || true
        
        print_message "$GREEN" "  ✓ Schema '$DB_SCHEMA' is ready in $db_name"
    done
}

# Function to deploy a single SQL file to the appropriate database
deploy_sql_file() {
    local sql_file=$1
    local db_name=$2
    local file_basename=$(basename "$sql_file")
    
    if [ ! -f "$sql_file" ]; then
        print_message "$RED" "Error: File not found: $sql_file"
        return 1
    fi
    
    print_message "$BLUE" "  Deploying: $file_basename to database: $db_name"
    
    # Check if this is a function or procedure
    local object_type="function"
    if grep -qi "CREATE.*PROCEDURE" "$sql_file"; then
        object_type="procedure"
    fi
    
    # Extract function/procedure name from the file for dropping
    local func_name=""
    if [[ "$file_basename" =~ ^([^.]+)\.sql$ ]]; then
        func_name="${BASH_REMATCH[1]}"
    fi
    
    # Try to drop existing function/procedure (ignore errors)
    if [ ! -z "$func_name" ]; then
        # Try different signature patterns as we don't know the exact parameters
        PGPASSWORD="${PGPASSWORD:-}" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$db_name" -q -c "DROP FUNCTION IF EXISTS $DB_SCHEMA.$func_name CASCADE;" 2>/dev/null || true
        PGPASSWORD="${PGPASSWORD:-}" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$db_name" -q -c "DROP PROCEDURE IF EXISTS $DB_SCHEMA.$func_name CASCADE;" 2>/dev/null || true
    fi
    
    # Deploy the new function/procedure
    if PGPASSWORD="${PGPASSWORD:-}" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$db_name" -q -f "$sql_file" 2>/tmp/psql_error.log; then
        print_message "$GREEN" "    ✓ Successfully deployed $file_basename to $db_name"
        return 0
    else
        print_message "$RED" "    ✗ Failed to deploy $file_basename to $db_name"
        if [ -f /tmp/psql_error.log ]; then
            cat /tmp/psql_error.log >&2
        fi
        return 1
    fi
}

# Function to list available SQL files
list_sql_files() {
    print_message "$BLUE" "Available SQL files organized by database:"
    echo ""
    
    local total_count=0
    local total_deployed=0
    local total_not_deployed=0
    local original_dir=$(pwd)
    
    # Temporarily disable exit-on-error for file listing  
    set +e
    
    for db_dir in "${!DB_CONFIGS[@]}"; do
        local db_name="${DB_CONFIGS[$db_dir]}"
        local dir_path="$SCRIPT_DIR/$db_dir"
        
        if [ ! -d "$dir_path" ]; then
            print_message "$YELLOW" "⚠ Directory not found: $db_dir (for database $db_name)"
            continue
        fi
        
        print_message "$BLUE" "Database: $db_name (directory: $db_dir/)"
        
        # Check if this specific database is accessible for status checking
        local db_accessible=false
        if PGPASSWORD="${PGPASSWORD:-}" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$db_name" -c '\q' 2>/dev/null; then
            db_accessible=true
        fi
        
        local count=0
        local deployed=0
        local not_deployed=0
        
        cd "$dir_path" || {
            print_message "$RED" "ERROR: Could not change to directory $dir_path" >&2
            continue
        }
        
        for sql_file in *.sql; do
            # Skip if no files match (nullglob should handle this, but just in case)
            [ -f "$sql_file" ] || continue
            
            # Skip the deploy-functions.sql file
            if [ "$sql_file" != "deploy-functions.sql" ]; then
                if [ "$db_accessible" = true ]; then
                    # Check deployment status
                    if check_function_exists_in_db "$sql_file" "$db_name"; then
                        echo "  ✓ $sql_file (deployed)"
                        ((deployed++))
                        ((total_deployed++))
                    else
                        echo "  ✗ $sql_file (not deployed)"
                        ((not_deployed++))
                        ((total_not_deployed++))
                    fi
                else
                    echo "  - $sql_file (database not accessible)"
                fi
                ((count++))
                ((total_count++))
            fi
        done
        
        if [ "$db_accessible" = true ]; then
            print_message "$GREEN" "  Subtotal: $count files (✓ $deployed deployed, ✗ $not_deployed not deployed)"
        else
            print_message "$YELLOW" "  Subtotal: $count files (database not accessible)"
        fi
        echo ""
    done
    
    cd "$original_dir"
    
    # Re-enable exit on error
    set -e
    
    print_message "$GREEN" "Grand Total: $total_count SQL files"
    if [ $total_deployed -gt 0 ] || [ $total_not_deployed -gt 0 ]; then
        print_message "$GREEN" "  ✓ Total Deployed: $total_deployed"
        print_message "$YELLOW" "  ✗ Total Not Deployed: $total_not_deployed"
    fi
}

# Function to deploy all SQL files
deploy_all_functions() {
    print_message "$BLUE" "Deploying all functions from all databases..."
    echo ""
    
    local total=0
    local success=0
    local failed=0
    local original_dir=$(pwd)
    
    # Temporarily disable exit-on-error for file listing
    set +e
    
    for db_dir in "${!DB_CONFIGS[@]}"; do
        local db_name="${DB_CONFIGS[$db_dir]}"
        local dir_path="$SCRIPT_DIR/$db_dir"
        
        if [ ! -d "$dir_path" ]; then
            print_message "$YELLOW" "⚠ Directory not found: $db_dir (for database $db_name)"
            continue
        fi
        
        print_message "$BLUE" "Processing database: $db_name (directory: $db_dir/)"
        
        cd "$dir_path" || {
            print_message "$RED" "ERROR: Could not change to directory $dir_path" >&2
            continue
        }
        
        for sql_file in *.sql; do
            # Skip if no files match (nullglob should handle this, but just in case)
            [ -f "$sql_file" ] || continue
            
            # Skip the deploy-functions.sql file
            if [ "$sql_file" != "deploy-functions.sql" ]; then
                ((total++))
                if deploy_sql_file "$dir_path/$sql_file" "$db_name"; then
                    ((success++))
                else
                    ((failed++))
                fi
            fi
        done
        
        echo ""
    done
    
    cd "$original_dir"
    
    # Re-enable exit on error
    set -e
    
    echo ""
    print_message "$BLUE" "Deployment Summary:"
    print_message "$GREEN" "  ✓ Success: $success"
    if [ $failed -gt 0 ]; then
        print_message "$RED" "  ✗ Failed: $failed"
    fi
    print_message "$BLUE" "  Total: $total"
    
    if [ $failed -gt 0 ]; then
        return 1
    fi
    return 0
}

# Function to check if a specific function exists in a specific database
check_function_exists_in_db() {
    local function_name=$1
    local db_name=$2
    
    # Extract function name without extension
    local base_name=$(basename "$function_name" .sql)
    
    # Query to check if function exists (case insensitive)
    local result=$(PGPASSWORD="${PGPASSWORD:-}" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$db_name" -t -c "
        SELECT COUNT(*) 
        FROM pg_catalog.pg_proc p
        LEFT JOIN pg_catalog.pg_namespace n ON n.oid = p.pronamespace
        WHERE n.nspname = '$DB_SCHEMA' 
        AND LOWER(p.proname) = LOWER('$base_name');
    " 2>/dev/null)
    
    if [ $? -eq 0 ] && [ ! -z "$result" ]; then
        local count=$(echo $result | tr -d ' ')
        [ "$count" -gt 0 ]
    else
        false
    fi
}

# Function to check if a specific function exists in the database (legacy compatibility)
check_function_exists() {
    local function_name=$1
    # Default to c3_dev for backward compatibility
    check_function_exists_in_db "$function_name" "c3_dev"
}


# Function to verify deployment across all databases
verify_deployment() {
    print_message "$BLUE" "\nVerifying deployed functions across all databases..."
    
    for db_dir in "${!DB_CONFIGS[@]}"; do
        local db_name="${DB_CONFIGS[$db_dir]}"
        
        local result=$(PGPASSWORD="${PGPASSWORD:-}" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$db_name" -t -c "
            SELECT COUNT(*) 
            FROM pg_catalog.pg_proc p
            LEFT JOIN pg_catalog.pg_namespace n ON n.oid = p.pronamespace
            WHERE n.nspname = '$DB_SCHEMA';
        " 2>/dev/null)
        
        if [ $? -eq 0 ] && [ ! -z "$result" ]; then
            local count=$(echo $result | tr -d ' ')
            print_message "$GREEN" "✓ Found $count functions/procedures in database '$db_name', schema '$DB_SCHEMA'"
        else
            print_message "$YELLOW" "⚠ Could not verify functions in database '$db_name'"
        fi
    done
}

# Main script logic
main() {
    local specific_file=""
    local list_only=false
    local deploy_all=false
    local verbose=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--file)
                specific_file="$2"
                shift 2
                ;;
            -l|--list)
                list_only=true
                shift
                ;;
            --deploy-all)
                deploy_all=true
                shift
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            *)
                print_message "$RED" "Unknown option: $1"
                echo "Use -h or --help for usage information"
                exit 1
                ;;
        esac
    done
    
    # If just listing files OR no arguments provided (default behavior)
    if [ "$list_only" = true ] || ([ -z "$specific_file" ] && [ "$deploy_all" = false ]); then
        list_sql_files
        echo ""
        print_message "$BLUE" "To deploy functions:"
        echo "  All functions:     ./$(basename "$0") --deploy-all"
        echo "  Specific function: ./$(basename "$0") -f <filename>"
        echo "  Show help:         ./$(basename "$0") -h"
        exit 0
    fi
    
    print_message "$GREEN" "=== PostgreSQL Functions Deployment Script ==="
    echo ""
    
    # Check prerequisites
    check_prerequisites
    
    # Ensure schema exists
    ensure_schema
    
    echo ""
    
    # Deploy functions
    if [ ! -z "$specific_file" ]; then
        # Deploy specific file - check all database directories
        local file_found=false
        local deployment_success=false
        
        for db_dir in "${!DB_CONFIGS[@]}"; do
            local db_name="${DB_CONFIGS[$db_dir]}"
            local full_path="$SCRIPT_DIR/$db_dir/$specific_file"
            
            # Add .sql extension if not present
            if [[ ! "$full_path" =~ \.sql$ ]]; then
                full_path="$full_path.sql"
            fi
            
            if [ -f "$full_path" ]; then
                file_found=true
                print_message "$BLUE" "Found $specific_file in $db_dir directory for database $db_name"
                
                if deploy_sql_file "$full_path" "$db_name"; then
                    deployment_success=true
                    print_message "$GREEN" "\n✓ Deployment completed successfully!"
                else
                    print_message "$RED" "\n✗ Deployment failed!"
                    exit 1
                fi
                break
            fi
        done
        
        if [ "$file_found" = false ]; then
            print_message "$RED" "Error: File '$specific_file' not found in any database directory"
            print_message "$YELLOW" "Available directories: ${!DB_CONFIGS[*]}"
            exit 1
        fi
        
        if [ "$deployment_success" = true ]; then
            verify_deployment
            exit 0
        else
            exit 1
        fi
    elif [ "$deploy_all" = true ]; then
        # Deploy all functions
        if deploy_all_functions; then
            verify_deployment
            print_message "$GREEN" "\n✓ All deployments completed successfully!"
            exit 0
        else
            print_message "$RED" "\n✗ Some deployments failed!"
            exit 1
        fi
    fi
}

# Run main function
main "$@"