-- PostgreSQL function for GetAPIPatientDetails
-- Migrated from SQL Server stored procedure
-- Simple patient details retrieval for API

CREATE OR REPLACE FUNCTION dbo.GetAPIPatientDetails()
RETURNS TABLE(
    patientid integer,
    firstname text,
    middlename text,
    lastname text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        D.PatientRecordId AS PatientID,
        D.FirstName,
        D.MiddleName,
        D.LastName
    FROM dbo.Demographics D
    JOIN dbo.PatientRecords P ON P.Id = D.PatientRecordId
    ORDER BY D.PatientRecordId DESC
    LIMIT 100;
END;
$$ LANGUAGE plpgsql;