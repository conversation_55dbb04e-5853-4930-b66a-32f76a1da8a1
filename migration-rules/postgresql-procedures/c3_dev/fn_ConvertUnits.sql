-- PostgreSQL function migration of fn_ConvertUnits
-- Original: fn_ConvertUnits(@Value decimal(6,3)) RETURNS decimal(6,3)
-- Purpose: Converts units by dividing input value by 10
-- Modified to return TABLE for repository pattern compatibility

CREATE OR REPLACE FUNCTION dbo.fn_ConvertUnits(
    p_value DECIMAL(6,3)
)
RETURNS TABLE(
    converted_value DECIMAL(6,3)
) AS $$
BEGIN
    -- Convert units by dividing by 10
    RETURN QUERY SELECT (p_value / 10.0)::DECIMAL(6,3);
END;
$$ LANGUAGE plpgsql;