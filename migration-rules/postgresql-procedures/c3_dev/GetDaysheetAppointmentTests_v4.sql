-- PostgreSQL function equivalent of SQL Server GetDaysheetAppointmentTests_v4 stored procedure
-- This function provides the same functionality as the SQL Server version but uses PostgreSQL syntax

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create function in dbo schema for compatibility
CREATE OR REPLACE FUNCTION dbo.GetDaysheetAppointmentTests_v4(
    p_office_id INT DEFAULT NULL,
    p_selected_date TIMESTAMP DEFAULT NULL,
    p_show_expected BOOLEAN DEFAULT false,
    p_exclude_test_only BOOLEAN DEFAULT false,
    p_exclude_cancelled BOOLEAN DEFAULT false,
    p_only_action_on_abnormal BOOLEAN DEFAULT false,
    p_appointment_status INT DEFAULT NULL,
    p_appointment_id INT DEFAULT NULL,
    p_test_group_ids INT[] DEFAULT ARRAY[]::INT[],
    p_filter_patient VARCHAR(50) DEFAULT NULL,
    p_user_id INT DEFAULT 0,
    p_show_no_billing_only <PERSON><PERSON><PERSON><PERSON>N DEFAULT NULL,
    p_page_num INT DEFAULT 1,
    p_page_size INT DEFAULT 50
)
RETURNS TABLE(
    -- Column set matching C# entity exactly
    id integer,                          -- 1
    practiceid integer,                  -- 2  
    officeid integer,                    -- 3
    appointmenttime timestamp,           -- 4
    arrivedtime text,                    -- 5
    lefttime text,                       -- 6
    appointmentpurpose text,             -- 7
    appointmentstatus integer,           -- 8
    appointmentnotes text,               -- 9
    appointmentregistrar integer,        -- 10
    mwlurl text,                         -- 11
    mwlsentflag boolean,                 -- 12
    actiononabnormal boolean,            -- 13
    bookingconfirmation boolean,         -- 14
    roomnumber text,                     -- 15
    practicedoctorid integer,            -- 16
    practicedoctor text,                 -- 17
    billstatusid integer,                -- 18
    billstatus text,                     -- 19
    billstatuscolor text,                -- 20
    openingstatement text,               -- 21
    referraldoctorid integer,            -- 22
    referraldoctor text,                 -- 23
    refdocohipphysicianid text,          -- 24
    refdoccpso text,                     -- 25
    referraldoctoraddressid integer,     -- 26
    referraldoctoraddress text,          -- 27
    referraldoctorphoneid integer,       -- 28
    referraldoctorfaxnumber text,        -- 29
    familydoctorid integer,              -- 30
    familydoctor text,                   -- 31
    patientphonenumbers text,            -- 32
    appointmenttypeparentid integer,     -- 33
    appointmenttypeparent text,          -- 34
    appointmenttypeid integer,           -- 35
    appointmenttype text,                -- 36
    appointmentconfirmation integer,     -- 37
    appointmentpaymentmethod integer,    -- 38
    triageurgencyid integer,             -- 39
    triagestatusid integer,              -- 40
    isactive boolean,                    -- 41
    recordtype smallint,                 -- 42
    patientrecordid integer,             -- 43
    patientfirstname text,               -- 44
    patientlastname text,                -- 45
    patientpreferredname text,           -- 46
    patientfhirid uuid,                  -- 47
    dateofbirth date,               -- 48
    datecreated timestamp,               -- 49
    lastmodified timestamp,              -- 50
    consultcodeid integer,               -- 51
    consultcode text,                    -- 52
    diagnosticcodeid integer,            -- 53
    diagnosticcode text,                 -- 54
    diagnosticcodeid2 integer,           -- 55
    diagnosticcode2 text,                -- 56
    diagnosticcodeid3 integer,           -- 57
    diagnosticcode3 text,                -- 58
    totalhealthcards integer,            -- 59
    totalreferraldocuments integer,      -- 60
    totaldoctorcomments integer,         -- 61
    totalreferraldocfax integer,         -- 62
    totalreferraldocnumbers integer,     -- 63
    isdoctordoublebook boolean,          -- 64
    totalvpbooked integer,               -- 65
    isimported boolean,                  -- 66
    totalrecords integer,                -- 67
    priorityname text,                   -- 68
    appointmentpriorityid integer,       -- 69
    fhirid uuid,                         -- 70
    appointmenttestid integer,           -- 71
    testid integer,                      -- 72
    testfullname text,                   -- 73
    testshortname text,                  -- 74
    requiredevice integer,               -- 75 (boolean converted to int)
    starttime timestamp,                 -- 76
    appointmentteststatusid integer,     -- 77
    teststatuscolor text,                -- 78
    teststatus text,                     -- 79
    testduration integer,                -- 80
    apptestbillstatusid integer,         -- 81
    apptestreferraldoctorid integer,     -- 82
    accessionnumber text,                -- 83
    physiciancomments text,              -- 84
    techniciancomments text,             -- 85
    apptestisactive integer,             -- 86 (boolean converted to int)
    apptestdatecreated timestamp,        -- 87
    dateupdated timestamp,               -- 88
    setforreview integer,                -- 89 (boolean converted to int)
    reassigndocid integer,               -- 90
    userfullname text,                   -- 91
    assignedtouserid integer,            -- 92
    performedbyuserid integer,           -- 93
    permissionid integer,                -- 94
    isdoctorrequiredinoffice integer,    -- 95 (boolean converted to int)
    reassigndate timestamp,              -- 96
    email text,                          -- 97
    consentemail boolean                 -- 98
) AS $$
BEGIN
    -- Restored business logic from original SQL Server stored procedure
    DECLARE 
        v_is_active INT := 1;
        v_cancel_flag INT := 1; 
        v_exclude_test_flag INT := 1;
        v_triage_app_status INT := 16; -- Triage status
        v_exclude_cancel_id INT := 7;  -- App Status Canceled
        v_no_list_status INT := 1;     -- Skips Cancellation List (Id = 0) and Wait List (Id = 1)
        v_init_dt TIMESTAMP;
        v_end_dt TIMESTAMP;
        v_office_id_new INT;
        v_practice_id INT;
        v_total_records INT;
        
    BEGIN
        -- Handle appointment ID lookup or use provided office/date
        IF p_appointment_id IS NULL OR p_appointment_id = 0 THEN
            v_init_dt := date_trunc('day', p_selected_date);
            v_end_dt := date_trunc('day', p_selected_date) + INTERVAL '1 day' - INTERVAL '1 second';
            v_office_id_new := p_office_id;
        ELSE
            SELECT date_trunc('day', appointmenttime), 
                   date_trunc('day', appointmenttime) + INTERVAL '1 day' - INTERVAL '1 second',
                   officeid
            INTO v_init_dt, v_end_dt, v_office_id_new
            FROM dbo.appointments 
            WHERE id = p_appointment_id;
        END IF;
        
        -- Get practice ID
        SELECT dbo.office.practiceid INTO v_practice_id FROM dbo.office WHERE dbo.office.id = v_office_id_new;
        
        -- Get total record count for pagination
        SELECT COUNT(*) INTO v_total_records
        FROM dbo.appointments a
        JOIN dbo.office o ON a.officeid = o.id
        JOIN dbo.demographics d ON a.patientrecordid = d.patientrecordid AND d.active = 0
        LEFT JOIN dbo.appointmenttypes at ON a.appointmenttypeid = at.id
        WHERE a.appointmenttime BETWEEN v_init_dt AND v_end_dt
          AND a.officeid = v_office_id_new
          AND a.isactive = true
          AND (p_appointment_status IS NULL OR a.appointmentstatus = p_appointment_status)
          AND (p_appointment_id IS NULL OR p_appointment_id = 0 OR a.id = p_appointment_id)
          -- Apply filtering logic from original procedure
          AND (p_show_expected = true OR a.appointmentstatus > v_no_list_status)
          AND (p_exclude_cancelled = false OR a.appointmentstatus <> v_exclude_cancel_id)
          AND (p_only_action_on_abnormal = false OR a.actiononabnormal = true)
          AND (p_exclude_test_only = false OR COALESCE(at.appointmenttypeid, 0) <> v_exclude_test_flag)
          AND a.appointmentstatus <> v_triage_app_status
          AND (p_filter_patient IS NULL OR 
               LOWER(d.lastname) LIKE LOWER(p_filter_patient) || '%' OR 
               LOWER(d.firstname) LIKE LOWER(p_filter_patient) || '%')
          -- Test group filtering
          AND (array_length(p_test_group_ids, 1) IS NULL OR 
               EXISTS (SELECT 1 FROM dbo.appointmenttests apt2 
                      JOIN dbo.testgroups tg ON apt2.testid = tg.testid
                      WHERE apt2.appointmentid = a.id 
                        AND apt2.isactive = true
                        AND tg.groupid = ANY(p_test_group_ids)));

        RETURN QUERY
        WITH appointments_with_calcs AS (
            SELECT 
                a.id::integer,
                o.practiceid::integer,
                a.officeid::integer,
                a.appointmenttime AT TIME ZONE 'UTC',
                COALESCE(a.arrivedtime, '')::text,
                COALESCE(a.lefttime, '')::text,
                COALESCE(a.appointmentpurpose, '')::text,
                a.appointmentstatus::integer,
                COALESCE(a.appointmentnotes, '')::text,
                COALESCE(a.appointmentregistrar, 0)::integer,
                COALESCE(a.mwlurl, '')::text,
                COALESCE(a.mwlsentflag, false),
                COALESCE(a.actiononabnormal, false),
                COALESCE(a.bookingconfirmation, false),
                COALESCE(a.roomnumber, '')::text,
                COALESCE(a.practicedoctorid, 0)::integer,
                COALESCE(ed2.firstname || ' ' || ed2.lastname, '')::text as practice_doctor,
                -- Enhanced bill status logic from original procedure
                COALESCE((SELECT CASE WHEN COUNT(*) > 0 THEN 2 ELSE 0 END FROM dbo.billdetails WHERE appointmentid = a.id), 0)::integer as billstatusid,
                COALESCE(bs.name, '')::text,
                COALESCE(bs.color, '')::text,
                COALESCE(a.openingstatement, '')::text,
                COALESCE(a.referraldoctorid, 0)::integer,
                COALESCE(ed.firstname || ' ' || ed.lastname, '')::text as referral_doctor,
                COALESCE(ed.ohipphysicianid, '')::text,
                COALESCE(ed.cpso, '')::text,
                COALESCE(a.referraldoctoraddressid, 0)::integer,
                COALESCE(eda.addressline1, '')::text,
                COALESCE(a.referraldoctorphonenumberid, 0)::integer,
                COALESCE(edp.faxnumber, '')::text,
                -- Family doctor logic from original
                0::integer as familydoctorid,
                COALESCE((
                    SELECT STRING_AGG(
                        COALESCE(ed_fam.id::text, '') || '|' || 
                        COALESCE(ed_fam.ohipphysicianid, '') || '|' || 
                        COALESCE(ed_fam.cpso, '') || '|' || 
                        COALESCE(edp_fam.faxnumber, '') || '|' || 
                        COALESCE(eda_fam.addressline1, '') || '|' || 
                        COALESCE(ed_fam.firstname || ' ' || ed_fam.lastname, ''), '|'
                    )
                    FROM dbo.demographicsfamilydoctors fd
                    JOIN dbo.externaldoctors ed_fam ON fd.externaldoctorid = ed_fam.id
                    LEFT JOIN dbo.externaldoctoraddresses eda_fam ON ed_fam.id = eda_fam.externaldoctorid AND eda_fam.isactive = true
                    LEFT JOIN dbo.externaldoctorlocations edl_fam ON eda_fam.id = edl_fam.externaldoctoraddressid AND edl_fam.isactive = true  
                    LEFT JOIN dbo.externaldoctorphonenumbers edp_fam ON edl_fam.externaldoctorphonenumberid = edp_fam.id AND edp_fam.isactive = true
                    WHERE fd.demographicid = d.id AND fd.isactive = true AND fd.isremoved = false
                    LIMIT 1
                ), '')::text as familydoctor,
                -- Phone numbers as JSON from original
                COALESCE((
                    SELECT json_agg(phone_data.phone_obj ORDER BY phone_data.type_order, phone_data.active_order DESC, phone_data.id_order)::text
                    FROM (
                        SELECT 
                            json_build_object(
                                'phoneNumber', dpn.phonenumber,
                                'Type', CASE dpn.typeofphonenumber
                                           WHEN 0 THEN 'H'
                                           WHEN 1 THEN 'C'
                                           WHEN 2 THEN 'W'
                                           ELSE '' END,
                                'isPrimary', dpn.isactive
                            ) as phone_obj,
                            dpn.typeofphonenumber as type_order,
                            dpn.isactive as active_order,
                            dpn.id as id_order
                        FROM dbo.demographicsphonenumbers dpn
                        WHERE dpn.demographicid = d.id
                          AND dpn.phonenumber IS NOT NULL
                          AND TRIM(dpn.phonenumber) <> ''
                          AND dpn.isremoved = false
                    ) phone_data
                ), '[]')::text as patientphonenumbers,
                COALESCE(at2.id, 0)::integer,
                COALESCE(at2.name, '')::text,
                COALESCE(a.appointmenttypeid, 0)::integer,
                COALESCE(at.name, '')::text,
                COALESCE(a.appointmentconfirmation, 0)::integer,
                COALESCE(a.appointmentpaymentmethod, 0)::integer,
                COALESCE(a.triageurgencyid, 0)::integer,
                COALESCE(a.triagestatusid, 0)::integer,
                COALESCE(a.isactive, false),
                COALESCE(a.recordtype, 0)::smallint,
                a.patientrecordid::integer,
                (COALESCE(d.firstname, '') || CASE WHEN d.middlename IS NOT NULL THEN ' ' || d.middlename ELSE '' END)::text,
                COALESCE(d.lastname, '')::text,
                COALESCE(d.preferredname, '')::text,
                d.fhirid,
                d.dateofbirth,
                a.datecreated AT TIME ZONE 'UTC',
                a.lastmodified AT TIME ZONE 'UTC',
                -- Billing codes from original logic
                COALESCE(bill.consultcodeid, 0)::integer,
                COALESCE(bill.consultcode, '')::text,
                COALESCE(bill.diagnosticcodeid, 0)::integer,
                COALESCE(bill.diagnosticcode, '')::text,
                COALESCE(bill.diagnosticcodeid2, 0)::integer,
                COALESCE(bill.diagnosticcode2, '')::text,
                COALESCE(bill.diagnosticcodeid3, 0)::integer,
                COALESCE(bill.diagnosticcode3, '')::text,
                -- Calculated fields from original
                (SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END FROM dbo.demographicshealthcards 
                 WHERE demographicid = d.id AND TRIM(number) <> '')::integer as totalhealthcards,
                (SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END FROM dbo.reportreceivedappointments 
                 WHERE appointmentid = a.id)::integer as totalreferraldocuments,
                (SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END FROM dbo.doctorcomments 
                 WHERE dbo.doctorcomments.patientrecordid = a.patientrecordid)::integer as totaldoctorcomments,
                COALESCE((SELECT COUNT(*) FROM dbo.externaldoctorphonenumbers WHERE externaldoctorid = ed.id AND faxnumber IS NOT NULL), 0)::integer as totalreferraldocfax,
                COALESCE((SELECT COUNT(*) FROM dbo.externaldoctorphonenumbers WHERE externaldoctorid = ed.id AND phonenumber IS NOT NULL), 0)::integer as totalreferraldocnumbers,
                -- Doctor double booking calculation
                (SELECT COUNT(*) > 1 FROM dbo.appointments a2 
                 WHERE a2.officeid = a.officeid 
                   AND a2.appointmenttime = a.appointmenttime 
                   AND a2.practicedoctorid = a.practicedoctorid
                   AND a2.isactive = true) as isdoctordoublebook,
                -- VP booking calculation (from original logic)
                0::integer as totalvpbooked, -- Simplified for now
                false as isimported,
                v_total_records::integer,
                COALESCE(ap.priorityname, '')::text,
                COALESCE(a.appointmentpriorityid, 0)::integer,
                a.fhirid,
                COALESCE(apt.id, 0)::integer,
                COALESCE(apt.testid, 0)::integer,
                COALESCE(t.testfullname, '')::text,
                COALESCE(t.testshortname, '')::text,
                CASE WHEN t.requiredevice IS NULL THEN 0 ELSE (t.requiredevice::int) END,
                COALESCE(apt.starttime, '1900-01-01'::timestamp) AT TIME ZONE 'UTC',
                COALESCE(apt.appointmentteststatusid, 0)::integer,
                COALESCE(ats.color, '')::text,
                COALESCE(ats.status, '')::text,
                COALESCE(apt.testduration, 0)::integer,
                COALESCE(apt.billstatusid, 0)::integer,
                COALESCE(apt.referraldoctorid, 0)::integer,
                COALESCE(apt.accessionnumber, '')::text,
                COALESCE(apt.physiciancomments, '')::text,
                COALESCE(apt.techniciancomments, '')::text,
                CASE WHEN apt.isactive IS NULL THEN 0 ELSE (apt.isactive::int) END,
                COALESCE(apt.datecreated, '1900-01-01'::timestamp) AT TIME ZONE 'UTC',
                COALESCE(apt.dateupdated, '1900-01-01'::timestamp) AT TIME ZONE 'UTC',
                CASE WHEN apt.setforreview IS NULL THEN 0 ELSE (apt.setforreview::int) END,
                COALESCE(apt.reassigndocid, 0)::integer,
                COALESCE(u.firstname || ' ' || u.lastname, '')::text,
                COALESCE(atr.assignedtouserid, 0)::integer,
                COALESCE(atr.performedbyuserid, 0)::integer,
                COALESCE(atr.permissionid, 0)::integer,
                CASE WHEN atr.isdoctorrequiredinoffice IS NULL THEN 0 ELSE (atr.isdoctorrequiredinoffice::int) END,
                COALESCE(apt.reassigndate, '1900-01-01'::timestamp) AT TIME ZONE 'UTC',
                COALESCE(d.email, '')::text,
                COALESCE(d.consentemail, false)
                
            FROM dbo.appointments a
            JOIN dbo.office o ON a.officeid = o.id
            JOIN dbo.demographics d ON a.patientrecordid = d.patientrecordid AND d.active = 0
            LEFT JOIN dbo.appointmenttypes at ON a.appointmenttypeid = at.id
            LEFT JOIN dbo.appointmenttypes at2 ON at.appointmenttypeid = at2.id
            LEFT JOIN dbo.practicedoctors pd ON a.practicedoctorid = pd.id
            LEFT JOIN dbo.externaldoctors ed2 ON pd.externaldoctorid = ed2.id
            LEFT JOIN dbo.externaldoctors ed ON a.referraldoctorid = ed.id
            LEFT JOIN dbo.externaldoctoraddresses eda ON a.referraldoctoraddressid = eda.id AND eda.isactive = true
            LEFT JOIN dbo.externaldoctorphonenumbers edp ON a.referraldoctorphonenumberid = edp.id
            LEFT JOIN dbo.billstatus bs ON a.billstatusid = bs.id
            LEFT JOIN dbo.appointmentpriority ap ON a.appointmentpriorityid = ap.id
            LEFT JOIN dbo.appointmenttests apt ON a.id = apt.appointmentid AND apt.isactive = true
            LEFT JOIN dbo.appointmenttestresources atr ON apt.id = atr.appointmenttestid AND atr.isactive = true
            LEFT JOIN dbo.aspnetusers u ON atr.assignedtouserid = u.userid
            LEFT JOIN dbo.tests t ON apt.testid = t.id
            LEFT JOIN dbo.appointmentteststatus ats ON apt.appointmentteststatusid = ats.id
            -- Billing join from original
            LEFT JOIN (
                SELECT appointmentid, bill1.consultcode as consultcodeid, bill1.diagnosticcode as diagnosticcodeid,
                       bill1.diagnosticcode2 as diagnosticcodeid2, bill1.diagnosticcode3 as diagnosticcodeid3,
                       -- Consult codes concatenation logic
                       (SELECT STRING_AGG(cc.code, ',') FROM dbo.consultcodes cc 
                        WHERE cc.id IN (bill1.consultcode, bill1.consultcode2, bill1.consultcode3)
                          AND cc.id IS NOT NULL) as consultcode,
                       -- Diagnostic codes lookup
                       (SELECT diagnosis FROM dbo.diagnosecodes WHERE dbo.diagnosecodes.id = bill1.diagnosticcode) as diagnosticcode,
                       (SELECT diagnosis FROM dbo.diagnosecodes WHERE dbo.diagnosecodes.id = bill1.diagnosticcode2) as diagnosticcode2,
                       (SELECT diagnosis FROM dbo.diagnosecodes WHERE dbo.diagnosecodes.id = bill1.diagnosticcode3) as diagnosticcode3
                FROM dbo.appointmentbills bill1
            ) bill ON a.id = bill.appointmentid
            WHERE a.appointmenttime BETWEEN v_init_dt AND v_end_dt
              AND a.officeid = v_office_id_new
              AND a.isactive = true
              AND (p_appointment_status IS NULL OR a.appointmentstatus = p_appointment_status)
              AND (p_appointment_id IS NULL OR p_appointment_id = 0 OR a.id = p_appointment_id)
              -- Business logic filters from original procedure
              AND (p_show_expected = true OR a.appointmentstatus > v_no_list_status)
              AND (p_exclude_cancelled = false OR a.appointmentstatus <> v_exclude_cancel_id)
              AND (p_only_action_on_abnormal = false OR a.actiononabnormal = true)
              AND (p_exclude_test_only = false OR COALESCE(at.appointmenttypeid, 0) <> v_exclude_test_flag)
              AND a.appointmentstatus <> v_triage_app_status
              AND (p_filter_patient IS NULL OR 
                   LOWER(d.lastname) LIKE LOWER(p_filter_patient) || '%' OR 
                   LOWER(d.firstname) LIKE LOWER(p_filter_patient) || '%')
              -- Test group filtering
              AND (array_length(p_test_group_ids, 1) IS NULL OR 
                   EXISTS (SELECT 1 FROM dbo.appointmenttests apt2 
                          JOIN dbo.testgroups tg ON apt2.testid = tg.testid
                          WHERE apt2.appointmentid = a.id 
                            AND apt2.isactive = true
                            AND tg.groupid = ANY(p_test_group_ids)))
            ORDER BY a.id ASC, apt.starttime ASC
            LIMIT p_page_size OFFSET ((p_page_num - 1) * p_page_size)
        )
        SELECT * FROM appointments_with_calcs;
    END;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.GetDaysheetAppointmentTests_v4 TO postgres;