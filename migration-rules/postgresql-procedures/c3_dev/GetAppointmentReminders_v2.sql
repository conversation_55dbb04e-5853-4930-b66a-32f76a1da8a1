-- Migration: GetAppointmentReminders_v2 stored procedure to PostgreSQL
-- Purpose: Get appointment reminders with enhanced filtering and batch processing
-- Migrated from: SQL Server stored procedure [dbo].[GetAppointmentReminders_v2]

CREATE OR REPLACE FUNCTION dbo.GetAppointmentReminders_v2(
    p_practice_id INTEGER,
    p_office_id INTEGER DEFAULT NULL,
    p_reminder_date TIMESTAMP DEFAULT NULL,
    p_reminder_type INTEGER DEFAULT NULL,
    p_batch_size INTEGER DEFAULT 100
) 
RETURNS TABLE (
    AppointmentId INTEGER,
    PatientId INTEGER,
    PracticeId INTEGER,
    OfficeId INTEGER,
    AppointmentTime TIMESTAMP,
    PatientFirstName VARCHAR(100),
    PatientLastName VARCHAR(100),
    PatientPhone VARCHAR(50),
    PatientEmail VARCHAR(255),
    AppointmentStatus INTEGER,
    AppointmentType VARCHAR(100),
    DoctorName VARCHAR(200),
    <PERSON>Name VARCHAR(100),
    LastReminderSent TIMESTAMP,
    <PERSON>minderType INTEGER,
    IsReminderEnabled BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.Id::INTEGER as AppointmentId,
        a.PatientRecordId::INTEGER as PatientId,
        a.PracticeId::INTEGER,
        a.OfficeId::INTEGER,
        a.appointmentTime as AppointmentTime,
        pd.firstName as PatientFirstName,
        pd.lastName as PatientLastName,
        COALESCE(pp.phoneNumber, '') as PatientPhone,
        COALESCE(pd.emailAddress, '') as PatientEmail,
        a.appointmentStatus::INTEGER as AppointmentStatus,
        COALESCE(at.appointmentType, '') as AppointmentType,
        COALESCE(pdu.firstName || ' ' || pdu.lastName, '') as DoctorName,
        COALESCE(o.officeName, '') as OfficeName,
        ar.lastReminderSent as LastReminderSent,
        COALESCE(ar.reminderType, 0)::INTEGER as ReminderType,
        COALESCE(ps.enableReminders, false) as IsReminderEnabled
    FROM Appointments a
    INNER JOIN PatientDemographics pd ON a.PatientRecordId = pd.patientRecordId
    LEFT JOIN PatientPhoneNumbers pp ON pd.patientRecordId = pp.patientRecordId AND pp.isActive = true
    LEFT JOIN AppointmentTypes at ON a.AppointmentTypeId = at.Id
    LEFT JOIN PracticeDoctors pdr ON a.PracticeDoctorId = pdr.Id
    LEFT JOIN PracticeDoctorUsers pdu ON pdr.PracticeDoctorUserId = pdu.Id
    LEFT JOIN Offices o ON a.OfficeId = o.Id
    LEFT JOIN AppointmentReminders ar ON a.Id = ar.appointmentId
    LEFT JOIN PracticeSettings ps ON a.PracticeId = ps.practiceId
    WHERE a.PracticeId = p_practice_id
        AND (p_office_id IS NULL OR a.OfficeId = p_office_id)
        AND (p_reminder_date IS NULL OR DATE(a.appointmentTime) = DATE(p_reminder_date))
        AND (p_reminder_type IS NULL OR COALESCE(ar.reminderType, 0) = p_reminder_type)
        AND a.appointmentStatus IN (1, 2) -- Active appointment statuses
        AND a.appointmentTime > NOW() -- Future appointments only
        AND COALESCE(ps.enableReminders, false) = true
        AND (ar.lastReminderSent IS NULL OR ar.lastReminderSent < NOW() - INTERVAL '24 hours') -- Not reminded in last 24 hours
    ORDER BY 
        CASE WHEN ar.reminderType = 1 THEN 1 ELSE 2 END, -- Priority: email first, then SMS
        a.appointmentTime ASC
    LIMIT p_batch_size;
END;
$$ LANGUAGE plpgsql;