-- PostgreSQL function migration of SP_VP_GetDoctorByUserId stored procedure
-- Original: SP_VP_GetDoctorByUserId(@practiceId INT, @userId INT)
-- Purpose: Gets doctor information by user ID for a specific practice

CREATE OR REPLACE FUNCTION dbo.SP_VP_GetDoctorByUserId(
    p_practice_id INTEGER,
    p_user_id INTEGER
)
RETURNS TABLE(
    userid integer,
    name varchar(200),
    lastname varchar(100),
    firstname varchar(100),
    externaldoctorid integer,
    practicedoctorid integer
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.userid,
        (e.lastname || ' ' || e.firstname) as name,
        e.lastname,
        e.firstname,
        e.id as externaldoctorid,
        p.id as practicedoctorid
    FROM dbo.aspnetusers u
    JOIN dbo.practicedoctors p ON u.id = p.applicationuserid
    JOIN dbo.externaldoctors e ON p.externaldoctorid = e.id
    WHERE p.practiceid = p_practice_id 
      AND u.userid = p_user_id
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;