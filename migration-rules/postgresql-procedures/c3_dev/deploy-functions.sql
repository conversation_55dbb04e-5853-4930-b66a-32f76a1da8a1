-- Deployment script for PostgreSQL functions
-- This script creates all necessary schemas and functions for PostgreSQL

-- Ensure dbo schema exists (common pattern from SQL Server migration)
CREATE SCHEMA IF NOT EXISTS dbo;

-- Grant usage on schema to the application user (adjust username as needed)
-- GRANT USAGE ON SCHEMA dbo TO your_app_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA dbo TO your_app_user;

-- Include all function definitions
\i GetUserMenuCount.sql

-- Add other function scripts here as they are created
-- \i AnotherFunction.sql
-- \i YetAnotherFunction.sql

-- Verify functions were created
SELECT 
    n.nspname AS schema_name,
    p.proname AS function_name,
    pg_catalog.pg_get_function_identity_arguments(p.oid) AS arguments
FROM pg_catalog.pg_proc p
LEFT JOIN pg_catalog.pg_namespace n ON n.oid = p.pronamespace
WHERE n.nspname = 'dbo'
ORDER BY function_name;