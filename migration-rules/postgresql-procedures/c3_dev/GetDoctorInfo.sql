-- PostgreSQL function equivalent of SQL Server GetDoctorInfo stored procedure
-- This function gets doctor information by user ID

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create GetDoctorInfo function
CREATE OR REPLACE FUNCTION dbo.GetDoctorInfo(
    p_user_id INTEGER
)
RETURNS TABLE(
    practicedoctorid bigint,
    externaldoctorid integer,
    firstname text,
    lastname text,
    fullname text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p_doc.id,
        p_doc.externaldoctorid,
        ex_doc.firstname::text,
        ex_doc.lastname::text,
        (ex_doc.lastname || ' ' || ex_doc.firstname)::text
    FROM dbo.practicedoctors p_doc
    JOIN dbo.externaldoctors ex_doc ON p_doc.externaldoctorid = ex_doc.id
    JOIN dbo.aspnetusers apu ON apu.id = p_doc.applicationuserid
    WHERE apu.userid = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.GetDoctorInfo TO postgres;