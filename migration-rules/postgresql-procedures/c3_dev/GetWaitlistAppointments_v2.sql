-- Migration: GetWaitlistAppointments_v2 stored procedure to PostgreSQL
-- Purpose: Get appointments on the waitlist for scheduling
-- Migrated from: SQL Server stored procedure [dbo].[GetWaitlistAppointments_v2]

CREATE OR REPLACE FUNCTION dbo.GetWaitlistAppointments_v2(
    p_practice_id INTEGER,
    p_office_id INTEGER DEFAULT NULL,
    p_doctor_id INTEGER DEFAULT NULL,
    p_from_date TIMESTAMP DEFAULT NULL,
    p_to_date TIMESTAMP DEFAULT NULL
) 
RETURNS TABLE (
    WaitlistId INTEGER,
    PatientId INTEGER,
    PracticeId INTEGER,
    OfficeId INTEGER,
    PatientFirstName VARCHAR(100),
    PatientLastName VARCHAR(100),
    PatientPhone VARCHAR(50),
    RequestedDate TIMESTAMP,
    PreferredStartTime TIMESTAMP,
    PreferredEndTime TIMESTAMP,
    PracticeDoctorId INTEGER,
    DoctorName VARCHAR(200),
    AppointmentTypeId INTEGER,
    AppointmentType VARCHAR(100),
    Priority INTEGER,
    CreatedDate TIMESTAMP,
    Notes TEXT,
    IsActive BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        w.Id::INTEGER as WaitlistId,
        w.PatientRecordId::INTEGER as PatientId,
        w.PracticeId::INTEGER,
        w.OfficeId::INTEGER,
        pd.firstName as PatientFirstName,
        pd.lastName as PatientLastName,
        COALESCE(pp.phoneNumber, '') as PatientPhone,
        w.requestedDate as RequestedDate,
        w.preferredStartTime as PreferredStartTime,
        w.preferredEndTime as PreferredEndTime,
        w.PracticeDoctorId::INTEGER,
        COALESCE(pdu.firstName || ' ' || pdu.lastName, '') as DoctorName,
        w.AppointmentTypeId::INTEGER,
        COALESCE(at.appointmentType, '') as AppointmentType,
        COALESCE(w.priority, 2)::INTEGER as Priority, -- Default to medium priority
        w.createdDate as CreatedDate,
        COALESCE(w.notes, '') as Notes,
        COALESCE(w.isActive, true) as IsActive
    FROM WaitlistAppointments w
    INNER JOIN PatientDemographics pd ON w.PatientRecordId = pd.patientRecordId
    LEFT JOIN PatientPhoneNumbers pp ON pd.patientRecordId = pp.patientRecordId AND pp.isActive = true
    LEFT JOIN AppointmentTypes at ON w.AppointmentTypeId = at.Id
    LEFT JOIN PracticeDoctors pdr ON w.PracticeDoctorId = pdr.Id
    LEFT JOIN PracticeDoctorUsers pdu ON pdr.PracticeDoctorUserId = pdu.Id
    WHERE w.PracticeId = p_practice_id
        AND COALESCE(w.isActive, true) = true
        AND (p_office_id IS NULL OR w.OfficeId = p_office_id)
        AND (p_doctor_id IS NULL OR w.PracticeDoctorId = p_doctor_id)
        AND (p_from_date IS NULL OR w.requestedDate >= p_from_date)
        AND (p_to_date IS NULL OR w.requestedDate <= p_to_date)
    ORDER BY 
        COALESCE(w.priority, 2) ASC, -- Higher priority (lower number) first
        w.createdDate ASC; -- Older requests first for same priority
END;
$$ LANGUAGE plpgsql;