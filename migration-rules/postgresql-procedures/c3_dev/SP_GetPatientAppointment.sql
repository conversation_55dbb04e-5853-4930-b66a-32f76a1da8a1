-- Migration: SP_GetPatientAppointment stored procedure to PostgreSQL
-- Purpose: Get detailed information for a specific patient appointment
-- Migrated from: SQL Server stored procedure [dbo].[SP_GetPatientAppointment]

CREATE OR REPLACE FUNCTION dbo.SP_GetPatientAppointment(
    p_appointment_id INTEGER,
    p_patient_id INTEGER DEFAULT NULL
) 
RETURNS TABLE (
    AppointmentId INTEGER,
    PatientId INTEGER,
    PracticeId INTEGER,
    OfficeId INTEGER,
    AppointmentTime TIMESTAMP,
    Duration INTEGER,
    AppointmentStatus INTEGER,
    AppointmentTypeId INTEGER,
    AppointmentType VARCHAR(100),
    PracticeDoctorId INTEGER,
    DoctorName VARCHAR(200),
    Notes TEXT,
    Reason VARCHAR(500),
    CreatedDate TIMESTAMP,
    ModifiedDate TIMESTAMP,
    IsVirtualVisit BOOLEAN,
    RoomNumber VARCHAR(50)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.Id::INTEGER as AppointmentId,
        a.PatientRecordId::INTEGER as PatientId,
        a.PracticeId::INTEGER,
        a.OfficeId::INTEGER,
        a.appointmentTime as AppointmentTime,
        COALESCE(a.duration, 30)::INTEGER as Duration,
        a.appointmentStatus::INTEGER as AppointmentStatus,
        a.AppointmentTypeId::INTEGER,
        COALESCE(at.appointmentType, '') as AppointmentType,
        a.PracticeDoctorId::INTEGER,
        COALESCE(pdu.firstName || ' ' || pdu.lastName, '') as DoctorName,
        COALESCE(a.notes, '') as Notes,
        COALESCE(a.reason, '') as Reason,
        a.createdDate as CreatedDate,
        a.modifiedDate as ModifiedDate,
        COALESCE(a.isVirtualVisit, false) as IsVirtualVisit,
        COALESCE(r.roomNumber, '') as RoomNumber
    FROM Appointments a
    LEFT JOIN AppointmentTypes at ON a.AppointmentTypeId = at.Id
    LEFT JOIN PracticeDoctors pdr ON a.PracticeDoctorId = pdr.Id
    LEFT JOIN PracticeDoctorUsers pdu ON pdr.PracticeDoctorUserId = pdu.Id
    LEFT JOIN Rooms r ON a.roomId = r.Id
    WHERE a.Id = p_appointment_id
        AND (p_patient_id IS NULL OR a.PatientRecordId = p_patient_id);
END;
$$ LANGUAGE plpgsql;