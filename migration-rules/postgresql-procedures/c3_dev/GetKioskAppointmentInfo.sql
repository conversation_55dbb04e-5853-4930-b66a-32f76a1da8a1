-- PostgreSQL function equivalent of SQL Server GetKioskAppointmentInfo stored procedure
-- This function gets kiosk appointment information by health card and IP address

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create GetKioskAppointmentInfo function
CREATE OR REPLACE FUNCTION dbo.GetKioskAppointmentInfo(
    p_healthcard VARCHAR(100),
    p_ip_address VARCHAR(100)
)
RETURNS TABLE(
    appointmentid integer,
    appointmenttime timestamp,
    officeid integer,
    practiceid integer,
    patientid integer,
    demographicid integer,
    addressline1 text,
    addressline2 text,
    city text,
    postalcode text,
    province text,
    country text,
    healthcard text,
    healthcardexpirydate timestamp,
    hasaddress boolean,
    familydoctor text,
    messagecode integer
) AS $$
DECLARE
    v_current_date TIMESTAMP := NOW();
    v_expiry_date TIMESTAMP := NULL;
    v_kiosk_message_server_error_code INTEGER := 5;
    v_practice_id INTEGER := 0;
    v_patient_id INTEGER := 0;
    v_demographic_id INTEGER := 0;
    v_address_line1 TEXT;
    v_address_line2 TEXT;
    v_city TEXT;
    v_postal_code TEXT;
    v_province TEXT;
    v_country TEXT;
    v_family_doctor TEXT;
BEGIN
    -- Get patient ID from health card
    SELECT COALESCE(d.patientrecordid, 0) INTO v_patient_id
    FROM dbo.demographicshealthcards hc 
    JOIN dbo.demographics d ON hc.demographicid = d.id
    WHERE hc.number = p_healthcard
    LIMIT 1;

    -- Get patient info using fn_GetPatientInfo equivalent logic
    IF v_patient_id > 0 THEN
        SELECT 
            da.addressline1,
            da.addressline2,
            da.city,
            da.postalcode,
            da.province,
            da.country,
            d.id,
            hc.expirydate,
            pr.practiceid
        INTO 
            v_address_line1,
            v_address_line2,
            v_city,
            v_postal_code,
            v_province,
            v_country,
            v_demographic_id,
            v_expiry_date,
            v_practice_id
        FROM dbo.patientrecords pr
        JOIN dbo.demographics d ON pr.id = d.patientrecordid
        LEFT JOIN dbo.demographicsaddresses da ON da.demographicid = d.id AND da.isactive = true
        LEFT JOIN dbo.demographicshealthcards hc ON hc.demographicid = d.id AND hc.number = p_healthcard
        WHERE pr.id = v_patient_id
        ORDER BY da.id DESC, hc.id DESC
        LIMIT 1;
    END IF;

    -- Return appointments with kiosk info
    RETURN QUERY
    SELECT 
        apps.id::integer,
        apps.appointmenttime AT TIME ZONE 'UTC',
        apps.officeid,
        v_practice_id,
        v_patient_id,
        v_demographic_id,
        COALESCE(v_address_line1, '')::text,
        COALESCE(v_address_line2, '')::text,
        COALESCE(v_city, '')::text,
        COALESCE(v_postal_code, '')::text,
        COALESCE(v_province, '')::text,
        COALESCE(v_country, '')::text,
        p_healthcard::text,
        v_expiry_date AT TIME ZONE 'UTC',
        CASE 
            WHEN v_address_line1 IS NOT NULL AND TRIM(v_address_line1) != '' 
            THEN true 
            ELSE false 
        END,
        -- Family Doctor
        COALESCE(
            (SELECT COALESCE(ed.firstname, '') || ' ' || COALESCE(ed.lastname, '')
             FROM dbo.externaldoctors ed 
             JOIN dbo.demographicsfamilydoctors fd ON ed.id = fd.externaldoctorid
             WHERE fd.demographicid = v_demographic_id 
             AND fd.isactive = true 
             AND fd.isremoved = false
             ORDER BY fd.id DESC 
             LIMIT 1), 
            ''
        )::text,
        -- Message Code
        CASE 
            WHEN k.kioskmessageid > 0 THEN 
                COALESCE(
                    (SELECT m.code 
                     FROM dbo.kioskmessages m 
                     WHERE m.id = k.kioskmessageid 
                     LIMIT 1), 
                    0
                ) 
            ELSE 0 
        END::integer
    FROM dbo.appointments apps
    JOIN dbo.kioskipaddresses kip ON apps.officeid = kip.officeid
    LEFT JOIN dbo.kioskcheckins k ON apps.id = k.appointmentid
    WHERE DATE(apps.appointmenttime) = DATE(v_current_date)
    AND apps.patientrecordid = v_patient_id
    AND kip.ipaddress = p_ip_address
    AND apps.isactive = true;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.GetKioskAppointmentInfo TO postgres;