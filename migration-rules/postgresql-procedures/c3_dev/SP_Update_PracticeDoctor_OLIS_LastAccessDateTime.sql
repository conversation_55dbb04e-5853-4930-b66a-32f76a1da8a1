CREATE OR REPLACE FUNCTION dbo.sp_update_practicedoctor_olis_lastaccessdatetime(
    p_practice_doctor_id INTEGER,
    p_last_update_datetime TIMESTAMP
)
RETURNS INTEGER AS $$
DECLARE
    rows_updated INTEGER;
BEGIN
    UPDATE dbo.PracticeDoctors 
    SET OLIS_LastUpdate = p_last_update_datetime AT TIME ZONE 'UTC'
    WHERE Id = p_practice_doctor_id;
    
    GET DIAGNOSTICS rows_updated = ROW_COUNT;
    RETURN rows_updated;
END;
$$ LANGUAGE plpgsql;