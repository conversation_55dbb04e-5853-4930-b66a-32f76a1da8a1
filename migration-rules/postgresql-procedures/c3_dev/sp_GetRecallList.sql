CREATE OR REPLACE FUNCTION dbo.sp_GetRecallList(
    p_doctor_id INTEGER,
    p_immunization_type_id INTEGER,
    p_status INTEGER DEFAULT NULL,
    p_rostered BOOLEAN DEFAULT false
)
RETURNS TABLE(
    patientrecordid INTEGER,
    firstname TEXT,
    lastname TEXT,
    phonenumber TEXT,
    recallid INTEGER,
    vp_cpp_immunization_id INTEGER,
    vp_cpp_immunizationstatusid_laststatus INTEGER,
    dateservicedday INTEGER,
    dateservicedmonth INTEGER,
    dateservicedyear INTEGER,
    contactedbyphone BOOLEAN,
    administeredby TEXT,
    table_of_doctors_names TEXT,
    immunizationsystem TEXT,
    refuse BOOLEAN,
    immunizationdate TIMESTAMP,
    immunizationrefuseddate TIMESTAMP,
    name TEXT,
    notes TEXT,
    manufacturer TEXT,
    lotnumber TEXT,
    route TEXT,
    site TEXT,
    dose TEXT,
    instructions TEXT,
    nextdate TIMESTAMP,
    notremind BOOLEAN,
    reasonnextdate TEXT,
    codingvocabulary TEXT,
    immunizationcode TEXT,
    reasonfordel TEXT,
    submitdate TIMESTAMP,
    isactive BOOLEAN,
    setid INTEGER,
    colonoscopy BOOLEAN,
    parentid INTEGER,
    vp_cpp_immunizationtypeid INTEGER,
    vp_cpp_immunizationstatusid INTEGER,
    contactedbyphonedate TIMESTAMP,
    doseunit TEXT,
    immunizationday INTEGER,
    immunizationmonth INTEGER,
    immunizationyear INTEGER,
    dateoflastprocedure TIMESTAMP
) AS $$
DECLARE
    v_age INTEGER;
    v_gender INTEGER;
    v_operator INTEGER;
    v_age_from INTEGER;
    v_age_to INTEGER;
    v_age_as_of_date TIMESTAMP;
    v_age_category TEXT;
    v_frequency INTEGER;
    v_date_cuttoff_service TIMESTAMP;
BEGIN
    -- Get immunization type parameters
    SELECT t.AgeFrom, t.AgeTo, t.DateFrom, t.DateTo, t.Operator, t.Gender, t.Period, t.agecategory
    INTO v_age_from, v_age_to, v_age_as_of_date, v_date_cuttoff_service, v_operator, v_gender, v_frequency, v_age_category
    FROM dbo.VP_CPP_ImmunizationType t
    WHERE t.Id = p_immunization_type_id;

    -- Hardcoded for Influenza (due seasonal requirement)
    IF p_immunization_type_id = 2 THEN
        v_frequency := 4; -- 4 months
    END IF;

    IF NOT p_rostered THEN
        -- Enrolled demographics
        IF v_age_category = 'Routine Infants & Children' THEN
            RETURN QUERY
            WITH target_population AS (
                SELECT 
                    d.patientrecordid,
                    d.firstname,
                    d.lastname,
                    pn.phonenumber
                FROM dbo.Demographics d 
                JOIN dbo.DemographicsMainResponsiblePhysicians mrp ON d.Id = mrp.DemographicId
                JOIN dbo.DemographicsEnrollments de ON de.DemographicsMRPId = mrp.Id
                LEFT JOIN (
                    SELECT DISTINCT ON (demographicid) 
                        demographicid, 
                        phonenumber
                    FROM dbo.DemographicsPhoneNumbers
                    ORDER BY demographicid, isactive DESC, id DESC
                ) pn ON pn.demographicid = d.Id
                WHERE mrp.PracticeDoctorId = p_doctor_id
                AND d.active = false  -- Means TRUE in this system
                AND d.gender = CASE WHEN v_gender = 2 THEN d.gender ELSE v_gender END
                AND COALESCE(de.enrollmentdate, v_date_cuttoff_service) <= v_date_cuttoff_service
                AND COALESCE(de.enrollmentterminationdate, v_date_cuttoff_service + INTERVAL '1 day') > v_date_cuttoff_service
                AND EXTRACT(MONTH FROM AGE(v_date_cuttoff_service, d.dateofbirth)) >= v_age_from 
                AND EXTRACT(MONTH FROM AGE(v_date_cuttoff_service, d.dateofbirth)) <= v_age_to
            )
            SELECT 
                tp.patientrecordid,
                tp.firstname,
                tp.lastname,
                tp.phonenumber,
                r.recallid::INTEGER,
                r.vp_cpp_immunization_id::INTEGER,
                r.vp_cpp_immunizationstatusid_laststatus::INTEGER,
                r.dateservicedday::INTEGER,
                r.dateservicedmonth::INTEGER,
                r.dateservicedyear::INTEGER,
                r.contactedbyphone,
                r.administeredby,
                r.table_of_doctors_names,
                r.immunizationsystem,
                r.refuse,
                r.immunizationdate,
                r.immunizationrefuseddate,
                r.name,
                r.notes,
                r.manufacturer,
                r.lotnumber,
                r.route,
                r.site,
                r.dose,
                r.instructions,
                r.nextdate,
                r.notremind,
                r.reasonnextdate,
                r.codingvocabulary,
                r.immunizationcode,
                r.reasonfordel,
                r.submitdate,
                r.isactive,
                r.setid::INTEGER,
                r.colonoscopy,
                r.parentid::INTEGER,
                r.vp_cpp_immunizationtypeid::INTEGER,
                r.vp_cpp_immunizationstatusid::INTEGER,
                r.contactedbyphonedate,
                r.doseunit,
                r.immunizationday::INTEGER,
                r.immunizationmonth::INTEGER,
                r.immunizationyear::INTEGER,
                r.dateoflastprocedure
            FROM target_population tp 
            JOIN (
                SELECT DISTINCT ON (i.patientrecordid)
                    ir.id AS recallid,
                    ir.vp_cpp_immunization_id,
                    ir.vp_cpp_immunizationstatusid AS vp_cpp_immunizationstatusid_laststatus,
                    ir.dateservicedday,
                    ir.dateservicedmonth,
                    ir.dateservicedyear,
                    i.contactedbyphone,
                    i.administeredby,
                    i.table_of_doctors_names,
                    i.immunizationsystem,
                    i.refuse,
                    i.immunizationdate,
                    i.immunizationrefuseddate,
                    i.name,
                    i.notes,
                    i.manufacturer,
                    i.lotnumber,
                    i.route,
                    i.site,
                    i.dose,
                    i.instructions,
                    i.nextdate,
                    i.notremind,
                    i.reasonnextdate,
                    i.codingvocabulary,
                    i.immunizationcode,
                    i.reasonfordel,
                    i.submitdate,
                    i.isactive,
                    i.setid,
                    i.colonoscopy,
                    i.parentid,
                    i.vp_cpp_immunizationtypeid,
                    i.vp_cpp_immunizationstatusid,
                    i.contactedbyphonedate,
                    i.doseunit,
                    i.immunizationday,
                    i.immunizationmonth,
                    i.immunizationyear,
                    i.patientrecordid,
                    CASE 
                        WHEN ir.vp_cpp_immunizationstatusid = 3 THEN ir.datecreated
                        WHEN ir.vp_cpp_immunizationstatusid = 4 THEN ir.datecreated
                        WHEN ir.vp_cpp_immunizationstatusid = 7 THEN ir.datecreated
                        ELSE NULL 
                    END AS dateoflastprocedure
                FROM dbo.VP_CPP_Immunization i 
                JOIN dbo.ImmunizationRecalls ir ON ir.vp_cpp_immunization_id = i.id 
                WHERE i.vp_cpp_immunizationtypeid = p_immunization_type_id
                AND ir.active = true
                ORDER BY i.patientrecordid, ir.datecreated DESC
            ) r ON r.patientrecordid = tp.patientrecordid
            WHERE (p_status IS NULL OR r.vp_cpp_immunizationstatusid_laststatus = p_status)
            ORDER BY tp.lastname;

        ELSE
            -- Non-routine age category for enrolled patients
            RETURN QUERY
            WITH target_population AS (
                SELECT 
                    d.patientrecordid,
                    d.firstname,
                    d.lastname,
                    pn.phonenumber
                FROM dbo.Demographics d 
                JOIN dbo.DemographicsMainResponsiblePhysicians mrp ON d.Id = mrp.DemographicId
                JOIN dbo.DemographicsEnrollments de ON de.DemographicsMRPId = mrp.Id
                LEFT JOIN (
                    SELECT DISTINCT ON (demographicid) 
                        demographicid, 
                        phonenumber
                    FROM dbo.DemographicsPhoneNumbers
                    ORDER BY demographicid, isactive DESC, id DESC
                ) pn ON pn.demographicid = d.Id
                WHERE mrp.PracticeDoctorId = p_doctor_id
                AND d.active = false  -- Means TRUE in this system
                AND d.gender = CASE WHEN v_gender = 2 THEN d.gender ELSE v_gender END
                AND COALESCE(de.enrollmentdate, v_date_cuttoff_service) <= v_date_cuttoff_service
                AND COALESCE(de.enrollmentterminationdate, v_date_cuttoff_service + INTERVAL '1 day') > v_date_cuttoff_service
                AND EXTRACT(YEAR FROM AGE(v_age_as_of_date, d.dateofbirth)) >= v_age_from 
                AND EXTRACT(YEAR FROM AGE(v_age_as_of_date, d.dateofbirth)) <= v_age_to
            )
            SELECT 
                tp.patientrecordid,
                tp.firstname,
                tp.lastname,
                tp.phonenumber,
                r.recallid::INTEGER,
                r.vp_cpp_immunization_id::INTEGER,
                r.vp_cpp_immunizationstatusid_laststatus::INTEGER,
                r.dateservicedday::INTEGER,
                r.dateservicedmonth::INTEGER,
                r.dateservicedyear::INTEGER,
                r.contactedbyphone,
                r.administeredby,
                r.table_of_doctors_names,
                r.immunizationsystem,
                r.refuse,
                r.immunizationdate,
                r.immunizationrefuseddate,
                r.name,
                r.notes,
                r.manufacturer,
                r.lotnumber,
                r.route,
                r.site,
                r.dose,
                r.instructions,
                r.nextdate,
                r.notremind,
                r.reasonnextdate,
                r.codingvocabulary,
                r.immunizationcode,
                r.reasonfordel,
                r.submitdate,
                r.isactive,
                r.setid::INTEGER,
                r.colonoscopy,
                r.parentid::INTEGER,
                r.vp_cpp_immunizationtypeid::INTEGER,
                r.vp_cpp_immunizationstatusid::INTEGER,
                r.contactedbyphonedate,
                r.doseunit,
                r.immunizationday::INTEGER,
                r.immunizationmonth::INTEGER,
                r.immunizationyear::INTEGER,
                r.dateoflastprocedure
            FROM target_population tp 
            JOIN (
                SELECT DISTINCT ON (i.patientrecordid)
                    ir.id AS recallid,
                    ir.vp_cpp_immunization_id,
                    ir.vp_cpp_immunizationstatusid AS vp_cpp_immunizationstatusid_laststatus,
                    ir.dateservicedday,
                    ir.dateservicedmonth,
                    ir.dateservicedyear,
                    i.contactedbyphone,
                    i.administeredby,
                    i.table_of_doctors_names,
                    i.immunizationsystem,
                    i.refuse,
                    i.immunizationdate,
                    i.immunizationrefuseddate,
                    i.name,
                    i.notes,
                    i.manufacturer,
                    i.lotnumber,
                    i.route,
                    i.site,
                    i.dose,
                    i.instructions,
                    i.nextdate,
                    i.notremind,
                    i.reasonnextdate,
                    i.codingvocabulary,
                    i.immunizationcode,
                    i.reasonfordel,
                    i.submitdate,
                    i.isactive,
                    i.setid,
                    i.colonoscopy,
                    i.parentid,
                    i.vp_cpp_immunizationtypeid,
                    i.vp_cpp_immunizationstatusid,
                    i.contactedbyphonedate,
                    i.doseunit,
                    i.immunizationday,
                    i.immunizationmonth,
                    i.immunizationyear,
                    i.patientrecordid,
                    CASE 
                        WHEN ir.vp_cpp_immunizationstatusid = 3 THEN ir.datecreated
                        WHEN ir.vp_cpp_immunizationstatusid = 4 THEN ir.datecreated
                        WHEN ir.vp_cpp_immunizationstatusid = 7 THEN ir.datecreated
                        ELSE NULL 
                    END AS dateoflastprocedure
                FROM dbo.VP_CPP_Immunization i 
                JOIN dbo.ImmunizationRecalls ir ON ir.vp_cpp_immunization_id = i.id 
                WHERE i.vp_cpp_immunizationtypeid = p_immunization_type_id
                AND ir.active = true
                ORDER BY i.patientrecordid, ir.datecreated DESC
            ) r ON r.patientrecordid = tp.patientrecordid
            WHERE (p_status IS NULL OR r.vp_cpp_immunizationstatusid_laststatus = p_status)
            ORDER BY tp.lastname;

        END IF;

    ELSE
        -- Rostered patients (not enrolled)
        IF v_age_category = 'Routine Infants & Children' THEN
            RETURN QUERY
            WITH target_population AS (
                SELECT 
                    d.patientrecordid,
                    d.firstname,
                    d.lastname,
                    pn.phonenumber
                FROM dbo.Demographics d 
                JOIN dbo.DemographicsMainResponsiblePhysicians mrp ON d.Id = mrp.DemographicId
                LEFT JOIN (
                    SELECT DISTINCT ON (demographicid) 
                        demographicid, 
                        phonenumber
                    FROM dbo.DemographicsPhoneNumbers
                    ORDER BY demographicid, isactive DESC, id DESC
                ) pn ON pn.demographicid = d.Id
                WHERE mrp.PracticeDoctorId = p_doctor_id
                AND d.active = false  -- Means TRUE in this system
                AND d.gender = CASE WHEN v_gender = 2 THEN d.gender ELSE v_gender END
                AND mrp.isactive = true
                AND EXTRACT(MONTH FROM AGE(v_date_cuttoff_service, d.dateofbirth)) >= v_age_from 
                AND EXTRACT(MONTH FROM AGE(v_date_cuttoff_service, d.dateofbirth)) <= v_age_to
            )
            SELECT 
                tp.patientrecordid,
                tp.firstname,
                tp.lastname,
                tp.phonenumber,
                r.recallid::INTEGER,
                r.vp_cpp_immunization_id::INTEGER,
                r.vp_cpp_immunizationstatusid_laststatus::INTEGER,
                r.dateservicedday::INTEGER,
                r.dateservicedmonth::INTEGER,
                r.dateservicedyear::INTEGER,
                r.contactedbyphone,
                r.administeredby,
                r.table_of_doctors_names,
                r.immunizationsystem,
                r.refuse,
                r.immunizationdate,
                r.immunizationrefuseddate,
                r.name,
                r.notes,
                r.manufacturer,
                r.lotnumber,
                r.route,
                r.site,
                r.dose,
                r.instructions,
                r.nextdate,
                r.notremind,
                r.reasonnextdate,
                r.codingvocabulary,
                r.immunizationcode,
                r.reasonfordel,
                r.submitdate,
                r.isactive,
                r.setid::INTEGER,
                r.colonoscopy,
                r.parentid::INTEGER,
                r.vp_cpp_immunizationtypeid::INTEGER,
                r.vp_cpp_immunizationstatusid::INTEGER,
                r.contactedbyphonedate,
                r.doseunit,
                r.immunizationday::INTEGER,
                r.immunizationmonth::INTEGER,
                r.immunizationyear::INTEGER,
                r.dateoflastprocedure
            FROM target_population tp 
            JOIN (
                SELECT DISTINCT ON (i.patientrecordid)
                    ir.id AS recallid,
                    ir.vp_cpp_immunization_id,
                    ir.vp_cpp_immunizationstatusid AS vp_cpp_immunizationstatusid_laststatus,
                    ir.dateservicedday,
                    ir.dateservicedmonth,
                    ir.dateservicedyear,
                    i.contactedbyphone,
                    i.administeredby,
                    i.table_of_doctors_names,
                    i.immunizationsystem,
                    i.refuse,
                    i.immunizationdate,
                    i.immunizationrefuseddate,
                    i.name,
                    i.notes,
                    i.manufacturer,
                    i.lotnumber,
                    i.route,
                    i.site,
                    i.dose,
                    i.instructions,
                    i.nextdate,
                    i.notremind,
                    i.reasonnextdate,
                    i.codingvocabulary,
                    i.immunizationcode,
                    i.reasonfordel,
                    i.submitdate,
                    i.isactive,
                    i.setid,
                    i.colonoscopy,
                    i.parentid,
                    i.vp_cpp_immunizationtypeid,
                    i.vp_cpp_immunizationstatusid,
                    i.contactedbyphonedate,
                    i.doseunit,
                    i.immunizationday,
                    i.immunizationmonth,
                    i.immunizationyear,
                    i.patientrecordid,
                    CASE 
                        WHEN ir.vp_cpp_immunizationstatusid = 3 THEN ir.datecreated
                        WHEN ir.vp_cpp_immunizationstatusid = 4 THEN ir.datecreated
                        WHEN ir.vp_cpp_immunizationstatusid = 7 THEN ir.datecreated
                        ELSE NULL 
                    END AS dateoflastprocedure
                FROM dbo.VP_CPP_Immunization i 
                JOIN dbo.ImmunizationRecalls ir ON ir.vp_cpp_immunization_id = i.id 
                WHERE i.vp_cpp_immunizationtypeid = p_immunization_type_id
                AND ir.active = true
                ORDER BY i.patientrecordid, ir.datecreated DESC
            ) r ON r.patientrecordid = tp.patientrecordid
            WHERE (p_status IS NULL OR r.vp_cpp_immunizationstatusid_laststatus = p_status)
            ORDER BY tp.lastname;

        ELSE
            -- Non-routine age category for rostered patients
            RETURN QUERY
            WITH target_population AS (
                SELECT 
                    d.patientrecordid,
                    d.firstname,
                    d.lastname,
                    pn.phonenumber
                FROM dbo.Demographics d 
                JOIN dbo.DemographicsMainResponsiblePhysicians mrp ON d.Id = mrp.DemographicId
                LEFT JOIN (
                    SELECT DISTINCT ON (demographicid) 
                        demographicid, 
                        phonenumber
                    FROM dbo.DemographicsPhoneNumbers
                    ORDER BY demographicid, isactive DESC, id DESC
                ) pn ON pn.demographicid = d.Id
                WHERE mrp.PracticeDoctorId = p_doctor_id
                AND d.active = false  -- Means TRUE in this system
                AND d.gender = CASE WHEN v_gender = 2 THEN d.gender ELSE v_gender END
                AND mrp.isactive = true
                AND EXTRACT(YEAR FROM AGE(v_age_as_of_date, d.dateofbirth)) >= v_age_from 
                AND EXTRACT(YEAR FROM AGE(v_age_as_of_date, d.dateofbirth)) <= v_age_to
            )
            SELECT 
                tp.patientrecordid,
                tp.firstname,
                tp.lastname,
                tp.phonenumber,
                r.recallid::INTEGER,
                r.vp_cpp_immunization_id::INTEGER,
                r.vp_cpp_immunizationstatusid_laststatus::INTEGER,
                r.dateservicedday::INTEGER,
                r.dateservicedmonth::INTEGER,
                r.dateservicedyear::INTEGER,
                r.contactedbyphone,
                r.administeredby,
                r.table_of_doctors_names,
                r.immunizationsystem,
                r.refuse,
                r.immunizationdate,
                r.immunizationrefuseddate,
                r.name,
                r.notes,
                r.manufacturer,
                r.lotnumber,
                r.route,
                r.site,
                r.dose,
                r.instructions,
                r.nextdate,
                r.notremind,
                r.reasonnextdate,
                r.codingvocabulary,
                r.immunizationcode,
                r.reasonfordel,
                r.submitdate,
                r.isactive,
                r.setid::INTEGER,
                r.colonoscopy,
                r.parentid::INTEGER,
                r.vp_cpp_immunizationtypeid::INTEGER,
                r.vp_cpp_immunizationstatusid::INTEGER,
                r.contactedbyphonedate,
                r.doseunit,
                r.immunizationday::INTEGER,
                r.immunizationmonth::INTEGER,
                r.immunizationyear::INTEGER,
                r.dateoflastprocedure
            FROM target_population tp 
            JOIN (
                SELECT DISTINCT ON (i.patientrecordid)
                    ir.id AS recallid,
                    ir.vp_cpp_immunization_id,
                    ir.vp_cpp_immunizationstatusid AS vp_cpp_immunizationstatusid_laststatus,
                    ir.dateservicedday,
                    ir.dateservicedmonth,
                    ir.dateservicedyear,
                    i.contactedbyphone,
                    i.administeredby,
                    i.table_of_doctors_names,
                    i.immunizationsystem,
                    i.refuse,
                    i.immunizationdate,
                    i.immunizationrefuseddate,
                    i.name,
                    i.notes,
                    i.manufacturer,
                    i.lotnumber,
                    i.route,
                    i.site,
                    i.dose,
                    i.instructions,
                    i.nextdate,
                    i.notremind,
                    i.reasonnextdate,
                    i.codingvocabulary,
                    i.immunizationcode,
                    i.reasonfordel,
                    i.submitdate,
                    i.isactive,
                    i.setid,
                    i.colonoscopy,
                    i.parentid,
                    i.vp_cpp_immunizationtypeid,
                    i.vp_cpp_immunizationstatusid,
                    i.contactedbyphonedate,
                    i.doseunit,
                    i.immunizationday,
                    i.immunizationmonth,
                    i.immunizationyear,
                    i.patientrecordid,
                    CASE 
                        WHEN ir.vp_cpp_immunizationstatusid = 3 THEN ir.datecreated
                        WHEN ir.vp_cpp_immunizationstatusid = 4 THEN ir.datecreated
                        WHEN ir.vp_cpp_immunizationstatusid = 7 THEN ir.datecreated
                        ELSE NULL 
                    END AS dateoflastprocedure
                FROM dbo.VP_CPP_Immunization i 
                JOIN dbo.ImmunizationRecalls ir ON ir.vp_cpp_immunization_id = i.id 
                WHERE i.vp_cpp_immunizationtypeid = p_immunization_type_id
                AND ir.active = true
                ORDER BY i.patientrecordid, ir.datecreated DESC
            ) r ON r.patientrecordid = tp.patientrecordid
            WHERE (p_status IS NULL OR r.vp_cpp_immunizationstatusid_laststatus = p_status)
            ORDER BY tp.lastname;

        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql;