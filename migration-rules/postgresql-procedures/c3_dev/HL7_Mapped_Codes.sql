-- PostgreSQL function for HL7_Mapped_Codes
-- Migrated from SQL Server stored procedure
-- Returns mapped HL7 codes with their LOINC mappings

CREATE OR REPLACE FUNCTION dbo.HL7_Mapped_Codes()
RETURNS TABLE(
    vpTestName text,
    id integer,
    labCode text,
    labName text,
    loinc text,
    labTestName text,
    status integer,
    spec integer,
    type integer
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        M.Name::text AS VPTestName,
        HC.Id::integer,
        HC.labCode::text,
        HC.labName::text,
        HC.LOINC::text,
        COALESCE(HC.description, '')::text AS labTestName,
        COALESCE(M.Status, 0)::integer,
        COALESCE(M.Spec, 0)::integer,
        COALESCE(M.Type, 0)::integer
    FROM dbo.HL7Coding HC
    JOIN dbo.VPUniqueMeasurements M ON HC.LOINC = M.Testcode
    WHERE 
        M.Status = 0 
        AND M.Spec = 0 
        AND M.Type = 1
        AND M.Name IS NOT NULL
    ORDER BY M.Name;
END;
$$ LANGUAGE plpgsql;