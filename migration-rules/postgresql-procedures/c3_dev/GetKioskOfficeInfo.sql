-- PostgreSQL function equivalent of SQL Server GetKioskOfficeInfo stored procedure
-- This function gets office information for kiosk displays by IP address

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create GetKioskOfficeInfo function
CREATE OR REPLACE FUNCTION dbo.GetKioskOfficeInfo(
    p_ip_address VARCHAR(100)
)
RETURNS TABLE(
    officename text,
    officebusinessname text,
    officeaddress1 text,
    officeaddress2 text,
    officecity text,
    officephone text,
    office_fax text,
    officecountry text,
    officezip text,
    officestate text,
    officeurl text,
    officeprovince text,
    officepostalcode text,
    leftlogo bytea,
    rightlogo bytea,
    middlelogo bytea,
    showlogo boolean,
    officemessage text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        o.name::text,
        o.businessname::text,
        o.address1::text,
        o.address2::text,
        o.city::text,
        o.phone::text,
        o.fax::text,
        o.country::text,
        o.zip::text,
        o.state::text,
        o.url::text,
        o.province::text,
        o.postalcode::text,
        ou.leftlogo,
        ou.rightlogo,
        ou.middlelogo,
        true AS showlogo,
        k.welcomemessage::text
    FROM dbo.office o
    JOIN dbo.kioskipaddresses k ON o.id = k.officeid
    JOIN dbo.officeoutlooks ou ON ou.officeid = o.id
    WHERE k.ipaddress = p_ip_address
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.GetKioskOfficeInfo TO postgres;