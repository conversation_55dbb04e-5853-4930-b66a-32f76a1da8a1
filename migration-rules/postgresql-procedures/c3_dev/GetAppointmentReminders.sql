-- Migration: GetAppointmentReminders_v2 stored procedure to PostgreSQL
-- Purpose: Get appointment reminders for notification system
-- Migrated from: SQL Server stored procedure [dbo].[GetAppointmentReminders_v2]

CREATE OR REPLACE FUNCTION dbo.GetAppointmentReminders(
    p_reminder_type VARCHAR(100),
    p_office_id INTEGER
) 
RETURNS TABLE (
    address1 TEXT,
    address2 TEXT,
    body TEXT,
    city TEXT,
    enableSsl BOOLEAN,
    officeBusinessName TEXT,
    officeId INTEGER,
    officeName TEXT,
    password TEXT,
    phone TEXT,
    practiceId INTEGER,
    reminderType TEXT,
    scheduleMessage INTEGER,
    scheduleMessage2 INTEGER,
    serverPort TEXT,
    serverUrl TEXT,
    subject TEXT,
    userName TEXT,
    appointmentTime TIMESTAMP WITH TIME ZONE,
    appointmentId INTEGER,
    patientRecordId INTEGER,
    demographicId INTEGER,
    patientFirstName TEXT,
    patientLastName TEXT,
    patientEmail TEXT,
    patientPhoneNumbers TEXT,
    practiceDoctorId INTEGER,
    practiceDoctorExternalDoctorId INTEGER,
    practiceDoctorFirstName TEXT,
    practiceDoctorLastName TEXT,
    AppointmentTestId INTEGER,
    testStartTime TIMESTAMP WITH TIME ZONE,
    TestId INTEGER,
    testShortName TEXT,
    testFullName TEXT,
    TestInstruction TEXT,
    appointmentTypeName TEXT
) AS $$
DECLARE
    v_reminder_type_id INTEGER;
    v_is_active INTEGER := 1;
    v_to_be_confirmed INTEGER := 0;
    v_waitlist_status_id INTEGER := 1;
    v_triage_status_id INTEGER := 16;
    v_cancelled_status_id INTEGER := 7;
    v_cancel_list_status_id INTEGER := 7;
    v_current_date TIMESTAMP := NOW();
    v_practice_id INTEGER;
    v_start_of_today TIMESTAMP;
    v_end_of_today TIMESTAMP;
BEGIN
    -- Get reminder type ID
    SELECT id INTO v_reminder_type_id 
    FROM dbo.remindertypes 
    WHERE name = p_reminder_type 
    LIMIT 1;
    
    IF v_reminder_type_id IS NULL THEN
        v_reminder_type_id := 0;
    END IF;
    
    -- Get practice ID from office
    SELECT o.practiceid INTO v_practice_id 
    FROM dbo.office o 
    WHERE o.id = p_office_id;
    
    -- Set today's date range
    v_start_of_today := DATE_TRUNC('day', CURRENT_DATE);
    v_end_of_today := v_start_of_today + INTERVAL '1 day' - INTERVAL '1 second';
    
    RETURN QUERY
    WITH reminder_offices AS (
        SELECT 
            o.id AS office_id,
            o.practiceid AS practice_id,
            o.name AS office_name,
            o.businessname AS office_business_name,
            o.address1,
            o.address2,
            o.city,
            o.phone,
            o.fax,
            r.remindertypeid AS reminder_type_id,
            p_reminder_type::TEXT AS reminder_type,
            e.serverurl AS server_url,
            e.serverport AS server_port,
            e.username AS user_name,
            e.password,
            r.subject,
            r.body,
            r.schedulemessage AS schedule_message,
            r.schedulemessage2 AS schedule_message2,
            e.enablessl AS enable_ssl,
            v_current_date + (r.schedulemessage || ' days')::INTERVAL AS appointment_date
        FROM dbo.office o
        LEFT JOIN dbo.officeemails e ON o.id = e.officeid
        LEFT JOIN dbo.reminderrules r ON o.id = r.officeid
        WHERE r.remindertypeid = v_reminder_type_id 
        AND r.schedulemessage > 0
        AND (p_reminder_type IN ('emailreminder', 'textreminder') 
             OR (p_reminder_type = 'voicereminder' AND o.id = p_office_id))
        
        UNION
        
        SELECT 
            o.id AS office_id,
            o.practiceid AS practice_id,
            o.name AS office_name,
            o.businessname AS office_business_name,
            o.address1,
            o.address2,
            o.city,
            o.phone,
            o.fax,
            r.remindertypeid AS reminder_type_id,
            p_reminder_type::TEXT AS reminder_type,
            e.serverurl AS server_url,
            e.serverport AS server_port,
            e.username AS user_name,
            e.password,
            r.subject,
            r.body,
            r.schedulemessage AS schedule_message,
            r.schedulemessage2 AS schedule_message2,
            e.enablessl AS enable_ssl,
            v_current_date + (r.schedulemessage2 || ' days')::INTERVAL AS appointment_date
        FROM dbo.office o
        LEFT JOIN dbo.officeemails e ON o.id = e.officeid
        LEFT JOIN dbo.reminderrules r ON o.id = r.officeid
        WHERE r.remindertypeid = v_reminder_type_id 
        AND r.schedulemessage2 > 0
        AND r.schedulemessage != r.schedulemessage2
        AND (p_reminder_type IN ('emailreminder', 'textreminder') 
             OR (p_reminder_type = 'voicereminder' AND o.id = p_office_id))
    ),
    appointments AS (
        SELECT 
            a.id AS appointment_id,
            a.officeid AS office_id,
            a.appointmenttime AS appointment_time,
            a.patientrecordid AS patient_record_id,
            d.id AS demographic_id,
            d.firstname AS patient_first_name,
            d.lastname AS patient_last_name,
            d.email AS patient_email,
            STRING_AGG(
                CASE 
                    WHEN dp.phonenumber IS NOT NULL AND TRIM(dp.phonenumber) != '' 
                    THEN dp.phonenumber || ' ' || 
                        CASE dp.typeofphonenumber
                            WHEN 0 THEN 'H'
                            WHEN 1 THEN 'C'
                            WHEN 2 THEN 'W'
                            ELSE ''
                        END
                    ELSE NULL
                END, ', '
            ) AS patient_phone_numbers,
            a.practicedoctorid AS practice_doctor_id,
            pd.externaldoctorid AS practice_doctor_external_doctor_id,
            ed.firstname AS practice_doctor_first_name,
            ed.lastname AS practice_doctor_last_name,
            a.appointmenttypeid AS appointment_type_id
        FROM dbo.appointments a
        JOIN reminder_offices ro ON ro.office_id = a.officeid
        LEFT JOIN dbo.practicedoctors pd ON a.practicedoctorid = pd.id
        LEFT JOIN dbo.externaldoctors ed ON pd.externaldoctorid = ed.id
        LEFT JOIN dbo.demographics d ON d.patientrecordid = a.patientrecordid
        LEFT JOIN dbo.demographicsphonenumbers dp ON dp.demographicid = d.id 
            AND dp.isremoved = false
        WHERE a.isactive = true
        AND a.appointmentconfirmation = v_to_be_confirmed
        AND a.appointmentstatus != v_cancelled_status_id
        AND a.appointmentstatus != v_cancel_list_status_id
        AND a.appointmentstatus != v_triage_status_id
        AND a.appointmentstatus != v_waitlist_status_id
        AND NOT EXISTS (
            SELECT 1 FROM dbo.appointmenttypes at 
            WHERE at.id = a.appointmenttypeid 
            AND at.appointmenttypeid = 4
        )
        AND a.appointmenttime BETWEEN 
            DATE_TRUNC('day', ro.appointment_date) 
            AND DATE_TRUNC('day', ro.appointment_date) + INTERVAL '1 day' - INTERVAL '1 second'
        AND (
            (p_reminder_type = 'emailreminder' AND d.email IS NOT NULL AND d.consentemail = true)
            OR (p_reminder_type = 'voicereminder' AND a.officeid = p_office_id AND EXISTS (
                SELECT 1 FROM dbo.demographicsphonenumbers dp2
                WHERE dp2.phonenumber IS NOT NULL 
                AND TRIM(dp2.phonenumber) != ''
                AND dp2.demographicid = d.id 
                AND dp2.isremoved = false
                AND dp2.typeofphonenumber IN (0, 1)
            ))
            OR (p_reminder_type = 'textreminder' AND EXISTS (
                SELECT 1 FROM dbo.demographicsphonenumbers dp2
                WHERE dp2.phonenumber IS NOT NULL 
                AND TRIM(dp2.phonenumber) != ''
                AND dp2.demographicid = d.id 
                AND dp2.isremoved = false
                AND dp2.typeofphonenumber IN (1)
            ))
        )
        GROUP BY 
            a.id, a.officeid, a.appointmenttime, a.patientrecordid,
            d.id, d.firstname, d.lastname, d.email,
            a.practicedoctorid, pd.externaldoctorid, 
            ed.firstname, ed.lastname, a.appointmenttypeid
    )
    SELECT DISTINCT
        ro.address1,
        ro.address2,
        ro.body,
        ro.city,
        ro.enable_ssl,
        ro.office_business_name,
        ro.office_id::INTEGER,
        ro.office_name,
        ro.password,
        ro.phone,
        ro.practice_id::INTEGER,
        ro.reminder_type,
        ro.schedule_message::INTEGER,
        ro.schedule_message2::INTEGER,
        ro.server_port,
        ro.server_url,
        ro.subject,
        ro.user_name,
        apps.appointment_time,
        apps.appointment_id::INTEGER,
        apps.patient_record_id::INTEGER,
        apps.demographic_id::INTEGER,
        apps.patient_first_name,
        apps.patient_last_name,
        apps.patient_email,
        apps.patient_phone_numbers,
        apps.practice_doctor_id::INTEGER,
        apps.practice_doctor_external_doctor_id::INTEGER,
        apps.practice_doctor_first_name,
        apps.practice_doctor_last_name,
        apptests.id::INTEGER AS appointment_test_id,
        apptests.starttime AS test_start_time,
        apptests.testid::INTEGER,
        t.testshortname,
        t.testfullname,
        pt.testinstruction,
        aptt.name AS appointment_type_name
    FROM reminder_offices ro
    JOIN appointments apps ON ro.office_id = apps.office_id
    LEFT JOIN dbo.appointmenttests apptests ON apptests.appointmentid = apps.appointment_id
        AND apptests.isactive = true
    LEFT JOIN dbo.appointmenttypes aptt ON aptt.id = apps.appointment_type_id
    LEFT JOIN dbo.practicetests pt ON pt.testid = apptests.testid
        AND pt.isactive = true
        AND pt.practiceid = ro.practice_id
    LEFT JOIN dbo.tests t ON t.id = apptests.testid
    WHERE NOT EXISTS (
        SELECT 1 FROM dbo.remindersenthistories rsh
        WHERE rsh.officeid = ro.office_id
        AND rsh.patientrecordid = apps.patient_record_id
        AND rsh.appointmentid::INTEGER = apps.appointment_id
        AND rsh.remindertypeid = v_reminder_type_id
        AND rsh.messagesentdate BETWEEN v_start_of_today AND v_end_of_today
        AND rsh.isresendrequired = false
    )
    ORDER BY apps.appointment_time;
END;
$$ LANGUAGE plpgsql;