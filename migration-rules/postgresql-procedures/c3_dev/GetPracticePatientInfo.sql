-- Migration: GetPracticePatientInfo stored procedure to PostgreSQL
-- Purpose: Get practice-specific patient information (simplified from fn_GetPatientInfo usage)
-- Migrated from: SQL Server stored procedure [dbo].[GetPracticePatientInfo]

CREATE OR REPLACE FUNCTION dbo.GetPracticePatientInfo(
    p_practice_id INTEGER,
    p_patient_record_id INTEGER
) 
RETURNS TABLE (
    PracticeId INTEGER,
    DemographicId BIGINT,
    Salutation INTEGER,
    PatientRecordId INTEGER,
    AliasLastName TEXT,
    AliasFirstName TEXT,
    AliasMiddleName TEXT,
    UseAliases BOOLEAN,
    PreferredName TEXT,
    FirstName TEXT,
    MiddleName TEXT,
    LastName TEXT,
    DateOfBirth DATE,
    AgeAccurate TEXT,
    Gender INTEGER,
    HealthCard TEXT,
    OHIPVersionCode TEXT,
    PatientPhoneNumbers TEXT,
    DefaultPaymentMethod INTEGER
) AS $$
DECLARE
    v_current_date TIMESTAMP := NOW();
    v_ohip_id TEXT;
    v_ohip_version_code TEXT;
    v_phone_numbers TEXT;
BEGIN
    -- Get health card information (latest entry)
    SELECT 
        COALESCE(hc.number, '') as ohip_id,
        COALESCE(hc.version, '') as version_code
    INTO v_ohip_id, v_ohip_version_code
    FROM dbo.demographicshealthcards hc 
    INNER JOIN dbo.demographics d ON hc.DemographicId = d.id
    WHERE d.patientrecordid = p_patient_record_id
    ORDER BY hc.id DESC
    LIMIT 1;

    -- Get phone numbers (concatenated string format for compatibility)
    SELECT STRING_AGG(
        CASE ph.typeOfPhoneNumber
            WHEN 0 THEN 'H: ' || ph.phoneNumber
            WHEN 1 THEN 'C: ' || ph.phoneNumber
            WHEN 2 THEN 'W: ' || ph.phoneNumber
            ELSE ph.phoneNumber
        END, ', '
        ORDER BY ph.typeOfPhoneNumber, ph.IsActive DESC, ph.Id DESC
    )
    INTO v_phone_numbers
    FROM dbo.demographicsphonenumbers ph 
    INNER JOIN dbo.demographics d ON ph.DemographicId = d.Id
    WHERE d.PatientRecordId = p_patient_record_id
        AND ph.phoneNumber IS NOT NULL 
        AND TRIM(ph.phoneNumber) <> '' 
        AND COALESCE(ph.isRemoved, false) = false;

    RETURN QUERY
    SELECT
        pr.PracticeId::INTEGER,
        d.Id::BIGINT as DemographicId,
        COALESCE(d.namePrefix, 0)::INTEGER as Salutation,
        d.PatientRecordId::INTEGER,
        d.aliasLastName,
        d.aliasFirstName,
        d.aliasMiddleName,
        COALESCE(d.useAliases, false) as UseAliases,
        d.preferredName,
        COALESCE(d.aliasFirstName, d.firstName) as FirstName,
        COALESCE(d.aliasMiddleName, d.middleName) as MiddleName,
        COALESCE(d.aliasLastName, d.lastName) as LastName,
        d.dateOfBirth,
        (EXTRACT(YEAR FROM AGE(v_current_date, d.dateOfBirth))::TEXT || ' years') as AgeAccurate,
        COALESCE(d.gender, 0)::INTEGER,
        COALESCE(v_ohip_id, '') as HealthCard,
        COALESCE(v_ohip_version_code, '') as OHIPVersionCode,
        COALESCE(v_phone_numbers, '') as PatientPhoneNumbers,
        COALESCE(d.defaultPaymentMethod, 0)::INTEGER
    FROM dbo.patientrecords pr
    INNER JOIN dbo.demographics d ON pr.Id = d.PatientRecordId
    WHERE pr.Id = p_patient_record_id AND pr.PracticeId = p_practice_id;
END;
$$ LANGUAGE plpgsql;