-- PostgreSQL function migration of Get_VP_ReportPhrasesSavedText stored procedure
-- Original: Get_VP_ReportPhrasesSavedText(@appointmentID INT, @patientID INT, @VP_AppointmentTestLogId INT = 0)
-- Purpose: Gets saved text for VP report phrases

CREATE OR REPLACE FUNCTION dbo.Get_VP_ReportPhrasesSavedText(
    p_appointment_id INTEGER,
    p_patient_id INTEGER,
    p_vp_appointment_test_log_id INTEGER DEFAULT 0
)
RETURNS TABLE(
    id integer,
    appointmentid integer,
    patientrecordid integer,
    vp_appointmenttestlogid integer,
    reportphraseid integer,
    savedtext text,
    datecreated timestamp,
    userid integer
    -- Note: Add other columns as needed based on actual table structure
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        v.id,
        v.appointmentid,
        v.patientrecordid,
        v.vp_appointmenttestlogid,
        v.reportphraseid,
        v.savedtext,
        v.datecreated AT TIME ZONE 'UTC' as datecreated,
        v.userid
    FROM dbo.vp_reportphrasessavedtext v
    WHERE v.appointmentid = p_appointment_id
      AND v.patientrecordid = p_patient_id
      AND v.vp_appointmenttestlogid = 
          CASE 
              WHEN p_vp_appointment_test_log_id = 0 
              THEN v.vp_appointmenttestlogid 
              ELSE p_vp_appointment_test_log_id 
          END;
END;
$$ LANGUAGE plpgsql;