-- Fixed PostgreSQL function equivalent of SQL Server GetPatientInfo stored procedure
-- This function gets comprehensive patient information including appointment and doctor details
-- Fixed version addressing common PostgreSQL function issues

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Helper function to calculate age (matching SQL Server fn_CalculateAge)
CREATE OR REPLACE FUNCTION dbo.fn_calculateage(birth_date DATE, calculate_date DATE DEFAULT CURRENT_DATE)
RETURNS TEXT AS $$
BEGIN
    IF birth_date IS NULL THEN
        RETURN '';
    END IF;
    
    IF calculate_date IS NULL THEN
        calculate_date := CURRENT_DATE;
    END IF;
    
    RETURN EXTRACT(YEAR FROM AGE(calculate_date, birth_date))::TEXT;
END;
$$ LANGUAGE plpgsql;

-- Fixed Main GetPatientInfo function
CREATE OR REPLACE FUNCTION dbo.GetPatientInfo(
    p_patient_id INTEGER,
    p_appointment_id INTEGER DEFAULT NULL,
    p_test_id INTEGER DEFAULT NULL,
    p_active_status INTEGER DEFAULT 0
)
RETURNS TABLE(
    practiceid integer,
    demographicid integer,
    salutation integer,
    patientrecordid integer,
    aliaslastname text,
    aliasfirstname text,
    aliasmiddlename text,
    usealiases boolean,
    preferredname text,
    firstname text,
    middlename text,
    lastname text,
    dateofbirth timestamp without time zone,
    ageaccurate text,
    gender integer,
    ohip text,
    ohipversioncode text,
    healthcardprovince text,
    patientphonenumbers text,
    defaultpaymentmethod integer,
    appointmenttime timestamp without time zone,
    actiononabnormal boolean,
    appointmentreferraldoctor text,
    testreferraldoctor text,
    testname text,
    mrp text,
    familydoctor text,
    defaultreferraldoctor text
) AS $$
DECLARE
    v_ohip TEXT := '';
    v_ohip_version TEXT := '';
    v_province TEXT := 'ON';
    v_appointment_date TIMESTAMP;
BEGIN
    -- Get appointment date if appointment ID is provided for age calculation
    IF p_appointment_id IS NOT NULL THEN
        SELECT appointmenttime INTO v_appointment_date 
        FROM dbo.appointments 
        WHERE id = p_appointment_id;
    END IF;

    -- Get health card information (matching SQL Server logic) - handle case where no health card exists
    BEGIN
        SELECT 
            COALESCE(hc.number, ''),
            COALESCE(hc.version, ''),
            CASE hc.provincecode
                WHEN 0 THEN 'AB'
                WHEN 9 THEN 'PE' 
                WHEN 4 THEN 'NL'
                WHEN 1 THEN 'BC'
                WHEN 8 THEN 'ON'
                WHEN 2 THEN 'MB'
                WHEN 5 THEN 'NS'
                ELSE 'ON'
            END
        INTO v_ohip, v_ohip_version, v_province
        FROM dbo.demographicshealthcards hc 
        JOIN dbo.demographics d ON hc.demographicid = d.id
        WHERE d.patientrecordid = p_patient_id
        ORDER BY hc.id DESC
        LIMIT 1;
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            v_ohip := '';
            v_ohip_version := '';
            v_province := 'ON';
    END;

    RETURN QUERY
    SELECT 
        pr.practiceid,
        d.id as demographicid,
        d.nameprefix as salutation,
        d.patientrecordid,
        d.aliaslastname::text,
        d.aliasfirstname::text,
        d.aliasmiddlename::text,
        d.usealiases,
        COALESCE(d.preferredname, '')::text,
        COALESCE(d.aliasfirstname, d.firstname)::text,
        COALESCE(d.aliasmiddlename, d.middlename)::text,
        COALESCE(d.aliaslastname, d.lastname)::text,
        -- Fixed: Remove AT TIME ZONE which can cause issues
        d.dateofbirth::timestamp without time zone,
        dbo.fn_calculateage(d.dateofbirth::DATE, COALESCE(v_appointment_date::DATE, CURRENT_DATE)),
        d.gender,
        v_ohip,
        v_ohip_version,
        v_province,
        -- Phone numbers with type indicators (matching SQL Server logic)
        COALESCE(
            (SELECT STRING_AGG(
                ph.phonenumber || ' ' || 
                CASE ph.typeofphonenumber
                    WHEN 0 THEN 'H'
                    WHEN 1 THEN 'C' 
                    WHEN 2 THEN 'W'
                    ELSE ''
                END, 
                ', ' 
                ORDER BY ph.typeofphonenumber, ph.isactive DESC, ph.id DESC
            )
             FROM dbo.demographicsphonenumbers ph 
             WHERE ph.demographicid = d.id 
             AND ph.phonenumber IS NOT NULL 
             AND TRIM(ph.phonenumber) != ''
             AND ph.isremoved = false), 
            ''
        )::text,
        d.defaultpaymentmethod,
        -- Appointment info - Fixed: Remove AT TIME ZONE
        app.appointmenttime::timestamp without time zone,
        app.actiononabnormal,
        -- Appointment referral doctor
        COALESCE(
            (SELECT COALESCE(ed.firstname, '') || ' ' || COALESCE(ed.lastname, '')
             FROM dbo.externaldoctors ed 
             WHERE ed.id = app.referraldoctorid), 
            ''
        )::text,
        -- Test referral doctor
        COALESCE(
            (SELECT COALESCE(ed.firstname, '') || ' ' || COALESCE(ed.lastname, '')
             FROM dbo.externaldoctors ed 
             JOIN dbo.appointmenttests apptest ON ed.id = apptest.referraldoctorid
             WHERE apptest.appointmentid = app.id 
             AND apptest.testid = p_test_id 
             AND ed.id = apptest.referraldoctorid
             LIMIT 1), 
            ''
        )::text,
        -- Test name
        COALESCE(
            (SELECT t.testfullname 
             FROM dbo.tests t 
             WHERE t.id = p_test_id 
             LIMIT 1), 
            ''
        )::text,
        -- MRP (Most Responsible Physician)
        COALESCE(
            (SELECT COALESCE(ed.firstname, '') || ' ' || COALESCE(ed.lastname, '')
             FROM dbo.externaldoctors ed 
             JOIN dbo.demographicsmainresponsiblephysicians mrp ON ed.id = mrp.externaldoctorid
             WHERE mrp.demographicid = d.id 
             AND mrp.isactive = true
             ORDER BY mrp.id DESC 
             LIMIT 1), 
            ''
        )::text,
        -- Family Doctor
        COALESCE(
            (SELECT COALESCE(ed.firstname, '') || ' ' || COALESCE(ed.lastname, '')
             FROM dbo.externaldoctors ed 
             JOIN dbo.demographicsfamilydoctors fd ON ed.id = fd.externaldoctorid
             WHERE fd.demographicid = d.id 
             AND fd.isactive = true 
             AND fd.isremoved = false
             ORDER BY fd.id DESC 
             LIMIT 1), 
            ''
        )::text,
        -- Default Referral Doctor
        COALESCE(
            (SELECT COALESCE(ed.firstname, '') || ' ' || COALESCE(ed.lastname, '')
             FROM dbo.externaldoctors ed 
             JOIN dbo.demographicsdefaultreferraldoctors rd ON ed.id = rd.externaldoctorid
             WHERE rd.demographicid = d.id 
             AND rd.isactive = true
             ORDER BY rd.id DESC 
             LIMIT 1), 
            ''
        )::text
    FROM dbo.patientrecords pr
    -- Fixed: Simplified the subquery approach
    JOIN dbo.demographics d ON pr.id = d.patientrecordid
    LEFT JOIN dbo.appointments app ON (
        d.patientrecordid = app.patientrecordid 
        AND app.id = p_appointment_id 
        AND app.isactive = true
    )
    WHERE pr.id = p_patient_id
    AND d.patientrecordid = p_patient_id  -- Added explicit filter
    ORDER BY d.id  -- Ensure consistent ordering
    LIMIT 1;  -- Ensure only one result
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.GetPatientInfo TO postgres;