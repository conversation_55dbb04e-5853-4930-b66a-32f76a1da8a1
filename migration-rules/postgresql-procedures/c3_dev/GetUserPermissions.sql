-- =============================================
-- Author:        Migration from SQL Server
-- Create date:   PostgreSQL Migration
-- Description:   Get user permissions - PostgreSQL equivalent of [dbo].[GetUserPermissions]
-- =============================================

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

CREATE OR REPLACE FUNCTION dbo.GetUserPermissions(p_userId INTEGER)
RETURNS TABLE(
    "Id" INTEGER,
    "PermissionName" VARCHAR,
    "Role" VARCHAR,
    "PermissionTypeId" INTEGER,
    "PermissionType" VARCHAR,
    "PracticeId" INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        rp.PermissionId AS "Id",
        rp.PermissionName::VARCHAR AS "PermissionName",
        r.Name::VARCHAR AS "Role", 
        rp.PermissionTypeId AS "PermissionTypeId",
        pt.Name::VARCHAR AS "PermissionType",
        r.PracticeId AS "PracticeId"
    FROM dbo.aspnetusers u
    JOIN dbo.aspnetuserroles AS ur ON u.Id = ur.UserId
    JOIN dbo.aspnetroles AS r ON ur.RoleId = r.Id
    JOIN dbo.rolepermissions AS rp ON ur.RoleId = rp.ApplicationRoleId
    JOIN dbo.permissiontypes AS pt ON rp.PermissionTypeId = pt.Id
    WHERE u.UserID = p_userId;
END;
$$ LANGUAGE plpgsql;