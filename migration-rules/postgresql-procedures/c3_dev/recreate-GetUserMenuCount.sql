-- Drop and recreate GetUserMenuCount function with proper schema references
-- This script ensures the function is completely replaced with the new version

-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS dbo.GetUserMenuCount(integer);

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create function in dbo schema with all table references including schema
CREATE OR REPLACE FUNCTION dbo.GetUserMenuCount(
    p_userId INT
)
RETURNS TABLE(
    ContactManagerCount INT,
    ExternalDocumentsCount INT,
    WorklistCount INT,
    TriageCount INT,
    TotalToDo INT
)
AS $$
DECLARE
    v_cmCount INT := 0; -- contact manager count
    v_exDocumentCount INT := 0; -- external documents count
    v_triageCount INT := 0; -- triage count
    v_wlCount INT := 0; -- worklist count
    v_TotalToDO INT := 0; -- total count
    v_practiceDoctorId INT := 0;
    v_practiceId INT := 0;
    v_appUserId VARCHAR(500); -- GUID application userId
    v_forTriageStatusId INT := 0;
    v_triagedStatusId INT := 0;
    v_doctorUserType INT := 5;
    v_userType INT := 0;
    v_faxHrmCount INT := 0;
    v_hl7Count INT := 0;
BEGIN
    -- Get user information
    SELECT Id, PracticeID, CerebrumUserType 
    INTO v_appUserId, v_practiceId, v_userType
    FROM dbo.AspNetUsers 
    WHERE UserID = p_userId
    LIMIT 1;
    
    -- Get practice doctor ID
    v_practiceDoctorId := COALESCE(
        (SELECT Id 
         FROM dbo.PracticeDoctors 
         WHERE ApplicationUserId = v_appUserId 
           AND ApplicationUserId IS NOT NULL
         LIMIT 1), 0);
    
    -- Get triage status IDs
    v_forTriageStatusId := COALESCE(
        (SELECT Id 
         FROM dbo.TriageStatus 
         WHERE Description = 'For Triage'
         LIMIT 1), 0);
    
    v_triagedStatusId := COALESCE(
        (SELECT Id 
         FROM dbo.TriageStatus 
         WHERE Description = 'Triaged'
         LIMIT 1), 0);
    
    -- Calculate contact manager count
    v_cmCount := COALESCE(
        (SELECT COUNT(*)
         FROM dbo.CM_TaskDefinition ts
         WHERE EXISTS (
             SELECT 1
             FROM dbo.CM_TaskMessage tm
             JOIN dbo.CM_TaskMessageRecipient tr ON tm.id = tr.CM_TaskMessageId
             WHERE tr.userid = v_appUserId
               AND tr.seen = false 
               AND tr.hideFromUnseen = false
               AND ts.id = tm.CM_TaskDefinitionId
         )
         AND ts.taskStatus = 0), 0);
    
    -- If user is a doctor, calculate additional counts
    IF v_practiceDoctorId > 0 AND v_userType = v_doctorUserType THEN
        
        -- Calculate worklist count (no datatransfer appointment types)
        v_wlCount := COALESCE(
            (SELECT COUNT(t.Id) 
             FROM dbo.AppointmentTests t
             JOIN dbo.Appointments a ON a.Id = t.AppointmentId
             WHERE a.appointmentStatus NOT IN (0,1,7) -- cancellation list, waitlist and cancelled
               AND a.PracticeDoctorId = v_practiceDoctorId 
               AND (t.ReassignDocId = v_practiceDoctorId OR t.ReassignDocID = 0)
               AND t.AppointmentTestStatusId = 9 -- ready for doctor
               AND a.IsActive = true
               AND t.IsActive = true), 0);
        
        -- Calculate triage count
        v_triageCount := COALESCE(
            (SELECT COUNT(a.Id) 
             FROM dbo.Appointments a
             WHERE a.IsActive = true
               AND a.PracticeDoctorId = v_practiceDoctorId
               AND a.TriageStatusId = v_forTriageStatusId 
               AND a.appointmentStatus = 16), 0); -- triage status
        
        -- Calculate fax/HRM count
        v_faxHrmCount := COALESCE(
            (SELECT COUNT(rr.Id) 
             FROM dbo.ReportReceiveds rr 
             JOIN dbo.DoctorsReportRevieweds rv ON rr.Id = rv.ReportReceivedId
             WHERE rv.practiceDoctorId = v_practiceDoctorId 
               AND rv.dateTimeReportReviewed IS NULL
               AND rr.assignmentStatus != 2 -- ignore
               AND (rr.status IS NULL OR rr.status = 1)), 0);
        
        -- HL7 count is commented out as per original
        v_hl7Count := 0;
        
        v_exDocumentCount := v_faxHrmCount + v_hl7Count;
    
    END IF;
    
    -- Calculate total
    v_TotalToDO := v_exDocumentCount + v_triageCount + v_wlCount;
    
    -- Return the result set
    RETURN QUERY
    SELECT 
        v_cmCount AS ContactManagerCount,
        v_exDocumentCount AS ExternalDocumentsCount,
        v_wlCount AS WorklistCount,
        v_triageCount AS TriageCount,
        v_TotalToDO AS TotalToDo;
        
END;
$$ LANGUAGE plpgsql;

-- Verify the function was created
SELECT 
    n.nspname AS schema_name,
    p.proname AS function_name,
    pg_catalog.pg_get_function_identity_arguments(p.oid) AS arguments
FROM pg_catalog.pg_proc p
LEFT JOIN pg_catalog.pg_namespace n ON n.oid = p.pronamespace
WHERE n.nspname = 'dbo' 
  AND p.proname = 'getusermenucount';

-- Test the function (uncomment and adjust user ID as needed)
-- SELECT * FROM dbo.GetUserMenuCount(123);