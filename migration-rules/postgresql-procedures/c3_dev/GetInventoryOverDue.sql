-- GetInventoryOverDue procedure for PostgreSQL
-- Migrated from SQL Server stored procedure

CREATE OR REPLACE FUNCTION dbo.GetInventoryOverDue(
    p_practice_id integer,
    p_office_id integer,
    p_selected_date timestamp
)
RETURNS TABLE (
    devicenumberid bigint,
    officeid integer,
    officename text,
    devicetypeid integer,
    devicenumber text,
    devicetype text,
    patientequipmentid bigint,
    patientid integer,
    patientfullname text,
    datestarted timestamp with time zone,
    dateexpectedreturn timestamp with time zone,
    notes text,
    appointmenttestid integer,
    assignedbyuserid integer,
    assignedbyuser text
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        i.id AS devicenumberid,
        i.officeid,
        o.name AS officename,
        i.inventorytypeid AS devicetypeid,
        i.code AS devicenumber,
        it.name AS devicetype,
        pe.id AS patientequipmentid,
        pe.patientrecordid AS patientid,
        COALESCE(d.lastname, '') || ', ' || COALESCE(d.firstname, '') AS patientfullname,
        pe.datestarted,
        pe.dateexpectedreturn,
        COALESCE(pe.notes, '') AS notes,
        pe.appointmenttestid,
        pe.assignedbyuserid,
        COALESCE((
            SELECT COALESCE(u.lastname, '') || ', ' || COALESCE(u.firstname, '')
            FROM dbo.aspnetusers u 
            WHERE u.userid = pe.assignedbyuserid 
            LIMIT 1
        ), '') AS assignedbyuser
    FROM dbo.patientequipments pe
    JOIN dbo.demographics d ON pe.patientrecordid = d.patientrecordid
    JOIN dbo.storeinventories i ON pe.inventoryid = i.id
    JOIN dbo.storeinventorytypes it ON i.inventorytypeid = it.id
    JOIN dbo.office o ON i.officeid = o.id
    WHERE pe.dateexpectedreturn IS NOT NULL
      AND pe.dateexpectedreturn < p_selected_date
      AND pe.datereturned IS NULL
      AND i.statusid = 2 -- in use
      AND d.active = 0 -- 0 means active is false (patient is inactive)
      AND o.practiceid = p_practice_id
      AND i.officeid = p_office_id;
END;
$$;