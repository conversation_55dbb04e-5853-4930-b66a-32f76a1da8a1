-- PostgreSQL function for GetDoc<PERSON><PERSON><PERSON><PERSON>ategoryPhrases
-- Migrated from SQL Server stored procedure
-- Gets doctor root category phrases with hierarchical structure and breadcrumb navigation

CREATE OR REPLACE FUNCTION dbo.GetDoctorRootCategoryPhrases(
    p_group_id INTEGER,
    p_external_doctor_id INTEGER,
    p_practice_id INTEGER,
    p_practice_template_id INTEGER,
    p_root_category_id INTEGER DEFAULT 0
)
RETURNS TABLE(
    pracrootcatphraseid INTEGER,
    rootcategoryphraseid INTEGER,
    pracrootcategorytempid INTEGER,
    roottemplateid INTEGER,
    rootcategoryid INTEGER,
    categoryname TEXT,
    groupid INTEGER,
    practiceid INTEGER,
    externaldoctorid INTEGER,
    isvisibletoolbar BOOLEAN,
    isvisible BOOLEAN,
    phrasename TEXT,
    phrasevalue TEXT,
    parentid INTEGER,
    categorydisplayorder INTEGER,
    phrasedisplayorder INTEGER,
    breadcrum TEXT,
    issubcategory BOOLEAN,
    level INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH phrase_data AS (
        SELECT 
            prcp.Id as pracrootcategoryphraseid,
            prcp.PracRootCategoryTempId as pracrootcategorytempid,
            prcp.RootCategoryPhraseId as rootcategoryphraseid,
            CAST(CASE WHEN drp.PhraseName IS NULL THEN rcp.PhraseName ELSE drp.PhraseName END AS TEXT) AS phrasename,
            CAST(CASE WHEN drp.PhraseValue IS NULL THEN rcp.PhraseValue ELSE drp.PhraseValue END AS TEXT) AS phrasevalue,
            CAST(CASE WHEN drp.IsVisible IS NULL THEN TRUE ELSE drp.IsVisible END AS BOOLEAN) AS isvisible,
            COALESCE(drp.DisplayOrder, 1000000) as displayorder,
            rcp.ParentId as parentid,
            rc.Id as rootcategoryid,
            rc.GroupId as groupid,
            rcp.IsSubCategory as issubcategory,
            CAST(CASE WHEN drp.IsActive IS NULL THEN TRUE ELSE drp.IsActive END AS BOOLEAN) as isacticefordoctor
        FROM dbo.PracticeRootCategoryPhrases prcp
        JOIN dbo.RootCategoryPhrases rcp ON prcp.RootCategoryPhraseId = rcp.Id
        JOIN dbo.RootCategories rc ON rcp.RootCategoryId = rc.Id
        JOIN dbo.DoctorRootCategoryPhrases drp ON drp.RootCategoryPhraseId = prcp.RootCategoryPhraseId 
            AND drp.PracRootCategoryTempId = p_practice_template_id 
            AND drp.ExternalDoctorId = p_external_doctor_id
        WHERE prcp.PracRootCategoryTempId = p_practice_template_id    
        AND prcp.IsActive = TRUE
        AND rc.GroupId = p_group_id
    ),
    RECURSIVE CustomPhraseCTE AS (
        -- Base case - root level phrases (no parent)
        SELECT 
            phrases.pracrootcategoryphraseid as pracrootcatphraseid,
            phrases.rootcategoryphraseid,
            drc.pracrootcategorytempid,
            drc.templateid AS roottemplateid,
            drc.rootcategoryid,
            CASE WHEN drc.categorynamecustom IS NULL THEN drc.categorynameoriginal ELSE drc.categorynamecustom END as categoryname,
            drc.groupid,
            drc.practiceid,
            drc.externaldoctorid,
            drc.isvisibletoolbar,
            phrases.isvisible,
            phrases.phrasename,
            phrases.phrasevalue,
            phrases.parentid,
            COALESCE(drc.displayorder, 1000000) as categorydisplayorder,
            phrases.displayorder as phrasedisplayorder,
            CAST((CASE WHEN drc.categorynamecustom IS NULL THEN drc.categorynameoriginal ELSE drc.categorynamecustom END) || ' -> ' || phrases.phrasename AS TEXT) as breadcrum,
            phrases.issubcategory,
            0 as level
        FROM phrase_data phrases
        JOIN dbo.GetDoctorRootCategories(p_group_id, p_external_doctor_id, p_practice_id, p_practice_template_id, p_root_category_id) drc 
            ON phrases.pracrootcategorytempid = drc.pracrootcategorytempid                    
        WHERE drc.isvisible = TRUE
        AND phrases.isvisible = TRUE
        AND phrases.parentid IS NULL
        AND phrases.rootcategoryid = drc.rootcategoryid
        AND phrases.isacticefordoctor = TRUE
        
        UNION ALL
        
        -- Recursive case - child phrases
        SELECT 
            phrases.pracrootcategoryphraseid as pracrootcatphraseid,
            phrases.rootcategoryphraseid,
            cte.pracrootcategorytempid,
            cte.roottemplateid,
            cte.rootcategoryid,
            cte.categoryname,
            cte.groupid,
            cte.practiceid,
            cte.externaldoctorid,
            cte.isvisibletoolbar,
            phrases.isvisible,
            phrases.phrasename,
            phrases.phrasevalue,
            phrases.parentid,
            cte.categorydisplayorder,
            phrases.displayorder as phrasedisplayorder,
            CAST(cte.breadcrum || ' -> ' || phrases.phrasename AS TEXT) as breadcrum,
            phrases.issubcategory,
            cte.level + 1 as level
        FROM phrase_data phrases
        JOIN CustomPhraseCTE cte ON phrases.parentid = cte.rootcategoryphraseid                
        WHERE phrases.isvisible = TRUE 
        AND cte.rootcategoryid = phrases.rootcategoryid
        AND phrases.isacticefordoctor = TRUE
    )
    SELECT 
        cte.pracrootcatphraseid,
        cte.rootcategoryphraseid,
        cte.pracrootcategorytempid,
        cte.roottemplateid,
        cte.rootcategoryid,
        cte.categoryname,
        cte.groupid,
        cte.practiceid,
        cte.externaldoctorid,
        cte.isvisibletoolbar,
        cte.isvisible,
        cte.phrasename,
        cte.phrasevalue,
        COALESCE(cte.parentid, -1) AS parentid,
        cte.categorydisplayorder,
        cte.phrasedisplayorder,
        cte.breadcrum,
        cte.issubcategory,
        cte.level
    FROM CustomPhraseCTE cte 
    WHERE cte.isvisible = TRUE
    ORDER BY cte.categorydisplayorder ASC, cte.phrasedisplayorder ASC, cte.categoryname ASC, cte.phrasename ASC;
    
END;
$$ LANGUAGE plpgsql;