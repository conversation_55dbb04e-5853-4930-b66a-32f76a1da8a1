-- getDoctorSignaturePath PostgreSQL Function
-- Retrieves the file path for a doctor's signature based on practice and appointment
-- PostgreSQL Migration: 2025-09-11 - Updated schema references and path separators

CREATE OR REPLACE FUNCTION dbo.getDoctorSignaturePath(
    p_practice_id INTEGER,
    p_appointment_id INTEGER
)
RETURNS TABLE(
    drsignature TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_signature_path TEXT;
BEGIN
    v_signature_path := NULL;
    
    -- Get signature path using proper PostgreSQL path separators
    SELECT 
        p.practicefolder || '/signatures/' || CAST(pd.applicationuserid AS TEXT) || '.gif'
    INTO v_signature_path
    FROM dbo.appointments a
    JOIN dbo.practicedoctors pd ON a.practicedoctorid = pd.id
    JOIN dbo.practices p ON pd.practiceid = p.id
    WHERE p.id = p_practice_id
      AND a.id = p_appointment_id
    LIMIT 1;
    
    RETURN QUERY SELECT v_signature_path;
END;
$$;