-- PostgreSQL function migration of fn_CalculateAge
-- Original: fn_CalculateAge(@dateOfBirth datetime, @date datetime = null) RETURNS nvarchar(100)
-- Purpose: Calculates patient age in days, months, or years format
-- Modified to return TABLE for repository pattern compatibility

CREATE OR REPLACE FUNCTION dbo.fn_CalculateAge(
    p_date_of_birth TIMESTAMP,
    p_date TIMESTAMP DEFAULT NULL
)
RETURNS TABLE(
    age_accurate TEXT
) AS $$
DECLARE
    v_age_accurate TEXT := '';
    v_years INTEGER;
    v_months INTEGER;
    v_days INTEGER;
    v_years_str TEXT;
    v_months_str TEXT;
    v_days_str TEXT;
    v_calc_date TIMESTAMP;
BEGIN
    -- If date of birth is not null
    IF p_date_of_birth IS NOT NULL THEN
        -- Set calculation date to current time if not provided or invalid
        IF p_date IS NULL OR p_date < p_date_of_birth THEN
            v_calc_date := NOW();
        ELSE
            v_calc_date := p_date;
        END IF;
        
        -- Calculate years and months difference
        v_years := EXTRACT(YEAR FROM v_calc_date) - EXTRACT(YEAR FROM p_date_of_birth);
        v_months := EXTRACT(MONTH FROM v_calc_date) - EXTRACT(MONTH FROM p_date_of_birth);
        
        -- Adjust for day differences
        IF EXTRACT(DAY FROM v_calc_date) < EXTRACT(DAY FROM p_date_of_birth) THEN
            v_months := v_months - 1;
        END IF;
        
        -- Adjust for negative months
        IF v_months < 0 THEN
            v_years := v_years - 1;
            v_months := v_months + 12;
        END IF;
        
        -- For babies less than 1 month old, return days
        IF v_years = 0 AND v_months = 0 THEN
            v_days := (v_calc_date::DATE - p_date_of_birth::DATE);
            v_days_str := v_days::TEXT;
            v_age_accurate := CASE 
                WHEN v_days > 1 THEN v_days_str || ' days'
                ELSE v_days_str || ' day'
            END;
        -- For children under 2 years, return months
        ELSIF v_years < 2 THEN
            v_months := v_months + (v_years * 12);
            v_months_str := v_months::TEXT;
            v_age_accurate := CASE 
                WHEN v_months > 1 THEN v_months_str || ' months'
                ELSE v_months_str || ' month'
            END;
        -- For others, return years
        ELSE
            v_years_str := v_years::TEXT;
            v_age_accurate := CASE 
                WHEN v_years > 1 THEN v_years_str || ' years'
                ELSE v_years_str || ' year'
            END;
        END IF;
    END IF;
    
    -- Return as table
    RETURN QUERY SELECT v_age_accurate;
END;
$$ LANGUAGE plpgsql;