-- PostgreSQL function equivalent of SQL Server GetDoctorComments stored procedure
-- This function gets doctor comments for a patient with optional date filtering

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create GetDoctorComments function
CREATE OR REPLACE FUNCTION dbo.GetDoctorComments(
    p_patient_id INTEGER,
    p_all_comments BOOLEAN,
    p_start_date TIMESTAMP DEFAULT NULL,
    p_end_date TIMESTAMP DEFAULT NULL
)
RETURNS TABLE(
    id bigint,
    comment text,
    patientrecordid integer,
    practicedoctorid integer,
    datecreated timestamp with time zone,
    isactive boolean,
    userid integer,
    datelastmodified timestamp with time zone,
    firstname text,
    lastname text
) AS $$
BEGIN
    IF p_all_comments THEN
        RETURN QUERY
        SELECT 
            dc.id,
            dc.comment,
            dc.patientrecordid,
            dc.practicedoctorid,
            dc.datecreated,
            dc.isactive,
            dc.userid,
            dc.datelastmodified,
            u.firstname,
            u.lastname
        FROM dbo.doctorcomments dc
        LEFT JOIN dbo.aspnetusers u ON dc.userid = u.userid
        WHERE dc.patientrecordid = p_patient_id
        ORDER BY dc.datecreated DESC;
    ELSE
        RETURN QUERY
        SELECT 
            dc.id,
            dc.comment,
            dc.patientrecordid,
            dc.practicedoctorid,
            dc.datecreated,
            dc.isactive,
            dc.userid,
            dc.datelastmodified,
            u.firstname,
            u.lastname
        FROM dbo.doctorcomments dc
        LEFT JOIN dbo.aspnetusers u ON dc.userid = u.userid
        WHERE dc.patientrecordid = p_patient_id
        AND dc.datecreated BETWEEN p_start_date AND p_end_date
        ORDER BY dc.datecreated DESC;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.GetDoctorComments TO postgres;