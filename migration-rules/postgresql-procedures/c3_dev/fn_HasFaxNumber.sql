-- PostgreSQL function migration of fn_HasFaxNumber
-- Original: fn_HasFaxNumber(@doctorId INT) RETURNS BIT
-- Purpose: Checks if a doctor has a fax number
-- Modified to return TABLE for repository pattern compatibility

CREATE OR REPLACE FUNCTION dbo.fn_HasFaxNumber(
    p_doctor_id INTEGER
)
RETURNS TABLE(
    hasfax BOOLEAN
) AS $$
DECLARE
    v_fax_number VARCHAR(20);
    v_result BOOLEAN;
BEGIN
    -- Select fax number for the doctor
    SELECT faxnumber
    INTO v_fax_number
    FROM dbo.externaldoctorphonenumbers
    WHERE externaldoctorid = p_doctor_id
    LIMIT 1;
    
    -- Determine if fax number exists and is not empty
    v_result := (v_fax_number IS NOT NULL AND TRIM(v_fax_number) != '');
    
    -- Return as table
    RETURN QUERY SELECT v_result;
END;
$$ LANGUAGE plpgsql;