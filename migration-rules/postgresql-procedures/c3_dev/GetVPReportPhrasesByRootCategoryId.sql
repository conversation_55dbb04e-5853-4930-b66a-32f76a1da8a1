-- PostgreSQL function migration of GetVPReportPhrasesByRootCategoryId stored procedure
-- Original: GetVPReportPhrasesByRootCategoryId(@rootCategoryId INT)
-- Purpose: Gets hierarchical VP report phrases by root category using recursive CTE

CREATE OR REPLACE FUNCTION dbo.GetVPReportPhrasesByRootCategoryId(
    p_root_category_id INTEGER
)
RETURNS TABLE(
    id integer,
    rootcategoryid integer,
    name text,
    value text,
    parent integer,
    parentname text,
    "order" integer,
    root integer,
    status integer,
    spec integer,
    level integer,
    drid integer,
    haschildren boolean
) AS $$
DECLARE
    v_status INTEGER := 0;
    v_status_st INTEGER := 2; -- status for opening statement
    v_opening_st VARCHAR(30) := 'Opening Statement';
    v_opening_st_id INTEGER;
BEGIN
    -- Get opening statement ID
    SELECT COALESCE(
        (SELECT p.id 
         FROM dbo.vpreportphrases p 
         WHERE p.status = 2 
           AND p.parent = -1 
           AND p.name = v_opening_st 
         LIMIT 1), 
        0
    ) INTO v_opening_st_id;

    RETURN QUERY
    -- Recursive CTE to get hierarchical report phrases
    WITH RECURSIVE ReportPhraseCTE AS (
        -- Base case: root categories
        SELECT 
            p.id,
            p.id as rootcategoryid,
            p.name,
            p.value,
            p.parent,
            p.name as parentname,
            0 as level,
            p."order",
            p.root,
            p.status,
            p.spec,
            p.drid
        FROM dbo.vpreportphrases p
        WHERE p.parent = -1
          AND (p.status = v_status OR p.status = v_status_st)
          AND p.name IS NOT NULL 
          AND TRIM(p.name) != ''
          AND p.id = p_root_category_id
        
        UNION ALL
        
        -- Recursive case: child phrases
        SELECT 
            vpr.id,
            cte.rootcategoryid,
            vpr.name,
            vpr.value,
            vpr.parent,
            cte.name as parentname,
            cte.level + 1,
            vpr."order",
            vpr.root,
            vpr.status,
            vpr.spec,
            vpr.drid
        FROM dbo.vpreportphrases vpr
        JOIN ReportPhraseCTE cte ON vpr.parent = cte.id
        WHERE (vpr.status = v_status OR vpr.status = v_status_st)
          AND vpr.name IS NOT NULL 
          AND TRIM(vpr.name) != ''
    )
    SELECT 
        vpr.id,
        vpr.rootcategoryid,
        vpr.name,
        vpr.value,
        vpr.parent,
        vpr.parentname,
        vpr."order",
        vpr.root,
        vpr.status,
        vpr.spec,
        vpr.level,
        vpr.drid,
        -- Check if has children
        CASE 
            WHEN EXISTS (
                SELECT 1 
                FROM dbo.vpreportphrases child 
                WHERE child.parent = vpr.id
            ) THEN true 
            ELSE false 
        END as haschildren
    FROM ReportPhraseCTE vpr
    ORDER BY vpr.level, vpr."order";
END;
$$ LANGUAGE plpgsql;