-- PostgreSQL function for SP_Get_Patient_Appointments_For_HCV_Response_Update
-- Migrated from SQL Server stored procedure
-- Returns patient appointments for HCV response updates

CREATE OR REPLACE FUNCTION dbo.SP_Get_Patient_Appointments_For_HCV_Response_Update(
    p_practice_id INTEGER,
    p_patient_record_id INTEGER,
    p_from_date TIMESTAMP,
    p_to_date TIMESTAMP
)
RETURNS TABLE(
    appointmentid integer,
    patientrecordid integer
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        A.Id::integer AS AppointmentId,
        A.PatientRecordId::integer
    FROM dbo.Appointments A
    JOIN dbo.PatientRecords P ON A.PatientRecordId = P.Id
    WHERE 
        P.PracticeId = p_practice_id 
        AND A.PatientRecordId = p_patient_record_id
        AND A.appointmentTime >= p_from_date 
        AND A.appointmentTime <= p_to_date
        AND A.IsActive = true
        AND A.appointmentStatus NOT IN (7, 1, 0, 16); -- cancelled, waitlist, cancellationlist, triage
END;
$$ LANGUAGE plpgsql;