-- PostgreSQL function migration of fn_GetSharedPath
-- Original: fn_GetSharedPath() RETURNS nvarchar(100)
-- Purpose: Returns network share location based on current database server and name
-- Modified to return TABLE for repository pattern compatibility

CREATE OR REPLACE FUNCTION dbo.fn_GetSharedPath()
RETURNS TABLE(
    share_location TEXT
) AS $$
DECLARE
    v_test_db TEXT := 'test_cer30';
    v_dev_db TEXT := 'dev_cer30';
    v_kmh_training_db TEXT := 'kmh_training_cer30';
    
    v_staged_db_server TEXT := 'staging-proddb';
    v_prod_db_server TEXT := 'c3-proddb';
    
    v_share_location TEXT := '';
    v_current_db_server TEXT;
    v_current_db TEXT;
BEGIN
    -- Get current database server and database name
    -- PostgreSQL equivalents of @@SERVERNAME and DB_NAME()
    SELECT inet_server_addr()::TEXT INTO v_current_db_server;
    SELECT current_database() INTO v_current_db;
    
    -- Convert to lowercase for comparison
    v_current_db := LOWER(v_current_db);
    v_current_db_server := COALESCE(LOWER(v_current_db_server), '');
    
    -- Determine share location based on database context
    CASE
        WHEN POSITION(v_dev_db IN v_current_db) > 0 THEN
            v_share_location := '//192.168.10.36/'; -- C3DevCo
        WHEN POSITION(v_test_db IN v_current_db) > 0 THEN
            v_share_location := '//192.168.10.32/'; -- C3TestCo
        WHEN POSITION(v_kmh_training_db IN v_current_db) > 0 THEN
            v_share_location := '//192.168.10.64/'; -- KMH_Training
        WHEN POSITION(v_prod_db_server IN v_current_db_server) > 0 THEN
            v_share_location := '//192.168.122.109/'; -- Production
        WHEN POSITION(v_staged_db_server IN v_current_db_server) > 0 THEN
            v_share_location := '//192.168.10.113/'; -- Staging
        ELSE
            v_share_location := '';
    END CASE;
    
    -- Return as table
    RETURN QUERY SELECT v_share_location;
END;
$$ LANGUAGE plpgsql;