CREATE OR REPLACE FUNCTION dbo.GetEconsultPatientReports(
    p_patient_record_id integer
)
RETURNS TABLE (
    "Id" integer,
    "url" text,
    "filename" text,
    "filesize" integer,
    "officeId" integer,
    "fileExtensionAndVersion" text,
    "receivedDateTime" timestamp,
    "description" text
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Return patient reports from multiple sources
    RETURN QUERY
    SELECT 
        z."Id",
        z."url",
        z."filename",
        z."filesize",
        z."officeId",
        z."fileExtensionAndVersion",
        z."receivedDateTime",
        z."description"
    FROM (
        -- External documents
        SELECT 
            rr."Id",
            rr."url",
            rr."fileName" as "filename",
            COALESCE(rr."fileSize", 0) as "filesize",
            rr."officeId",
            rr."fileExtensionAndVersion",
            rr."receivedDateTime",
            rr."description"
        FROM "reportreceiveds" rr 
        WHERE rr."PatientRecordId" = p_patient_record_id
        
        UNION ALL
        
        -- Sent letters
        SELECT 
            sr."Id",
            sr."URL" as "url",
            SUBSTRING(sr."URL" FROM '[^\\]+$') as "filename",
            0 as "filesize",
            a."officeId",
            SUBSTRING(sr."URL" FROM '[^.]+$') as "fileExtensionAndVersion",
            sr."DateEntered" as "receivedDateTime",
            'Letter' as "description"
        FROM "vp_sendreport" sr
        JOIN "appointments" a ON sr."appointmentID" = a."id"
        WHERE sr."SendTypeId" = 6
          AND sr."Sent" = 1
          AND sr."PatientRecordId" = p_patient_record_id
          
        UNION ALL
        
        -- Raw documents and Sent reports
        SELECT 
            sr."Id",
            sr."URL" as "url",
            SUBSTRING(sr."URL" FROM '[^\\]+$') as "filename",
            0 as "filesize",
            a."officeId",
            SUBSTRING(sr."URL" FROM '[^.]+$') as "fileExtensionAndVersion",
            sr."DateEntered" as "receivedDateTime",
            CASE 
                WHEN sr."SendTypeId" = 5 THEN 'Raw Document' 
                ELSE 'Report' 
            END as "description"
        FROM "ws_sendreport" sr
        JOIN "appointments" a ON sr."appointmentID" = a."id"
        WHERE (sr."SendTypeId" = 5 OR (sr."SendTypeId" = 6 AND sr."Sent" = 1))
          AND sr."Sent" = 1
          AND a."PatientRecordId" = p_patient_record_id
    ) z
    ORDER BY z."receivedDateTime" DESC;
END;
$$;