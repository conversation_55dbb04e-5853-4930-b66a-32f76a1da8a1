-- PostgreSQL function for usp_GetRadStudy
-- Migrated from SQL Server stored procedure
-- Gets radiology study list with comprehensive filtering including office filtering

CREATE OR REPLACE FUNCTION dbo.usp_GetRadStudy(
    p_accession_number VARCHAR(50) DEFAULT NULL,
    p_exam_date TIMESTAMP DEFAULT NULL,
    p_patient_id INTEGER DEFAULT NULL,
    p_patient_dob TIMESTAMP DEFAULT NULL,
    p_offices VARCHAR(500) DEFAULT NULL
)
RETURNS TABLE(
    id INTEGER,
    accessionnumber VARCHAR(100),
    studydate VARCHAR(50),
    patientfirstname VARCHAR(100),
    patientlastname VARCHAR(100),
    patientbd VARCHAR(50),
    officename VARCHAR(200),
    image_count BIGINT
) AS $$
DECLARE
    where_clause TEXT := '';
    init_dt TEXT;
    end_dt TEXT;
    office_names TEXT := NULL;
BEGIN
    -- Build WHERE clause dynamically based on provided parameters
    
    -- Patient ID filter
    IF p_patient_id IS NOT NULL AND p_patient_id > 0 THEN
        where_clause := where_clause || ' AND s.PatientId = ' || p_patient_id::TEXT;
    END IF;
    
    -- Accession Number filter
    IF p_accession_number IS NOT NULL AND TRIM(COALESCE(p_accession_number, '')) <> '' THEN
        where_clause := where_clause || ' AND s.AccessionNum = ' || QUOTE_LITERAL(p_accession_number);
    END IF;
    
    -- Exam Date filter (full day range)
    IF p_exam_date IS NOT NULL THEN
        init_dt := TO_CHAR(p_exam_date, 'YYYY-MM-DD') || ' 00:00:00';
        end_dt := TO_CHAR(p_exam_date, 'YYYY-MM-DD') || ' 23:59:59';
        where_clause := where_clause || ' AND s.StudyDate BETWEEN ' || 
                       QUOTE_LITERAL(init_dt) || ' AND ' || QUOTE_LITERAL(end_dt);
    END IF;
    
    -- Patient Date of Birth filter
    IF p_patient_dob IS NOT NULL THEN
        where_clause := where_clause || ' AND p.PatientBD = ' || 
                       QUOTE_LITERAL(TO_CHAR(p_patient_dob, 'YYYY-MM-DD HH24:MI:SS'));
    END IF;
    
    -- Office names filter (handles comma-separated list)
    IF p_offices IS NOT NULL THEN
        -- Convert comma-separated offices to quoted lowercase format for IN clause
        SELECT STRING_AGG(QUOTE_LITERAL(LOWER(TRIM(value))), ', ')
        INTO office_names
        FROM UNNEST(STRING_TO_ARRAY(p_offices, ',')) AS value
        WHERE TRIM(value) <> '';
        
        IF office_names IS NOT NULL THEN
            where_clause := where_clause || ' AND LOWER(I.IName) IN (' || office_names || ')';
        END IF;
    END IF;
    
    -- Execute the dynamic query using RETURN QUERY EXECUTE
    RETURN QUERY EXECUTE '
        SELECT 
            s.id,
            s.AccessionNum as accessionnumber,
            COALESCE(TO_CHAR(s.StudyDate, ''YYYY-MM-DD HH24:MI:SS''), '''') as studydate,
            COALESCE(p.PatientFirstName, '''') as patientfirstname,
            COALESCE(p.PatientLastName, '''') as patientlastname,
            COALESCE(TO_CHAR(p.PatientBD, ''YYYY-MM-DD HH24:MI:SS''), '''') as patientbd,
            I.IName as officename,
            COALESCE((
                SELECT COUNT(*)
                FROM dbo.RAD_Series ser
                INNER JOIN dbo.RAD_Image img ON img.SeriesId = ser.id
                WHERE ser.StudyId = s.id
            ), 0) as image_count
        FROM dbo.RAD_Study s 
        LEFT JOIN dbo.RAD_Institution I ON s.Institution = I.id 
        LEFT JOIN dbo.RAD_Patient p ON s.PatientId = p.id
        WHERE 1=1 ' || where_clause;
        
END;
$$ LANGUAGE plpgsql;