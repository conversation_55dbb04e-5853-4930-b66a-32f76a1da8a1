-- PostgreSQL function migration of fn_GetReportMedications
-- Original: fn_GetReportMedications(@practiceId, @patientId, @externalDoctorId, @appointmentTime, @isVp, @isForLetter)
-- Purpose: Helper function that retrieves patient medications with action types

CREATE OR REPLACE FUNCTION dbo.fn_getreportmedications(
    p_practice_id INTEGER,
    p_patient_id INTEGER,
    p_external_doctor_id INTEGER,
    p_appointment_time TIMESTAMP,
    p_is_vp BOOLEAN DEFAULT false,
    p_is_for_letter BOOLEAN DEFAULT false
)
RETURNS TABLE(
    patientmedicationid integer,
    patientid integer,
    medicationsetid integer,
    medicationname varchar(500),
    din varchar(50),
    dose varchar(50),
    strength varchar(50),
    form varchar(100),
    ingredients varchar(1500),
    sig varchar(50),
    route varchar(100),
    medicationnodinid integer,
    datestarted timestamp,
    datediscontinued timestamp,
    actiontype varchar(100),
    rownum integer
) AS $$
DECLARE
    v_lookup_date TIMESTAMP;
    v_is_visible_meds BOOLEAN := false;
    v_cpp_meds_category_id INTEGER := 7;
    
    -- Action type constants
    v_added_med VARCHAR(100) := 'Added';
    v_prior_med VARCHAR(100) := 'Prior';
    v_discontinued_med VARCHAR(100) := 'Discontinued';
    v_dose_change_med VARCHAR(100) := 'Dose Changed';
    v_active_med VARCHAR(100) := 'Active';
BEGIN
    -- Check if medications should be visible for VP
    IF p_is_vp = true THEN
        SELECT CASE WHEN COALESCE(cs.skip, 1) = 0 THEN true ELSE false END
        INTO v_is_visible_meds
        FROM dbo.vp_cpp_skipped cs 
        WHERE cs.userid = p_external_doctor_id 
          AND cs.cpp_category_id = v_cpp_meds_category_id
        LIMIT 1;
        
        v_is_visible_meds := COALESCE(v_is_visible_meds, false);
    ELSIF p_practice_id = 3 THEN -- only for practice 3 ticket #10084
        SELECT COALESCE(cs.visible, false)
        INTO v_is_visible_meds
        FROM dbo.vp_cpp_setting cs 
        WHERE cs.doctorid = p_external_doctor_id 
          AND cs.vp_cpp_category_id = v_cpp_meds_category_id
        LIMIT 1;
        
        v_is_visible_meds := COALESCE(v_is_visible_meds, false);
    END IF;

    -- Only return medications if they should be visible
    IF v_is_visible_meds = true THEN
        RETURN QUERY
        WITH temp_meds AS (
            SELECT 
                pm.id as patientmedicationid,
                pm.patientrecordid as patientid,
                pm.medicationsetid,
                pm.medicationname,
                pm.din,
                pm.dose,
                pm.strength,
                pm.form,
                pm.ingredients,
                pm.sig,
                pm.route,
                pm.medicationnodinid,
                pm.datestarted AT TIME ZONE 'UTC' as datestarted,
                pm.datediscontinued AT TIME ZONE 'UTC' as datediscontinued,
                pm.datecreated AT TIME ZONE 'UTC' as datecreated
            FROM dbo.patientmedications pm 
            WHERE pm.patientrecordid = p_patient_id 
              AND DATE(pm.datestarted) <= DATE(p_appointment_time)
              AND pm.isactive = true
            ORDER BY pm.datecreated DESC, pm.datediscontinued ASC NULLS LAST
        ),
        ranked_meds AS (
            SELECT 
                *,
                ROW_NUMBER() OVER (
                    PARTITION BY medicationsetid 
                    ORDER BY datecreated DESC
                ) AS row_num
            FROM temp_meds
        )
        SELECT 
            rm.patientmedicationid,
            rm.patientid,
            rm.medicationsetid,
            rm.medicationname,
            rm.din,
            rm.dose,
            rm.strength,
            rm.form,
            rm.ingredients,
            rm.sig,
            rm.route,
            rm.medicationnodinid,
            rm.datestarted,
            rm.datediscontinued,
            -- Complex CASE statement for medication action type
            CASE
                -- Dose changed: exists other med with same set, discontinued today, this one not discontinued
                WHEN EXISTS (
                    SELECT 1 FROM temp_meds tm2 
                    WHERE tm2.medicationsetid = rm.medicationsetid 
                      AND tm2.patientmedicationid != rm.patientmedicationid
                      AND DATE(tm2.datediscontinued) = DATE(p_appointment_time)
                ) AND rm.datediscontinued IS NULL 
                THEN v_dose_change_med
                
                -- Discontinued: no other meds with same set, discontinued <= appointment time
                WHEN NOT EXISTS (
                    SELECT 1 FROM temp_meds tm2 
                    WHERE tm2.medicationsetid = rm.medicationsetid 
                      AND tm2.patientmedicationid != rm.patientmedicationid
                ) AND DATE(rm.datediscontinued) <= DATE(p_appointment_time)
                THEN v_discontinued_med
                
                -- Discontinued: exists other meds with same set, this one discontinued <= appointment time
                WHEN EXISTS (
                    SELECT 1 FROM temp_meds tm2 
                    WHERE tm2.medicationsetid = rm.medicationsetid 
                      AND tm2.patientmedicationid != rm.patientmedicationid
                ) AND DATE(rm.datediscontinued) <= DATE(p_appointment_time)
                THEN v_discontinued_med
                
                -- Added: no other meds with same set, started = appointment time, not discontinued
                WHEN NOT EXISTS (
                    SELECT 1 FROM temp_meds tm2 
                    WHERE tm2.medicationsetid = rm.medicationsetid 
                      AND tm2.patientmedicationid != rm.patientmedicationid
                ) AND DATE(rm.datestarted) = DATE(p_appointment_time)
                  AND rm.datediscontinued IS NULL
                THEN v_added_med
                
                -- Prior: exists other med with same set, discontinued <= appointment time, this one not discontinued
                WHEN EXISTS (
                    SELECT 1 FROM temp_meds tm2 
                    WHERE tm2.medicationsetid = rm.medicationsetid 
                      AND tm2.patientmedicationid != rm.patientmedicationid
                      AND DATE(tm2.datediscontinued) <= DATE(p_appointment_time)
                ) AND rm.datediscontinued IS NULL
                THEN v_prior_med
                
                -- Prior: exists other med with same set, this one discontinued >= appointment time
                WHEN EXISTS (
                    SELECT 1 FROM temp_meds tm2 
                    WHERE tm2.medicationsetid = rm.medicationsetid 
                      AND tm2.patientmedicationid != rm.patientmedicationid
                ) AND DATE(rm.datediscontinued) >= DATE(p_appointment_time)
                THEN v_prior_med
                
                -- Prior: no other meds with same set, not discontinued, started < appointment time
                WHEN NOT EXISTS (
                    SELECT 1 FROM temp_meds tm2 
                    WHERE tm2.medicationsetid = rm.medicationsetid 
                      AND tm2.patientmedicationid != rm.patientmedicationid
                ) AND rm.datediscontinued IS NULL 
                  AND rm.datestarted < p_appointment_time
                THEN v_prior_med
                
                -- Prior: no other meds with same set, discontinued > appointment time
                WHEN NOT EXISTS (
                    SELECT 1 FROM temp_meds tm2 
                    WHERE tm2.medicationsetid = rm.medicationsetid 
                      AND tm2.patientmedicationid != rm.patientmedicationid
                ) AND rm.datediscontinued > p_appointment_time
                THEN v_prior_med
                
                -- Default to Prior
                ELSE v_prior_med
            END AS actiontype,
            rm.row_num::integer as rownum
        FROM ranked_meds rm
        WHERE rm.row_num = 1 
           OR DATE(rm.datediscontinued) = DATE(p_appointment_time)
        ORDER BY rm.datestarted DESC, rm.datecreated DESC;
    END IF;
    
    -- If medications are not visible, return empty result set
    RETURN;
END;
$$ LANGUAGE plpgsql;