-- PostgreSQL function equivalent of SQL Server GetPracticeWorkList_v2 stored procedure
-- This function gets a paginated work list for practice management

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create GetPracticeWorkList_v2 function
CREATE OR REPLACE FUNCTION dbo.GetPracticeWorkList_v2(
    p_practice_id INTEGER,
    p_office_id INTEGER,
    p_practice_doctor_id INTEGER,
    p_test_group_id INTEGER,
    p_test_status_ids INTEGER[] DEFAULT ARRAY[]::INTEGER[],
    p_appointment_type INTEGER,
    p_test_set_for_review BOOLEAN,
    p_is_abnormal BOOLEAN,
    p_tests_only_ext BOOLEAN,
    p_exclude_ecg BOOLEAN,
    p_from_date TIMESTAMP DEFAULT NULL,
    p_to_date TIMESTAMP DEFAULT NULL,
    p_page_num INTEGER,
    p_page_size INTEGER,
    p_selected_priority_ids INTEGER[] DEFAULT ARRAY[]::INTEGER[]
)
RETURNS TABLE(
    officeid integer,
    practiceid integer,
    officename text,
    appointmentid integer,
    appointmenttypeid integer,
    appointmenttypeparentid integer,
    appointmenttype text,
    appointmentstatus integer,
    practicedoctorid integer,
    doctorname text,
    referraldoctorid integer,
    referraldoctor text,
    patienid integer,
    patientfirstname text,
    patientlastname text,
    patientfhirid uuid,
    appointmenttestid integer,
    testid integer,
    testshortname text,
    testdate timestamp,
    teststatusid integer,
    teststatus text,
    isabnormal boolean,
    setforreview boolean,
    reassigndate timestamp,
    reassigndocid integer,
    reassigndocname text,
    isvp boolean,
    sentcompletely integer,
    priorityname text,
    totalrecords bigint
) AS $$
DECLARE
    v_appointment_status INTEGER := 1;
    v_cancelled_status INTEGER := 7;
    v_is_active BOOLEAN := true;
    v_max_rank INTEGER;
    v_default_max_rank INTEGER := 10;
    v_offset INTEGER;
BEGIN
    -- Get max rank for priority handling
    SELECT COALESCE(MAX(rank), v_default_max_rank) + 1
    INTO v_max_rank
    FROM dbo.appointmentpriority
    WHERE practiceid = p_practice_id AND isactive = true;

    -- Calculate pagination offset
    v_offset := (p_page_num - 1) * p_page_size;

    RETURN QUERY
    WITH appointment_tests_base AS (
        SELECT DISTINCT 
            ap.id as appointment_id,
            ap.officeid,
            o.practiceid,
            o.name as office_name,
            ap.appointmenttypeid,
            COALESCE(typ.appointmenttypeid, 0) as appointment_type_parent_id,
            typ.name as appointment_type,
            ap.appointmentstatus,
            ap.practicedoctorid,
            ap.referraldoctorid,
            ap.patientrecordid,
            apt.id as appointment_test_id,
            apt.starttime as test_date,
            apt.appointmentteststatusid as test_status_id,
            apt.isabnormal,
            apt.setforreview,
            apt.reassigndate,
            apt.reassigndocid,
            apt.testid,
            CASE 
                WHEN tg.groupid = 101 THEN true 
                ELSE false 
            END as is_vp,
            COALESCE(atr.practice_doctor_id, ap.practicedoctorid) as prac_doc_descr,
            ap.appointmentpriorityid,
            COALESCE(app_prior.rank, v_max_rank) as priority_rank
        FROM dbo.appointmenttests apt
        JOIN dbo.appointments ap ON apt.appointmentid = ap.id
        JOIN dbo.office o ON o.id = ap.officeid
        JOIN dbo.appointmenttypes typ ON ap.appointmenttypeid = typ.id
        JOIN dbo.testgroups tg ON tg.testid = apt.testid
        LEFT JOIN (
            SELECT 
                atr.appointmenttestid,
                prac_docs.id as practice_doctor_id
            FROM dbo.appointmenttestresources atr
            JOIN dbo.aspnetusers users ON atr.assignedtouserid = users.userid
            JOIN dbo.practicedoctors prac_docs ON users.id = prac_docs.applicationuserid
            WHERE prac_docs.practiceid = p_practice_id
            AND atr.isactive = true
        ) atr ON apt.id = atr.appointmenttestid
        LEFT JOIN dbo.appointmentpriority app_prior ON ap.appointmentpriorityid = app_prior.id
        WHERE o.practiceid = p_practice_id
        AND ap.appointmentstatus > v_appointment_status
        AND ap.appointmentstatus != v_cancelled_status
        AND ap.isactive = v_is_active
        AND apt.isactive = v_is_active
        -- Apply filters
        AND (p_office_id = 0 OR ap.officeid = p_office_id)
        AND (p_practice_doctor_id = 0 OR (
            ap.practicedoctorid = p_practice_doctor_id 
            OR apt.reassigndocid = p_practice_doctor_id 
            OR atr.practice_doctor_id = p_practice_doctor_id
        ))
        AND (p_test_group_id = 0 OR tg.groupid = p_test_group_id)
        AND (array_length(p_test_status_ids, 1) IS NULL OR apt.appointmentteststatusid = ANY(p_test_status_ids))
        AND (p_appointment_type = 0 OR ap.appointmenttypeid = p_appointment_type)
        AND (NOT p_test_set_for_review OR apt.setforreview = true)
        AND (NOT p_is_abnormal OR apt.isabnormal = true)
        AND (NOT p_tests_only_ext OR typ.id = 6) -- test only ext appointment type
        AND (NOT p_exclude_ecg OR apt.testid != 4) -- exclude ECG test
        AND (p_from_date IS NULL OR p_to_date IS NULL OR (apt.starttime >= p_from_date AND apt.starttime <= p_to_date))
        AND (
            array_length(p_selected_priority_ids, 1) IS NULL 
            OR ap.appointmentpriorityid = ANY(p_selected_priority_ids)
            OR (ap.appointmentpriorityid IS NULL AND NULL = ANY(p_selected_priority_ids))
        )
    ),
    paginated_results AS (
        SELECT *,
            COUNT(*) OVER() as total_records
        FROM appointment_tests_base
        ORDER BY priority_rank, test_date ASC
        LIMIT p_page_size OFFSET v_offset
    )
    SELECT 
        pr.officeid,
        pr.practiceid,
        pr.office_name::text,
        pr.appointment_id,
        pr.appointmenttypeid,
        pr.appointment_type_parent_id,
        pr.appointment_type::text,
        pr.appointmentstatus,
        pr.prac_doc_descr,
        COALESCE(ext_docs.lastname, '') || ' ' || COALESCE(ext_docs.firstname, '')::text,
        pr.referraldoctorid,
        COALESCE(
            (SELECT ref_ext.firstname || ' ' || ref_ext.lastname
             FROM dbo.externaldoctors ref_ext
             WHERE ref_ext.id = pr.referraldoctorid
             LIMIT 1),
            ''
        )::text,
        pr.patientrecordid,
        demo.firstname::text,
        demo.lastname::text,
        demo.fhirid,
        pr.appointment_test_id,
        tst.id,
        tst.testshortname::text,
        pr.test_date AT TIME ZONE 'UTC',
        pr.test_status_id,
        COALESCE(
            (SELECT status 
             FROM dbo.appointmentteststatus 
             WHERE id = pr.test_status_id 
             LIMIT 1),
            ''
        )::text,
        pr.isabnormal,
        pr.setforreview,
        pr.reassigndate AT TIME ZONE 'UTC',
        pr.reassigndocid,
        CASE 
            WHEN pr.reassigndocid > 0 THEN
                COALESCE(
                    (SELECT reassign_ext.firstname || ' ' || reassign_ext.lastname
                     FROM dbo.externaldoctors reassign_ext
                     JOIN dbo.practicedoctors pd ON reassign_ext.id = pd.externaldoctorid
                     WHERE pd.id = pr.reassigndocid
                     LIMIT 1),
                    ''
                )
            ELSE ''
        END::text,
        pr.is_vp,
        -- Sent completely logic (simplified - would need VP_SendReport and WS_SendReport tables)
        CASE 
            WHEN pr.is_vp THEN 0  -- VP logic would need VP_SendReport table
            ELSE 0  -- Worksheet logic would need WS_SendReport table
        END,
        app_prior.priorityname::text,
        pr.total_records
    FROM paginated_results pr
    JOIN dbo.practicedoctors prac_docs ON pr.prac_doc_descr = prac_docs.id
    JOIN dbo.externaldoctors ext_docs ON prac_docs.externaldoctorid = ext_docs.id
    JOIN dbo.demographics demo ON pr.patientrecordid = demo.patientrecordid
    JOIN dbo.tests tst ON pr.testid = tst.id
    LEFT JOIN dbo.appointmentpriority app_prior ON pr.appointmentpriorityid = app_prior.id
    ORDER BY pr.priority_rank, pr.test_date ASC;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.GetPracticeWorkList_v2 TO postgres;