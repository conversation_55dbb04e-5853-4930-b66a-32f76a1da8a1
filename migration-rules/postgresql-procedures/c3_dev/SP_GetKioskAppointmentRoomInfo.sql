-- PostgreSQL function equivalent of SQL Server SP_GetKioskAppointmentRoomInfo stored procedure
-- This function gets room information for kiosk appointments

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create SP_GetKioskAppointmentRoomInfo function
CREATE OR REPLACE FUNCTION dbo.SP_GetKioskAppointmentRoomInfo(
    p_appointment_id INTEGER
)
RETURNS TABLE(
    room text
) AS $$
DECLARE
    v_assigned_to_user_id INTEGER;
BEGIN
    -- Get the assigned user ID from appointment test resources
    SELECT ar.assignedtouserid INTO v_assigned_to_user_id
    FROM dbo.appointmenttestresources ar 
    JOIN dbo.appointmenttests apt ON ar.appointmenttestid = apt.id
    JOIN dbo.appointments app ON apt.appointmentid = app.id
    WHERE app.id = p_appointment_id
    ORDER BY apt.starttime
    LIMIT 1;

    -- Return room type based on user schedule
    RETURN QUERY
    SELECT t.type::text
    FROM dbo.schedulestaffnotes sn 
    JOIN dbo.officerooms ofr ON sn.officeroomid = ofr.id
    JOIN dbo.officeroomtypes t ON ofr.officeroomtypeid = t.id
    WHERE sn.userid = v_assigned_to_user_id 
    AND sn.date = CURRENT_DATE;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.SP_GetKioskAppointmentRoomInfo TO postgres;