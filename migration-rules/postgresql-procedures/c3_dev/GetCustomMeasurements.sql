-- PostgreSQL function equivalent of SQL Server GetCustomMeasurements stored procedure
-- This function gets custom measurements for a doctor with optional visibility filtering

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Helper function equivalent to fn_GetCustomMeasurements
CREATE OR REPLACE FUNCTION dbo.fn_getcustommeasurements(
    p_external_doctor_id INTEGER,
    p_custom_type INTEGER, -- 0 = vitals category, 1 = lab category
    p_visible_only BOOLEAN DEFAULT false
)
RETURNS TABLE(
    id bigint,
    name text,
    shortname text,
    "order" integer,
    units text,
    normal text,
    range1 numeric,
    range2 numeric,
    spec integer,
    "type" integer,
    externaldoctorid integer,
    status integer,
    options text,
    testcode text,
    cdid integer,
    valuetype integer,
    visible boolean,
    displayorder integer
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        um.id,
        um.name,
        um.shortname,
        um."order",
        um.units,
        um.normal,
        um.range1,
        um.range2,
        um.spec,
        um."type",
        um.drid as external_doctor_id,
        um.status,
        um.options,
        um.testcode,
        um.cdid,
        um.valuetype,
        COALESCE(cm.visible, false),
        COALESCE(cm.rank, 1000000)
    FROM dbo.vpuniquemeasurements um
    LEFT JOIN dbo.vp_measurements_patient cm ON um.id = cm.measurementid AND cm.docid = p_external_doctor_id
    WHERE um.status = 0
    AND um.spec = 0
    AND um."type" = p_custom_type
    AND (
        (p_visible_only AND cm.visible = true)
        OR (NOT p_visible_only)
    );
END;
$$ LANGUAGE plpgsql;

-- Create GetCustomMeasurements function
CREATE OR REPLACE FUNCTION dbo.GetCustomMeasurements(
    p_external_doctor_id INTEGER,
    p_custom_type INTEGER,
    p_visible_only BOOLEAN DEFAULT false
)
RETURNS TABLE(
    id bigint,
    name text,
    shortname text,
    "order" integer,
    units text,
    normal text,
    range1 numeric,
    range2 numeric,
    spec integer,
    "type" integer,
    externaldoctorid integer,
    status integer,
    options text,
    testcode text,
    cdid integer,
    valuetype integer,
    visible boolean,
    displayorder integer
) AS $$
BEGIN
    RETURN QUERY
    SELECT *
    FROM dbo.fn_getcustommeasurements(p_external_doctor_id, p_custom_type, p_visible_only) cm
    ORDER BY cm.displayorder, cm.name;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.fn_getcustommeasurements TO postgres;
GRANT EXECUTE ON FUNCTION dbo.GetCustomMeasurements TO postgres;