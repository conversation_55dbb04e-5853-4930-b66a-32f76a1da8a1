-- PostgreSQL function for Get<PERSON>racticePhrasesAdmin
-- Migrated from SQL Server stored procedure
-- Gets practice phrases hierarchically for admin with breadcrumb navigation

CREATE OR REPLACE FUNCTION dbo.GetPracticePhrasesAdmin(
    p_group_id INTEGER,
    p_practice_id INTEGER,
    p_practice_template_id INTEGER,
    p_root_category_id INTEGER
)
RETURNS TABLE(
    pracrootcatphraseid INTEGER,
    rootcategoryphraseid INTEGER,
    pracrootcategorytempid INTEGER,
    roottemplateid INTEGER,
    rootcategoryid INTEGER,
    categoryname TEXT,
    groupid INTEGER,
    phrasename TEXT,
    phrasevalue TEXT,
    parentid INTEGER,
    issubcategory BOOLEAN,
    breadcrum TEXT,
    isactivepracticephrase BOOLEAN,
    level INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH phrase_data AS (
        SELECT 
            COALESCE(prcp.PracRootCategoryPhraseId, 0) AS pracrootcategoryId,
            p_practice_template_id as pracrootcategorytempid,
            rcp.Id as rootcategoryphraseid,
            rcp.PhraseName as phrasename,
            rcp.PhraseValue as phrasevalue,
            rcp.ParentId as parentid,
            rcp.IsSubCategory as issubcategory,
            rc.Id as rootcategoryid,
            rc.CategoryName as categoryname,
            rc.GroupId as groupid,
            COALESCE(prcp.TemplateId, 0) AS roottemplateid,
            COALESCE(prcp.IsActivePracticePhrase, FALSE) AS isactivepracticephrase
        FROM dbo.RootCategoryPhrases rcp 
        JOIN dbo.RootCategories rc ON rcp.RootCategoryId = rc.Id
        LEFT JOIN (
            SELECT 
                prp.Id AS PracRootCategoryPhraseId,
                prp.PracRootCategoryTempId,
                pt.TemplateId,
                prp.IsActive AS IsActivePracticePhrase,
                prp.RootCategoryPhraseId 
            FROM dbo.PracticeRootCategoryPhrases prp 
            JOIN dbo.PracticeRootCategoryTemplates pt ON prp.PracRootCategoryTempId = pt.Id
            WHERE prp.PracRootCategoryTempId = p_practice_template_id
        ) prcp ON prcp.RootCategoryPhraseId = rcp.Id
        WHERE rc.GroupId = p_group_id
        AND rcp.RootCategoryId = p_root_category_id
        AND rcp.IsActive = TRUE
    ),
    RECURSIVE CustomPhraseCTE AS (
        -- Base case - root level phrases (no parent)
        SELECT 
            phrases.pracrootcategoryId as pracrootcatphraseid,
            phrases.rootcategoryphraseid,
            phrases.pracrootcategorytempid,
            phrases.roottemplateid,
            phrases.rootcategoryid,
            phrases.categoryname,
            phrases.groupid,
            phrases.phrasename,
            phrases.phrasevalue,
            phrases.parentid,
            phrases.issubcategory,
            CAST(phrases.categoryname || ' -> ' || phrases.phrasename AS TEXT) as breadcrum,
            phrases.isactivepracticephrase,
            0 as level
        FROM phrase_data phrases        
        WHERE phrases.parentid IS NULL        
        
        UNION ALL
        
        -- Recursive case - child phrases
        SELECT 
            phrases.pracrootcategoryId as pracrootcatphraseid,
            phrases.rootcategoryphraseid,
            cte.pracrootcategorytempid,
            cte.roottemplateid,
            cte.rootcategoryid,
            cte.categoryname,
            cte.groupid,
            phrases.phrasename,
            phrases.phrasevalue,
            phrases.parentid,
            phrases.issubcategory,
            CAST(cte.breadcrum || ' -> ' || phrases.phrasename AS TEXT) as breadcrum,
            phrases.isactivepracticephrase,
            cte.level + 1 as level
        FROM phrase_data phrases
        JOIN CustomPhraseCTE cte ON phrases.parentid = cte.rootcategoryphraseid                
        WHERE cte.rootcategoryid = phrases.rootcategoryid
    )
    SELECT 
        cte.pracrootcatphraseid,
        cte.rootcategoryphraseid,
        cte.pracrootcategorytempid,
        cte.roottemplateid,
        cte.rootcategoryid,
        cte.categoryname,
        cte.groupid,
        cte.phrasename,
        cte.phrasevalue,
        COALESCE(cte.parentid, 0) AS parentid,
        cte.issubcategory,
        cte.breadcrum,
        cte.isactivepracticephrase,
        cte.level
    FROM CustomPhraseCTE cte 
    ORDER BY cte.parentid ASC, cte.categoryname ASC, cte.phrasename ASC;
    
END;
$$ LANGUAGE plpgsql;