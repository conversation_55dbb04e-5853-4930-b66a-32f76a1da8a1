-- PostgreSQL function equivalent of SQL Server SearchPatientsByOldChartNumber stored procedure
-- This function searches for patients by their old/legacy chart numbers

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create function in dbo schema for compatibility
CREATE OR REPLACE FUNCTION dbo.SearchPatientsByOldChartNumber(
    p_practice_id INTEGER,
    p_chart_number VARCHAR(200) DEFAULT NULL,
    p_top_result INTEGER DEFAULT 100
)
RETURNS TABLE(
    patientid integer,
    firstname text,
    lastname text,
    middlename text,
    dateofbirth timestamp,
    gender integer,
    practiceid integer,
    defaultpaymentmethod integer,
    ohip text,
    ohipversioncode text,
    active integer,
    uniquechartnumber text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.patientrecordid::integer,
        d.firstname::text,
        d.lastname::text,
        COALESCE(d.middlename, '')::text,
        d.dateofbirth AT TIME ZONE 'UTC',
        d.gender,
        p.practiceid::integer,
        d.defaultpaymentmethod,
        COALESCE(hc.number, '')::text,
        COALESCE(hc.version, '')::text,
        d.active,
        d.uniquevendoridsequence::text
    FROM dbo.patientrecords p
    JOIN dbo.demographics d ON p.id = d.patientrecordid
    LEFT JOIN (
        SELECT DISTINCT ON (demographicid) 
            demographicid, 
            number, 
            version
        FROM dbo.demographicshealthcards 
        ORDER BY demographicid, id DESC
    ) hc ON d.id = hc.demographicid
    WHERE p.practiceid = p_practice_id
    AND (p_chart_number IS NULL OR 
         LOWER(d.uniquevendoridsequence) LIKE LOWER(p_chart_number || '%'))
    AND d.active = 0  -- Only active patients
    ORDER BY d.id DESC
    LIMIT p_top_result;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.SearchPatientsByOldChartNumber TO postgres;