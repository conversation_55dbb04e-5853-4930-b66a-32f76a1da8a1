-- PostgreSQL function equivalent of SQL Server GetDaysheetCohorts stored procedure
-- This function provides the same functionality as the SQL Server version but uses PostgreSQL syntax

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create function in dbo schema for compatibility
CREATE OR REPLACE FUNCTION dbo.GetDaysheetCohorts(
    p_patient_ids INTEGER[] DEFAULT ARRAY[]::INTEGER[]
)
RETURNS TABLE(
    id integer,
    patientid integer,
    started timestamp with time zone,
    terminated timestamp with time zone,
    doctorid integer,
    officeid integer,
    notes text,
    cohortid integer,
    cohort text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pc.id::integer,
        pc.patientid::integer,
        pc.started::timestamp with time zone AS started,
        pc.terminated::timestamp with time zone AS terminated,
        pc.doctorid::integer,
        pc.officeid::integer,
        pc.notes::text,
        pc.cohortid::integer,
        c.description::text as cohort
    FROM dbo.patientcohorts pc
    JOIN dbo.cohorts c ON pc.cohortid = c.id
    WHERE pc.patientid = ANY(p_patient_ids);
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.GetDaysheetCohorts TO postgres;