-- PostgreSQL function for Get_VP_Options stored procedure
-- Migrated from SQL Server procedure Get_VP_Options

CREATE OR REPLACE FUNCTION dbo.Get_VP_Options()
RETURNS TABLE(
    id integer,
    optiontxt text,
    spec integer,
    grp integer
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        vo.Id::integer,
        vo.OptionTxt::text,
        vo.Spec::integer,
        vo.Grp::integer
    FROM dbo.VPOptions vo
    WHERE vo.Spec = 0 AND vo.Grp = 0;
END;
$$ LANGUAGE plpgsql;