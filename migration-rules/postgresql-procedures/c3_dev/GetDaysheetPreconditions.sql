-- PostgreSQL function equivalent of SQL Server GetDaysheetPreconditions stored procedure
-- This function provides the same functionality as the SQL Server version but uses PostgreSQL syntax

CREATE OR REPLACE FUNCTION dbo.getdaysheetpreconditions(
    p_appointment_ids integer[] DEFAULT ARRAY[]::integer[]
)
RETURNS TABLE (
    id integer,
    type text,
    status boolean,
    appointmentid integer
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Return appointment preconditions for the specified appointment IDs
    RETURN QUERY
    SELECT 
        ap.id::integer,
        ap.type::text,
        ap.status,
        ap.appointmentid::integer
    FROM dbo.appointmentpreconditons ap
    WHERE ap.appointmentid = ANY(p_appointment_ids);
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.getdaysheetpreconditions TO postgres;