CREATE OR REPLACE FUNCTION dbo.GetUnmappedLOINC(
    p_page_number INTEGER DEFAULT 1,
    p_page_size INTEGER DEFAULT 0,
    INOUT p_rowcount INTEGER DEFAULT 0
)
RETURNS TABLE(
    id INTEGER,
    labcode TEXT,
    labtestname TEXT,
    sendingapp TEXT
) AS $$
DECLARE
    v_offset INTEGER;
BEGIN
    -- Calculate offset for pagination
    v_offset := (p_page_number - 1) * p_page_size;
    
    -- Get total count on first page
    IF p_page_number = 1 THEN
        SELECT COUNT(*)
        INTO p_rowcount
        FROM (
            SELECT DISTINCT hr.testCodeIdentifier as labCode, 
                           hr.testDescription as labTestName, 
                           0 as id, 
                           m.sendingApp
            FROM dbo.HL7Result hr 
            JOIN dbo.HL7ReportVersion hrv ON hr.HL7ReportVersionId = hrv.id 
            JOIN dbo.HL7Message m ON hrv.HL7MessageId = m.Id
            WHERE NOT EXISTS (
                SELECT 1 FROM dbo.HL7Coding 
                WHERE labcode = hr.testCodeIdentifier
            )
        ) z;
    END IF;

    -- Return paginated results
    RETURN QUERY
    SELECT DISTINCT 
        0 AS id,
        hr.testCodeIdentifier AS labcode,
        hr.testDescription AS labtestname,
        m.sendingApp AS sendingapp
    FROM dbo.HL7Result hr 
    JOIN dbo.HL7ReportVersion hrv ON hr.HL7ReportVersionId = hrv.id 
    JOIN dbo.HL7Message m ON hrv.HL7MessageId = m.Id
    WHERE NOT EXISTS (
        SELECT 1 FROM dbo.HL7Coding 
        WHERE labcode = hr.testCodeIdentifier
    )
    ORDER BY hr.testCodeIdentifier
    OFFSET v_offset
    LIMIT CASE WHEN p_page_size > 0 THEN p_page_size ELSE NULL END;
END;
$$ LANGUAGE plpgsql;