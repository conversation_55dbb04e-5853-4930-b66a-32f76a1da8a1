-- PostgreSQL function for GetDoctorRootCategoryTemplates
-- Migrated from SQL Server stored procedure
-- Gets doctor root category templates by practice, group, and external doctor

CREATE OR REPLACE FUNCTION dbo.GetDoctorRootCategoryTemplates(
    p_practice_id INTEGER,
    p_group_id INTEGER,
    p_external_doctor_id INTEGER
)
RETURNS TABLE(
    doctorpracticetemplateid integer,
    practicetemplateid integer,
    practiceid integer,
    datecreated timestamp,
    datelastmodified timestamp,
    lastmodifiedbyuserid integer,
    templateid integer,
    templatename varchar,
    issystem boolean,
    isactive boolean,
    isactivedoctortemplate boolean,
    isdefault boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        drt.Id AS DoctorPracticeTemplateId,
        prt.Id AS PracticeTemplateId,
        prt.PracticeId,
        prt.DateCreated,
        prt.DateLastModified,
        prt.LastModifiedByUserId,
        prt.TemplateId,
        rt.TemplateName,
        rt.IsSystem,
        prt.IsActive,
        drt.IsActive AS IsActiveDoctorTemplate,
        drt.IsDefault
    FROM dbo.DoctorRootCategoryTemplates drt
    JOIN dbo.PracticeRootCategoryTemplates prt ON drt.PracRootCategoryTempId = prt.Id
    JOIN dbo.RootTemplates rt ON prt.TemplateId = rt.Id
    WHERE prt.PracticeId = p_practice_id
        AND rt.IsActive = true
        AND drt.ExternalDoctorId = p_external_doctor_id
        AND rt.GroupId = p_group_id;
END;
$$ LANGUAGE plpgsql;