-- PostgreSQL function for Get_VP_PrivacyNotes
-- Migrated from SQL Server stored procedure
-- Returns privacy notes for a specific user and patient

CREATE OR REPLACE FUNCTION dbo.Get_VP_PrivacyNotes(
    p_user_id INTEGER,
    p_patient_id INTEGER
)
RETURNS TABLE(
    Id INTEGER,
    Note VARCHAR(1024),
    PatientId INTEGER,
    UserId INTEGER,
    DateEntered TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.Id,
        p.Notes AS Note,
        p.PatientRecordId AS PatientId,
        p.UserId,
        p.DateEntered
    FROM 
        VP_Privacy_Notes p 
    WHERE
        p.UserId = p_user_id AND 
        p.PatientRecordId = p_patient_id 
    ORDER BY p.Id DESC;
END;
$$ LANGUAGE plpgsql;