CREATE OR REPLACE FUNCTION dbo.GetEConsults(
    p_requester_practitioner_id TEXT
)
RETURNS TABLE(
    id INTEGER,
    subjectline TEXT,
    notes TEXT,
    draftstatus BOOLEAN,
    datecreated TIMESTAMP,
    datelastmodified TIMESTAMP,
    patientrecordid INTEGER,
    patientfirstname TEXT,
    patientlastname TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        E.Id,
        E.SubjectLine,
        E.Notes,
        E.DraftStatus,
        E.DateCreated AT TIME ZONE 'UTC',
        E.DateLastModified AT TIME ZONE 'UTC',
        D.PatientRecordId,
        D.firstName AS PatientFirstName,
        D.lastName AS PatientLastName
    FROM dbo.Econsults E
    JOIN dbo.Demographics D ON D.PatientRecordId = E.PatientId
    WHERE E.RequesterPractitionerId = p_requester_practitioner_id;
END;
$$ LANGUAGE plpgsql;