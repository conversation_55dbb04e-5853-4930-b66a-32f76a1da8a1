-- PostgreSQL function migration of SP_VP_LabResults_Acc stored procedure
-- Original: SP_VP_LabResults_Acc(@patientRecordId INT, @accessionNumber INT = NULL, @fromDate DATETIME = NULL)
-- Purpose: Gets VP lab results with accession numbers

CREATE OR REPLACE FUNCTION dbo.SP_VP_LabResults_Acc(
    p_patient_record_id INTEGER,
    p_accession_number INTEGER DEFAULT NULL,
    p_from_date TIMESTAMP DEFAULT NULL
)
RETURNS TABLE(
    id integer,
    patientrecordid integer,
    appointmentid integer,
    accessionnumber integer,
    testname text,
    testresult text,
    units text,
    referencerange text,
    testdate timestamp,
    status text,
    resultflag text,
    testcode text,
    loinccode text,
    numericresult decimal,
    comments text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        lr.id,
        lr.patientrecordid,
        lr.appointmentid,
        lr.accessionnumber,
        lr.testname,
        lr.testresult,
        lr.units,
        lr.referencerange,
        lr.testdate AT TIME ZONE 'UTC' as testdate,
        lr.status,
        lr.resultflag,
        lr.testcode,
        lr.loinccode,
        lr.numericresult,
        lr.comments
    FROM dbo.labresults lr
    WHERE lr.patientrecordid = p_patient_record_id
      AND (p_accession_number IS NULL OR lr.accessionnumber = p_accession_number)
      AND (p_from_date IS NULL OR lr.testdate >= p_from_date)
    ORDER BY lr.testdate DESC, lr.accessionnumber DESC;
END;
$$ LANGUAGE plpgsql;