-- SP_Get_Patient_VP_CPP_Immunization_Types procedure for PostgreSQL
-- Migrated from SQL Server stored procedure
-- Gets VP CPP immunization types for a specific patient with optional name filter

CREATE OR REPLACE FUNCTION dbo.SP_Get_Patient_VP_CPP_Immunization_Types(
    p_practice_id integer,
    p_patient_record_id integer,
    p_immunization_name text DEFAULT NULL
)
RETURNS TABLE (
    typeid integer,
    name text
)
LANGUAGE plpgsql
AS $$
BEGIN
    IF p_immunization_name IS NOT NULL THEN
        RETURN QUERY
        SELECT DISTINCT 
            imm.vp_cpp_immunizationtypeid AS typeid,
            immt.name
        FROM vp_cpp_immunization imm
        JOIN vp_cpp_immunizationtype immt ON imm.vp_cpp_immunizationtypeid = immt.id
        JOIN patientrecords pr ON imm.patientrecordid = pr.id
        WHERE pr.practiceid = p_practice_id 
            AND imm.patientrecordid = p_patient_record_id 
            AND immt.name = p_immunization_name;
    ELSE
        RETURN QUERY
        SELECT DISTINCT 
            imm.vp_cpp_immunizationtypeid AS typeid,
            immt.name
        FROM vp_cpp_immunization imm
        JOIN vp_cpp_immunizationtype immt ON imm.vp_cpp_immunizationtypeid = immt.id
        JOIN patientrecords pr ON imm.patientrecordid = pr.id
        WHERE pr.practiceid = p_practice_id 
            AND imm.patientrecordid = p_patient_record_id;
    END IF;
END;
$$;