-- PostgreSQL function for SP_OfficeStaffNotes
-- Migrated from SQL Server stored procedure
-- Gets office staff notes for a selected date with user type filtering

CREATE OR REPLACE FUNCTION dbo.SP_OfficeStaffNotes(
    p_practice_id INTEGER,
    p_office_id INTEGER,
    p_selected_date DATE,
    p_filter_user_types INTEGER[]
)
RETURNS TABLE(
    userid INTEGER,
    firstname VA<PERSON>HA<PERSON>(200),
    lastname <PERSON><PERSON><PERSON><PERSON>(200),
    usertype INTEGER,
    officeid INTEGER,
    date DATE,
    notes VARCHAR(300),
    officeroomid INTEGER
) AS $$
BEGIN
    -- Check if filter has values (equivalent to checking if first element > 0)
    IF COALESCE(array_length(p_filter_user_types, 1), 0) > 0 AND p_filter_user_types[1] > 0 THEN
        -- Filtered query
        RETURN QUERY
        SELECT DISTINCT
            U.UserID as userid,
            U.FirstName as firstname,
            U.LastName as lastname,
            U.CerebrumUserType as usertype,
            UO.OfficeId as officeid,
            COALESCE(N.Date, p_selected_date) as date,
            N.Notes as notes,
            <PERSON><PERSON>Office<PERSON>oomId as officeroomid
        FROM dbo.AspNetUsers U 
        JOIN dbo.UserOffices UO ON U.Id = UO.ApplicationUserId
        JOIN dbo.ScheduleUsers SCHU ON U.UserID = SCHU.UserId
        LEFT JOIN (
            SELECT Note.Date, Note.userid, Note.Notes, Note.OfficeRoomId 
            FROM dbo.ScheduleStaffNotes Note 
            WHERE Note.Date = p_selected_date
        ) AS N ON U.UserID = N.UserId
        WHERE U.CerebrumUserType = ANY(p_filter_user_types)
            AND U.PracticeId = p_practice_id 
            AND UO.OfficeId = p_office_id 
            AND EXISTS(
                SELECT 1 
                FROM dbo.ScheduleWeekDays wd 
                WHERE wd.ScheduleUserId = SCHU.Id
                LIMIT 1
            )
        ORDER BY U.LastName DESC;
    ELSE
        -- Unfiltered query
        RETURN QUERY
        SELECT DISTINCT
            U.UserID as userid,
            U.FirstName as firstname,
            U.LastName as lastname,
            U.CerebrumUserType as usertype,
            UO.OfficeId as officeid,
            COALESCE(N.Date, p_selected_date) as date,
            N.Notes as notes,
            N.OfficeRoomId as officeroomid
        FROM dbo.AspNetUsers U 
        JOIN dbo.UserOffices UO ON U.Id = UO.ApplicationUserId
        JOIN dbo.ScheduleUsers SCHU ON U.UserID = SCHU.UserId
        LEFT JOIN (
            SELECT Note.Date, Note.userid, Note.Notes, Note.OfficeRoomId 
            FROM dbo.ScheduleStaffNotes Note 
            WHERE Note.Date = p_selected_date
        ) AS N ON U.UserID = N.UserId
        WHERE U.PracticeId = p_practice_id 
            AND UO.OfficeId = p_office_id 
            AND EXISTS(
                SELECT 1 
                FROM dbo.ScheduleWeekDays wd 
                WHERE wd.ScheduleUserId = SCHU.Id
                LIMIT 1
            )
        ORDER BY U.LastName DESC;
    END IF;
END;
$$ LANGUAGE plpgsql;