CREATE OR REPLACE FUNCTION dbo.SP_UserAssociateBillingDoctors(
    p_practice_id INTEGER,
    p_user_id_guid TEXT
)
RETURNS TABLE(
    id INTEGER,
    practicedoctorid INTEGER,
    firstname TEXT,
    lastname TEXT,
    specialityid INTEGER
) AS $$
DECLARE
    v_active INTEGER := 1;
    v_hospital_daysheet_admin INTEGER := 178;
    v_hospital_personal_daysheet_admin INTEGER := 179;
    v_has_hospital_daysheet_admin BOOLEAN := false;
    v_has_hospital_personal_daysheet_admin BOOLEAN := false;
BEGIN
    -- Check if user has HospitalDaysheetAdmin permission
    SELECT EXISTS(
        SELECT 1 
        FROM dbo.aspnetusers u 
        JOIN dbo.aspnetuserroles ur ON u.id = ur.userid
        JOIN dbo.aspnetroles r ON ur.roleid = r.id
        JOIN dbo.rolepermissions rp ON r.id = rp.applicationroleid
        WHERE u.id = p_user_id_guid 
        AND rp.permissionid = v_hospital_daysheet_admin
    ) INTO v_has_hospital_daysheet_admin;
    
    -- Check if user has HospitalPersonalDaysheetAdmin permission
    SELECT EXISTS(
        SELECT 1 
        FROM dbo.aspnetusers u 
        JOIN dbo.aspnetuserroles ur ON u.id = ur.userid
        JOIN dbo.aspnetroles r ON ur.roleid = r.id
        JOIN dbo.rolepermissions rp ON r.id = rp.applicationroleid
        WHERE u.id = p_user_id_guid 
        AND rp.permissionid = v_hospital_personal_daysheet_admin
    ) INTO v_has_hospital_personal_daysheet_admin;
    
    -- Return doctors based on permissions
    IF v_has_hospital_daysheet_admin THEN
        -- Return all active doctors for the practice
        RETURN QUERY
        SELECT DISTINCT
            exd.id,
            pd.id AS practicedoctorid,
            exd.firstname,
            exd.lastname,
            exd.specialtyid AS specialityid
        FROM dbo.externaldoctors exd
        JOIN dbo.practicedoctors pd ON exd.id = pd.externaldoctorid
        WHERE pd.practiceid = p_practice_id 
        AND exd.active = v_active 
        AND pd.isactive = v_active
        ORDER BY exd.lastname;
        
    ELSIF v_has_hospital_personal_daysheet_admin THEN
        -- Return doctors associated with this specific user
        RETURN QUERY
        SELECT DISTINCT
            exd.id,
            pd.id AS practicedoctorid,
            exd.firstname,
            exd.lastname,
            exd.specialtyid AS specialityid
        FROM dbo.externaldoctors exd
        JOIN dbo.practicedoctors pd ON exd.id = pd.externaldoctorid
        WHERE pd.practiceid = p_practice_id 
        AND (
            (pd.applicationuserid = p_user_id_guid AND exd.active = v_active AND pd.isactive = v_active)
            OR exd.id IN (
                SELECT ubd.externaldoctorid 
                FROM dbo.userbillingdoctors ubd 
                WHERE ubd.applicationuserid = p_user_id_guid
            )
        )
        ORDER BY exd.lastname;
    END IF;
END;
$$ LANGUAGE plpgsql;