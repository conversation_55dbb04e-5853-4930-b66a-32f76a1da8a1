-- GetWaitlistTests procedure for PostgreSQL
-- Migrated from SQL Server stored procedure

CREATE OR REPLACE FUNCTION dbo.GetWaitlistTests(
    p_appointment_ids integer[]
)
RETURNS TABLE (
    id integer,
    testid integer,
    starttime timestamp,
    appointmentteststatsusid integer,
    testduration integer,
    billstatusid integer,
    referraldoctorid integer,
    appointmentid integer,
    accessionnumber varchar(300),
    physiciancomments text,
    techniciancomments text,
    isactive boolean,
    datecreated timestamp,
    dateupdated timestamp,
    setforreview boolean,
    reassigndocid integer,
    reassigndate timestamp,
    testfullname varchar(500),
    testshortname varchar(300),
    requiredevice boolean,
    teststatuscolor varchar(100),
    teststatus varchar(300)
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        apptest.id,
        apptest.testid,
        apptest.starttime,
        apptest.appointmentteststatsusid,
        apptest.testduration,
        apptest.billstatusid,
        apptest.referraldoctorid,
        apptest.appointmentid,
        COALESCE(apptest.accessionnumber, '') AS accessionnumber,
        COALESCE(apptest.physiciancomments, '') AS physiciancomments,
        COALESCE(apptest.techniciancomments, '') AS techniciancomments,
        apptest.isactive,
        apptest.datecreated,
        apptest.dateupdated,
        apptest.setforreview,
        apptest.reassigndocid,
        apptest.reassigndate,
        test.testfullname,
        test.testshortname,
        test.requiredevice,
        teststatus.color AS teststatuscolor,
        teststatus.status AS teststatus
    FROM appointmenttests apptest
    JOIN unnest(p_appointment_ids) AS app_ids ON apptest.appointmentid = app_ids
    JOIN appointmentteststatus teststatus ON apptest.appointmentteststatsusid = teststatus.id
    JOIN tests test ON apptest.testid = test.id  
    WHERE apptest.isactive = true;
END;
$$;