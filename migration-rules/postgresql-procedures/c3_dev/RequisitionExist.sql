-- PostgreSQL function for RequisitionExist
-- Migrated from SQL Server stored procedure  
-- Checks if requisitions exist for a patient

CREATE OR REPLACE FUNCTION dbo.RequisitionExist(
    p_patient_record_id INTEGER,
    p_requisition_type VARCHAR(64)
)
RETURNS TABLE(exists boolean) AS $$
DECLARE
    requisition_exist BOOLEAN := false;
    requisition_type_internal INTEGER := 2;
    requisition_status_ordered INTEGER := 1;
    requisition_status_arranged INTEGER := 2;
BEGIN
    IF p_requisition_type = '' OR p_requisition_type IS NULL THEN
        -- Check for any active requisitions excluding internal type
        IF EXISTS (
            SELECT a.id 
            FROM dbo.RequisitionPatient a 
            INNER JOIN dbo.Requisition b ON a.id = b.requisitionPatientId
            WHERE a.PatientRecordId = p_patient_record_id 
                AND b.IsActive = true 
                AND b.requisitionTypeId <> requisition_type_internal
                AND (b.requisitionStatus = requisition_status_ordered 
                     OR b.requisitionStatus = requisition_status_arranged)
        ) THEN
            requisition_exist := true;
        END IF;
    ELSE
        -- Check for specific requisition type
        IF EXISTS (
            SELECT a.id 
            FROM dbo.RequisitionPatient a 
            INNER JOIN dbo.Requisition b ON a.id = b.requisitionPatientId
            INNER JOIN dbo.RequisitionType c ON b.requisitionTypeId = c.id
            WHERE a.PatientRecordId = p_patient_record_id 
                AND b.IsActive = true 
                AND LOWER(c.name) = LOWER(p_requisition_type)
                AND (b.requisitionStatus = requisition_status_ordered 
                     OR b.requisitionStatus = requisition_status_arranged)
        ) THEN
            requisition_exist := true;
        END IF;
    END IF;

    RETURN QUERY SELECT requisition_exist;
END;
$$ LANGUAGE plpgsql;