-- TAPP_GetPracticeDoctors procedure for PostgreSQL
-- Migrated from SQL Server stored procedure
-- Gets practice doctors associated with TAPP studies

CREATE OR REPLACE FUNCTION dbo.TAPP_GetPracticeDoctors()
RETURNS TABLE (
    practicedoctorid integer,
    practicedoctorname text
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pd.id AS practicedoctorid,
        'Dr. ' || ed.firstname || ' ' || ed.lastname AS practicedoctorname
    FROM practicedoctors pd
    JOIN externaldoctors ed ON pd.externaldoctorid = ed.id
    JOIN doctorstudies ds ON ds.practicedoctorid = pd.id
    JOIN studies s ON ds.studyid = s.id
    WHERE ds.isactive = true 
      AND s.internalname = 'TAPP';
END;
$$;