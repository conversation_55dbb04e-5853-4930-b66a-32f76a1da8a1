-- Migration: SP_GetPatientPreviousAppointments stored procedure to PostgreSQL
-- Purpose: Get previous appointment history for a patient
-- Migrated from: SQL Server stored procedure [dbo].[SP_GetPatientPreviousAppointments]

CREATE OR REPLACE FUNCTION dbo.SP_GetPatientPreviousAppointments(
    p_patient_id INTEGER,
    p_practice_id INTEGER,
    p_limit INTEGER DEFAULT 10
) 
RETURNS TABLE (
    AppointmentId INTEGER,
    PatientId INTEGER,
    PracticeId INTEGER,
    OfficeId INTEGER,
    AppointmentTime TIMESTAMP WITH TIME ZONE,
    Duration INTEGER,
    AppointmentStatus INTEGER,
    AppointmentTypeId INTEGER,
    AppointmentType VARCHAR(100),
    PracticeDoctorId INTEGER,
    DoctorName VARCHAR(200),
    Notes TEXT,
    Reason VARCHAR(500),
    CreatedDate TIMESTAMP WITH TIME ZONE,
    ModifiedDate TIMESTAMP WITH TIME ZONE,
    IsVirtualVisit BOOLEAN,
    RoomNumber VARCHAR(50)
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        a.id::INTEGER as AppointmentId,
        a.patientrecordid::INTEGER as PatientId,
        pdr.practiceid::INTEGER,
        a.officeid::INTEGER,
        a.appointmenttime as AppointmentTime,
        30::INTEGER as Duration, -- Default duration since column doesn't exist
        a.appointmentstatus::INTEGER as AppointmentStatus,
        a.appointmenttypeid::INTEGER,
        COALESCE(at.name, '')::VARCHAR(100) as AppointmentType,
        a.practicedoctorid::INTEGER,
        COALESCE(ed.firstname || ' ' || ed.lastname, '')::VARCHAR(200) as DoctorName,
        COALESCE(a.appointmentnotes, '')::TEXT as Notes,
        COALESCE(a.appointmentpurpose, '')::VARCHAR(500) as Reason,
        a.datecreated as CreatedDate,
        a.lastmodified as ModifiedDate,
        false as IsVirtualVisit, -- Default since column doesn't exist
        COALESCE(a.roomnumber, '')::VARCHAR(50) as RoomNumber
    FROM dbo.appointments a
    LEFT JOIN dbo.appointmenttypes at ON a.appointmenttypeid = at.id
    LEFT JOIN dbo.practicedoctors pdr ON a.practicedoctorid = pdr.id
    LEFT JOIN dbo.externaldoctors ed ON pdr.externaldoctorid = ed.id
    WHERE a.patientrecordid = p_patient_id
        AND pdr.practiceid = p_practice_id
        AND a.appointmenttime < NOW() -- Only past appointments
        AND a.appointmentstatus IN (3, 4) -- Completed or No-Show statuses
    ORDER BY a.appointmenttime DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;