-- Migration: SP_GetPatientPreviousAppointments stored procedure to PostgreSQL
-- Purpose: Get previous appointment history for a patient
-- Migrated from: SQL Server stored procedure [dbo].[SP_GetPatientPreviousAppointments]

CREATE OR REPLACE FUNCTION dbo.SP_GetPatientPreviousAppointments(
    p_patient_id INTEGER,
    p_practice_id INTEGER,
    p_limit INTEGER DEFAULT 10
) 
RETURNS TABLE (
    AppointmentId INTEGER,
    PatientId INTEGER,
    PracticeId INTEGER,
    OfficeId INTEGER,
    AppointmentTime TIMESTAMP,
    Duration INTEGER,
    AppointmentStatus INTEGER,
    AppointmentTypeId INTEGER,
    AppointmentType VARCHAR(100),
    PracticeDoctorId INTEGER,
    DoctorName VARCHAR(200),
    Notes TEXT,
    Reason VARCHAR(500),
    CreatedDate TIMESTAMP,
    ModifiedDate TIMESTAMP,
    IsVirtualVisit BOOLEAN,
    RoomNumber VARCHAR(50)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.Id::INTEGER as AppointmentId,
        a.PatientRecordId::INTEGER as PatientId,
        a.PracticeId::INTEGER,
        a.OfficeId::INTEGER,
        a.appointmentTime as AppointmentTime,
        COALESCE(a.duration, 30)::INTEGER as Duration,
        a.appointmentStatus::INTEGER as AppointmentStatus,
        a.AppointmentTypeId::INTEGER,
        COALESCE(at.appointmentType, '') as AppointmentType,
        a.PracticeDoctorId::INTEGER,
        COALESCE(pdu.firstName || ' ' || pdu.lastName, '') as DoctorName,
        COALESCE(a.notes, '') as Notes,
        COALESCE(a.reason, '') as Reason,
        a.createdDate as CreatedDate,
        a.modifiedDate as ModifiedDate,
        COALESCE(a.isVirtualVisit, false) as IsVirtualVisit,
        COALESCE(r.roomNumber, '') as RoomNumber
    FROM Appointments a
    LEFT JOIN AppointmentTypes at ON a.AppointmentTypeId = at.Id
    LEFT JOIN PracticeDoctors pdr ON a.PracticeDoctorId = pdr.Id
    LEFT JOIN PracticeDoctorUsers pdu ON pdr.PracticeDoctorUserId = pdu.Id
    LEFT JOIN Rooms r ON a.roomId = r.Id
    WHERE a.PatientRecordId = p_patient_id
        AND a.PracticeId = p_practice_id
        AND a.appointmentTime < NOW() -- Only past appointments
        AND a.appointmentStatus IN (3, 4) -- Completed or No-Show statuses
    ORDER BY a.appointmentTime DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;