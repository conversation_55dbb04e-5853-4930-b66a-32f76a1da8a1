-- Migration: GetKiosk<PERSON>heckins stored procedure to PostgreSQL
-- Purpose: Get kiosk check-ins with patient and appointment information
-- Migrated from: SQL Server stored procedure [dbo].[GetKiosk<PERSON><PERSON>ckins]

CREATE OR REPLACE FUNCTION dbo.GetKioskCheckins(
    p_practice_id INTEGER,
    p_office_id INTEGER DEFAULT 0,
    p_app_date TIMESTAMP DEFAULT NULL
) 
RETURNS TABLE (
    CheckinId INTEGER,
    PracticeId INTEGER,
    AppointmentId INTEGER,
    AppointmentTime TIMESTAMP,
    CheckinTime TIMESTAMP,
    Heathcard VARCHAR(100),
    KioskMessageCode INTEGER,
    InternalMessage VARCHAR(1000),
    ExternalMessage VARCHAR(1000),
    OfficeId INTEGER,
    OfficeName VARCHAR(200),
    PatientId INTEGER,
    DemographicId INTEGER,
    FirstName VARCHAR(100),
    LastName VARCHAR(100),
    MiddleName VARCHAR(100)
) AS $$
DECLARE
    v_app_date TIMESTAMP := COALESCE(p_app_date, NOW());
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        ck.Id::INTEGER as CheckinId,
        p_practice_id as PracticeId,
        apps.Id::INTEGER as AppointmentId,
        apps.appointmentTime as AppointmentTime,
        ck.DateCheckedIn as CheckinTime,
        COALESCE(ck.HealthCard, '') as Heathcard,
        km.Code::INTEGER as KioskMessageCode,
        COALESCE(km.InternalMessage, '') as InternalMessage,
        COALESCE(km.ExternalMessage, '') as ExternalMessage,
        o.Id::INTEGER as OfficeId,
        COALESCE(o.name, '') as OfficeName,
        COALESCE(demo.PatientRecordId, 0)::INTEGER as PatientId,
        COALESCE(demo.DemographicId, 0)::INTEGER as DemographicId,
        COALESCE(demo.firstName, '') as FirstName,
        COALESCE(demo.lastName, '') as LastName,
        COALESCE(demo.middleName, '') as MiddleName
    FROM KioskCheckins ck
    INNER JOIN KioskIpAddresses kip ON ck.IpAddress = kip.IpAddress
    INNER JOIN KioskMessages km ON ck.KioskMessageId = km.Id
    INNER JOIN Offices o ON kip.OfficeId = o.Id
    LEFT JOIN Appointments apps ON ck.AppointmentId = apps.Id
    LEFT JOIN (
        -- Get latest demographic record for each health card
        SELECT DISTINCT ON (hc.number) 
            d.PatientRecordId, 
            d.Id as DemographicId, 
            d.firstName, 
            d.lastName, 
            d.middleName, 
            hc.number
        FROM Demographics d
        INNER JOIN DemographicsHealthCards hc ON d.Id = hc.DemographicId
        INNER JOIN PatientRecords pr ON d.PatientRecordId = pr.Id
        WHERE pr.PracticeId = p_practice_id
        ORDER BY hc.number, hc.Id DESC
    ) demo ON ck.HealthCard = demo.number
    WHERE DATE(ck.DateCheckedIn) = DATE(v_app_date)
        AND ck.IpAddress IS NOT NULL
        AND ck.HealthCard IS NOT NULL
        AND o.Id = CASE WHEN p_office_id > 0 THEN p_office_id ELSE o.Id END
        AND o.PracticeId = p_practice_id
    ORDER BY ck.DateCheckedIn DESC;
END;
$$ LANGUAGE plpgsql;