-- PostgreSQL function migration of GetReportClinicDailyRegister stored procedure
-- Original: GetReportClinicDailyRegister(@PracticeId, @OfficeIds, @AssignedToUserIds, @StartDate, @EndDate)
-- Purpose: Generates daily clinic register report with appointments and demographics

CREATE OR REPLACE FUNCTION dbo.GetReportClinicDailyRegister(
    p_practice_id INTEGER,
    p_office_ids INTEGER[] DEFAULT ARRAY[]::INTEGER[],
    p_assigned_to_user_ids INTEGER[] DEFAULT ARRAY[]::INTEGER[],
    p_start_date DATE,
    p_end_date DATE
)
RETURNS TABLE(
    appointmentid integer,
    officeid integer,
    officename varchar(100),
    patientrecordid integer,
    patientname varchar(500),
    patientphonenumbers varchar(300),
    patientemail varchar(100),
    referraldoctorid integer,
    referraldoctorname varchar(500),
    service varchar(100),
    modality varchar(100),
    appointmenttime timestamp
) AS $$
DECLARE
    v_start_datetime TIMESTAMP;
    v_end_datetime TIMESTAMP;
BEGIN
    -- Set date boundaries
    v_start_datetime := p_start_date::timestamp + TIME '00:00:00';
    v_end_datetime := p_end_date::timestamp + TIME '23:59:59';
    
    RETURN QUERY
    WITH appointments_filtered AS (
        SELECT 
            ap.id AS appointmentid,
            ap.officeid,
            ap.patientrecordid,
            ap.referraldoctorid,
            t.id AS testid,
            t.testshortname,
            ap.appointmenttime AT TIME ZONE 'UTC' AS appointmenttime
        FROM dbo.appointments ap
        JOIN dbo.appointmenttests apt ON apt.appointmentid = ap.id
        JOIN dbo.office o ON o.id = ap.officeid
        JOIN dbo.tests t ON t.id = apt.testid
        WHERE o.practiceid = p_practice_id
          AND ap.appointmenttime BETWEEN v_start_datetime AND v_end_datetime
          AND ap.appointmentstatus <> 7
          AND ap.isactive = true
          AND apt.isactive = true
          -- Office filter
          AND (array_length(p_office_ids, 1) IS NULL OR ap.officeid = ANY(p_office_ids))
          -- Resource assignment filter
          AND CASE 
              WHEN array_length(p_assigned_to_user_ids, 1) IS NOT NULL THEN
                  EXISTS (
                      SELECT 1 
                      FROM dbo.appointmenttestresources res
                      JOIN dbo.aspnetusers usr ON res.assignedtouserid = usr.userid
                      WHERE res.appointmenttestid = apt.id
                        AND res.isactive = true
                        AND res.assignedtouserid = ANY(p_assigned_to_user_ids)
                  )
              ELSE
                  EXISTS (
                      SELECT 1 
                      FROM dbo.appointmenttestresources res
                      JOIN dbo.aspnetusers usr ON res.assignedtouserid = usr.userid
                      WHERE res.appointmenttestid = apt.id
                        AND res.isactive = true
                  )
          END
    )
    SELECT 
        a.appointmentid,
        a.officeid,
        o.name as officename,
        a.patientrecordid,
        -- Patient name
        COALESCE(d.lastname || ', ', '') || COALESCE(d.firstname, '') as patientname,
        -- Patient phone numbers (concatenated)
        (
            SELECT string_agg(
                ph.phonenumber || ' ' || 
                CASE ph.typeofphonenumber
                    WHEN 0 THEN 'H'
                    WHEN 1 THEN 'C' 
                    WHEN 2 THEN 'W'
                    ELSE ''
                END,
                ', '
                ORDER BY ph.demographicid, ph.typeofphonenumber, ph.isactive DESC, ph.id DESC
            )
            FROM dbo.demographicsphonenumbers ph
            WHERE ph.phonenumber IS NOT NULL 
              AND TRIM(ph.phonenumber) <> ''
              AND ph.isremoved = false
              AND ph.demographicid = d.id
        ) as patientphonenumbers,
        -- Patient email
        d.email as patientemail,
        a.referraldoctorid,
        -- Referral doctor name
        COALESCE(ed.lastname || ', ', '') || COALESCE(ed.firstname, '') as referraldoctorname,
        -- Service
        a.testshortname as service,
        -- Modality
        (
            SELECT m.modalityname
            FROM dbo.testmodalities tm
            JOIN dbo.modalities m ON tm.modalityid = m.id
            WHERE tm.testid = a.testid 
              AND tm.isactive = true
            LIMIT 1
        ) as modality,
        a.appointmenttime
    FROM appointments_filtered a
    JOIN dbo.demographics d ON a.patientrecordid = d.patientrecordid
    JOIN dbo.office o ON o.id = a.officeid
    JOIN dbo.externaldoctors ed ON ed.id = a.referraldoctorid
    ORDER BY a.officeid ASC, a.appointmenttime ASC, a.appointmentid ASC;
END;
$$ LANGUAGE plpgsql;