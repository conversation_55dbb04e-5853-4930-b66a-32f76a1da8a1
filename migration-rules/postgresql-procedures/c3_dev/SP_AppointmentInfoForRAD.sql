-- PostgreSQL function for SP_AppointmentInfoForRAD
-- Migrated from SQL Server stored procedure
-- RAD appointment information retrieval

CREATE OR REPLACE FUNCTION dbo.SP_AppointmentInfoForRAD(
    p_practice_id INTEGER,
    p_accession_number VARCHAR(1000)
)
RETURNS TABLE(
    appointmentid integer,
    testid integer,
    accessionnumber varchar(1000),
    officeid integer,
    officename text,
    officeurl text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        A.Id AS AppointmentId,
        T.TestId,
        T.AccessionNumber,
        A.OfficeId,
        O.name AS OfficeName,
        OURL.url AS OfficeURL 
    FROM dbo.Appointments A
    JOIN dbo.AppointmentTests T ON A.Id = T.AppointmentId
    JOIN dbo.Office O ON A.OfficeId = O.Id
    JOIN (
        SELECT OU.officeId, OU.url 
        FROM dbo.OfficeUrls OU 
        JOIN dbo.OfficeUrlTypes UT ON OU.urlTypeId = UT.Id
        WHERE LOWER(UT.urlType) = 'image'
    ) AS OURL ON O.Id = OURL.officeId
    WHERE O.PracticeId = p_practice_id 
        AND T.AccessionNumber = p_accession_number
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;