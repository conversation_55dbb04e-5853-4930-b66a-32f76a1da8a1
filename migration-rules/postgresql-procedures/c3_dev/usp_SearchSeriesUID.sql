-- PostgreSQL function for usp_SearchSeriesUID
-- Migrated from SQL Server stored procedure
-- Searches for series by SeriesUI<PERSON> using hash-based lookup

CREATE OR REPLACE FUNCTION dbo.usp_SearchSeriesUID(
    p_series_uid VARCHAR(250)
)
RETURNS TABLE(
    id INTEGER,
    seriesuid VARCHAR(250),
    studyid INTEGER,
    seriesnum INTEGER,
    createddatetime TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id,
        s.SeriesUID as seriesuid,
        s.StudyId as studyid,
        s.SeriesNum as seriesnum,
        s.CreatedDateTime as createddatetime
    FROM dbo.RAD_Series s
    WHERE s.SeriesUIDHash = MD5(p_series_uid)::UUID::TEXT
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;