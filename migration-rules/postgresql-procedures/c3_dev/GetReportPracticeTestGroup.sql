-- GetReportPracticeTestGroup procedure for PostgreSQL
-- Migrated from SQL Server stored procedure
-- Gets report practice test group information with report seal

CREATE OR REPLACE FUNCTION dbo.GetReportPracticeTestGroup(
    p_appointment_test_id integer
)
RETURNS TABLE (
    appointmenttestid integer,
    reportseal bytea
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        apt.id AS appointmenttestid,
        rs.image AS reportseal
    FROM appointmenttests apt
    JOIN appointments a ON a.id = apt.appointmentid
    JOIN office o ON o.id = a.officeid
    JOIN testgroups tg ON tg.testid = apt.testid
    JOIN practicetestgroups ptg ON ptg.groupid = tg.groupid AND ptg.practiceid = o.practiceid
    JOIN practicetestgroupreportseal rs ON rs.id = ptg.reportsealid
    WHERE apt.id = p_appointment_test_id
    LIMIT 1;
END;
$$;