-- PostgreSQL function for PrepareMWL
-- Migrated from SQL Server stored procedure
-- Prepares MWL URLs for appointments

CREATE OR REPLACE FUNCTION dbo.PrepareMWL(
    p_practice_id INTEGER,
    p_appointment_id INTEGER
)
RETURNS TABLE(
    appointmentid INTEGER,
    mwlsentflag BOOLEAN,
    mwlurl VARCHAR(1000),
    newurl TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.Id as appointmentid,
        a.<PERSON> as mwlsentflag,
        a.MWLUrl as mwlurl,
        CONCAT(ourl.url, '?aid=', a.Id, '&office=', a.OfficeId, '&pid=', a.PatientRecordId, 
               '&lname=', demo.lastName, '&fname=', demo.firstName, 
               '&bd=', TO_CHAR(demo.dateOfBirth, 'YYYYMMDD'), 
               '&sex=', demo.gender, '&dn=', ed.lastName, ',', ed.firstName) as newurl
    FROM dbo.Appointments a
    JOIN dbo.PatientRecords pr ON a.PatientRecordId = pr.Id
    JOIN dbo.Demographics demo ON pr.Id = demo.PatientRecordId
    JOIN dbo.PracticeDoctors pd ON a.PracticeDoctorId = pd.Id
    JOIN dbo.ExternalDoctors ed ON pd.ExternalDoctorId = ed.Id
    JOIN dbo.OfficeUrls ourl ON a.OfficeId = ourl.officeId
    JOIN dbo.OfficeUrlTypes ty ON ourl.urlTypeId = ty.Id
    WHERE pr.PracticeId = p_practice_id 
        AND a.Id = p_appointment_id 
        AND ty.urlType = 'MWL' 
        AND demo.dateOfBirth IS NOT NULL
    ORDER BY a.appointmentTime DESC;
END;
$$ LANGUAGE plpgsql;