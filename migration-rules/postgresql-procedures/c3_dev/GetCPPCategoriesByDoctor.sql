-- GetCPPCategoriesByDoctor PostgreSQL Function
-- Retrieves CPP categories by doctor with custom settings and skip status

CREATE OR REPLACE FUNCTION GetCPPCategoriesByDoctor(
    p_externalDoctorId INTEGER,
    p_patientId INTEGER DEFAULT 0
)
RETURNS TABLE(
    Id INTEGER,
    Text VARCHAR(255),
    "Group" INTEGER,
    "Order" INTEGER,
    Visible BOOLEAN,
    CustomText VARCHAR(255),
    Skipped BOOLEAN,
    PatientId INTEGER,
    ExternalDoctorId INTEGER
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        g.Id,
        g.<PERSON>,
        g."Group",
        COALESCE(s."Order", 1000000) AS "Order",
        COALESCE(s.Visible, FALSE) AS Visible,
        s.Text AS CustomText,
        COALESCE(sk.Skip, TRUE)::BOOLEAN AS Skipped, -- TRUE means we don't want to see it in the letter
        COALESCE(s.PatientRecordId, 0) AS PatientId,
        p_externalDoctorId AS ExternalDoctorId
    FROM VP_CPP_Category g
    LEFT JOIN VP_CPP_Setting s ON g.Id = s.VP_CPP_Category_Id AND s.DoctorID = p_externalDoctorId
    LEFT JOIN VP_CPP_Skipped sk ON g.Id = sk.CPP_Category_ID AND sk.UserID = p_externalDoctorId		
    ORDER BY "Order" ASC;
END;
$$;