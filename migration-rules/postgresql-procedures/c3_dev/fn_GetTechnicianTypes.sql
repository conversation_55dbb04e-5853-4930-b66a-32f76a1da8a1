-- PostgreSQL function migration of fn_GetTechnicianTypes
-- Original: fn_GetTechnicianTypes() RETURNS TABLE
-- Purpose: Returns lookup table of technician types with IDs and names
-- Modified to return TABLE for repository pattern compatibility

CREATE OR REPLACE FUNCTION dbo.fn_GetTechnicianTypes()
RETURNS TABLE(
    id INTEGER,
    typename VA<PERSON>HAR(50)
) AS $$
BEGIN
    -- Return hardcoded lookup values for technician types
    RETURN QUERY VALUES
        (4, 'CardiologyTech'::VARCHAR(50)),
        (6, 'VascularTech'::VARCHAR(50)),
        (7, 'SETech'::VARCHAR(50)),
        (8, 'GXTTech'::VARCHAR(50)),
        (9, 'EchoTech'::VARCHAR(50)),
        (10, 'HolterTech'::VARCHAR(50)),
        (11, 'ECGTech'::VARCHAR(50)),
        (15, 'NuclearTech'::VARCHAR(50)),
        (16, 'USTech'::VARCHAR(50)),
        (17, 'XRayTech'::VARCHAR(50)),
        (18, 'PulmonaryTech'::VARCHA<PERSON>(50)),
        (19, 'Nurse'::VARCHAR(50));
END;
$$ LANGUAGE plpgsql;