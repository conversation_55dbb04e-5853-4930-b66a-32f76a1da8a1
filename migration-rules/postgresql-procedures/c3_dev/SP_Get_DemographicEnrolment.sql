-- SP_Get_DemographicEnrolment procedure for PostgreSQL
-- Migrated from SQL Server stored procedure
-- VALIDATED: 2025-12-09 - Function working correctly with proper data types

CREATE OR REPLACE FUNCTION dbo.sp_get_demographicenrolment(
    p_patient_id integer
)
RETURNS TABLE (
    "Id" bigint,
    "enrolled" boolean,
    "enrollmentStatus" integer,
    "enrollmentStatusSpecified" boolean,
    "enrollmentDate" timestamp with time zone,
    "enrollmentDateSpecified" boolean,
    "enrollmentTerminationDate" timestamp with time zone,
    "enrollmentTerminationDateSpecified" boolean,
    "terminationReason" integer,
    "terminationReasonSpecified" boolean,
    "DemographicsMRPId" integer
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id as "Id",
        d.enrolled,
        d.enrollmentstatus as "enrollmentStatus",
        d.enrollmentstatusspecified as "enrollmentStatusSpecified",
        d.enrollmentdate as "enrollmentDate",
        d.enrollmentdatespecified as "enrollmentDateSpecified",
        d.enrollmentterminationdate as "enrollmentTerminationDate",
        d.enrollmentterminationdatespecified as "enrollmentTerminationDateSpecified",
        d.terminationreason as "terminationReason",
        d.terminationreasonspecified as "terminationReasonSpecified",
        d.demographicsmrpid as "DemographicsMRPId"
    FROM dbo.demographicsenrollments d 
    WHERE d.demographicsmrpid IN (
        SELECT dmrp.id 
        FROM dbo.demographicsmainresponsiblephysicians dmrp
        WHERE dmrp.demographicid IN (
            SELECT demo.id 
            FROM dbo.demographics demo
            WHERE demo.patientrecordid = p_patient_id
        )
    );
END;
$$;