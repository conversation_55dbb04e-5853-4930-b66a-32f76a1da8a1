-- PostgreSQL function migration of fn_SuperTrimRight
-- Original: fn_SuperTrimRight(@str VARCHAR(MAX)) RETURNS VARCHAR(MAX)
-- Purpose: Advanced trimming function that removes tab, line feed, carriage return, and spaces from right side
-- Modified to return TABLE for repository pattern compatibility

CREATE OR REPLACE FUNCTION dbo.fn_SuperTrimRight(
    p_str TEXT
)
RETURNS TABLE(
    trimmed_str TEXT
) AS $$
DECLARE
    v_result TEXT;
BEGIN
    -- PostgreSQL equivalent of advanced right trimming
    -- Remove tab (ASCII 9), line feed (ASCII 10), carriage return (ASCII 13), and space (ASCII 32)
    v_result := RTRIM(p_str, E'\t\n\r ');
    
    -- Return as table
    RETURN QUERY SELECT v_result;
END;
$$ LANGUAGE plpgsql;