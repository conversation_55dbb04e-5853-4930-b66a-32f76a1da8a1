CREATE OR REPLACE FUNCTION dbo.HCV_Get_Practice_Doctor_Credential(
    p_practice_id INTEGER,
    p_practice_doctor_id INTEGER DEFAULT 0
)
RETURNS TABLE(
    practicedoctorid integer,
    externaldoctorid integer,
    billingnumber text,
    username text,
    mc_edt_pwd text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pd.Id::integer as practicedoctorid,
        pd.ExternalDoctorId::integer as externaldoctorid,
        ed.OHIPPhysicianId::text as billingnumber,
        pd.mc_un::text as username,
        pd.mc_pwd::text as mc_edt_pwd
    FROM dbo.PracticeDoctors pd
    JOIN dbo.ExternalDoctors ed ON pd.ExternalDoctorId = ed.Id
    WHERE pd.PracticeId = p_practice_id 
        AND pd.IsActive = true
        AND (CASE WHEN pd.canUseCredentialForHCV IS NOT NULL AND pd.canUseCredentialForHCV = true THEN true ELSE true END) = true
        AND pd.mc_un IS NOT NULL 
        AND pd.mc_pwd IS NOT NULL
        AND pd.mc_un <> '' 
        AND pd.mc_pwd <> ''
        AND ed.OHIPPhysicianId IS NOT NULL
        AND (CASE WHEN p_practice_doctor_id > 0 AND pd.Id = p_practice_doctor_id THEN true ELSE true END) = true
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;