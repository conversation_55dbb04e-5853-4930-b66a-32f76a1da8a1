-- Migration: GetPatientPreviousTests stored procedure to PostgreSQL
-- Purpose: Get previous tests for the same test type for a patient (before a given appointment)
-- Migrated from: SQL Server stored procedure [dbo].[GetPatientPreviousTests]

CREATE OR REPLACE FUNCTION dbo.GetPatientPreviousTests(
    p_appointment_test_id INTEGER
) 
RETURNS TABLE (
    PatientId INTEGER,
    PracticeId INTEGER,
    OfficeId INTEGER,
    AppointmentTestId INTEGER,
    AppointmentId INTEGER,
    AppointmentTypeId INTEGER,
    AppointmentStatus INTEGER,
    AppointmentTime TIMESTAMP,
    PracticeDoctorId INTEGER,
    TestId INTEGER,
    TestName VARCHAR(200),
    TestStatusId INTEGER,
    TestStatusColor VARCHAR(50),
    TestDate TIMESTAMP,
    PracticeDoctor VARCHAR(200),
    ExternalDoctorId INTEGER,
    BillStatusId INTEGER,
    AppointmentTestLogId INTEGER
) AS $$
DECLARE
    v_practice_id INTEGER := 0;
    v_patient_id INTEGER := 0;
    v_appointment_time TIMESTAMP;
    v_test_id INTEGER := 0;
    -- Excluded appointment statuses for previous tests
    v_excluded_statuses INTEGER[] := ARRAY[0, 1, 2, 3, 6, 7, 16]; -- cancellationlist, waitlist, Booked, NotArrived, Missed, Cancelled, triage
BEGIN
    -- Get context from the appointment test
    IF p_appointment_test_id > 0 THEN
        SELECT 
            COALESCE(o.PracticeId, 0),
            COALESCE(app.PatientRecordId, 0),
            COALESCE(appTest.TestId, 0),
            app.appointmentTime
        INTO v_practice_id, v_patient_id, v_test_id, v_appointment_time
        FROM AppointmentTests appTest
        INNER JOIN Appointments app ON appTest.AppointmentId = app.Id	
        INNER JOIN Offices o ON app.OfficeId = o.Id
        WHERE appTest.Id = p_appointment_test_id
        LIMIT 1;
    END IF;

    RETURN QUERY
    SELECT 
        apt.PatientRecordId::INTEGER as PatientId,
        o.PracticeId::INTEGER,
        apt.OfficeId::INTEGER,
        at.Id::INTEGER as AppointmentTestId,
        apt.Id::INTEGER as AppointmentId,
        apt.AppointmentTypeId::INTEGER,
        apt.appointmentStatus::INTEGER as AppointmentStatus,
        apt.appointmentTime,
        apt.PracticeDoctorId::INTEGER,
        at.TestId::INTEGER,
        COALESCE(t.testShortName, '') as TestName,
        COALESCE(at.testStatusId, 0)::INTEGER as TestStatusId,
        COALESCE(ts.color, '') as TestStatusColor,
        COALESCE(at.testDate, apt.appointmentTime) as TestDate,
        COALESCE(pdu.firstName || ' ' || pdu.lastName, '') as PracticeDoctor,
        COALESCE(at.ExternalDoctorId, 0)::INTEGER,
        COALESCE(ab.billStatusId, 0)::INTEGER as BillStatusId,
        COALESCE(atl.Id, 0)::INTEGER as AppointmentTestLogId
    FROM Appointments apt
    INNER JOIN AppointmentTests at ON apt.Id = at.AppointmentId
    INNER JOIN Tests t ON at.TestId = t.Id
    INNER JOIN Offices o ON apt.OfficeId = o.Id
    LEFT JOIN TestStatuses ts ON at.testStatusId = ts.Id
    LEFT JOIN PracticeDoctors pd ON apt.PracticeDoctorId = pd.Id
    LEFT JOIN PracticeDoctorUsers pdu ON pd.PracticeDoctorUserId = pdu.Id
    LEFT JOIN AppointmentBills ab ON apt.Id = ab.AppointmentId
    LEFT JOIN AppointmentTestSaveLogs atl ON at.Id = atl.appointmentTestId
    WHERE apt.PatientRecordId = v_patient_id
        AND o.PracticeId = v_practice_id
        AND apt.appointmentTime < v_appointment_time
        AND apt.appointmentStatus <> ALL(v_excluded_statuses)
        AND at.TestId = v_test_id
        AND COALESCE(at.IsActive, false) = true
    ORDER BY apt.appointmentTime DESC;
END;
$$ LANGUAGE plpgsql;