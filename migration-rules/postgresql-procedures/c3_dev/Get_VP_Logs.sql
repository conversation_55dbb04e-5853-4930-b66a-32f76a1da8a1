-- PostgreSQL function for Get_VP_Logs
-- Migrated from SQL Server stored procedure
-- Returns VP appointment test logs for a specific appointment and patient

CREATE OR REPLACE FUNCTION dbo.Get_VP_Logs(
    p_appointment_id INTEGER,
    p_patient_id INTEGER
)
RETURNS TABLE(
    Id INTEGER,
    Date TIMESTAMP,
    Status INTEGER,
    AppointmentId INTEGER,
    PatientRecordId INTEGER,
    IP VARCHAR(100),
    UserId INTEGER,
    UserName VARCHAR(256),
    FirstName VARCHAR(100),
    LastName VARCHAR(100),
    PracticeDoctorId INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        x.Id,
        x.Date,
        x.Status,
        x.AppointmentId,
        x.PatientRecordId,
        x.IP,
        x.UserId,
        u.UserName,
        u.FirstName,
        u.LastName,
        COALESCE((
            SELECT d.Id 
            FROM PracticeDoctors d 
            WHERE d.ApplicationUserId = u.Id 
            LIMIT 1
        ), 0) AS PracticeDoctorId
    FROM 
        VP_AppointmentTestLog x  
        LEFT JOIN AspNetUsers u ON x.UserId = u.UserID
        JOIN Appointments a ON x.AppointmentId = a.Id
        JOIN PatientRecords p ON x.PatientRecordId = p.Id
    WHERE
        a.PatientRecordId = p.Id AND
        x.AppointmentId = p_appointment_id AND 
        x.PatientRecordId = p_patient_id;
END;
$$ LANGUAGE plpgsql;