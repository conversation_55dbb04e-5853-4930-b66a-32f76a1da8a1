-- GetReportDoctors procedure for PostgreSQL
-- Simplified migration from SQL Server's complex fn_GetReportDoctors function
-- NOTE: This is a simplified version that covers core functionality
-- The original SQL Server function has very complex logic for determining doctor hierarchies

CREATE OR REPLACE FUNCTION dbo.GetReportDoctors(
    p_appointment_test_id integer
)
RETURNS TABLE (
    externaldoctorid integer,
    practicedoctorid integer,
    doctorsignature bytea,
    email boolean,
    fax boolean,
    hrm boolean,
    mail boolean,
    firstname varchar(100),
    lastname varchar(100),
    emailaddress varchar(200),
    address varchar(500),
    city varchar(100),
    postalcode varchar(100),
    province varchar(100),
    faxnumber varchar(100),
    phonenumber varchar(100),
    phonenumberextension varchar(100),
    reportresourcetype integer,
    resourcetypedescription varchar(200),
    letterhead varchar(1000),
    degrees varchar(200),
    cpso varchar(200),
    ohipid varchar(200),
    appointmenttime timestamp,
    appointmentid integer,
    appointmenttestid integer,
    ismainreportdoctor boolean,
    ismainresponsibleDoctor boolean,
    ismainreferraldoctor boolean,
    issendtoself boolean,
    physiciantype varchar(10),
    hrmmnemonic varchar(30)
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_practice_id integer;
    v_office_id integer;
    v_appointment_id integer;
    v_patient_id integer;
    v_demographic_id integer;
    v_appointment_time timestamp;
    v_practice_doctor_id integer;
    v_referral_doctor_id integer;
    v_appointment_doctor_id integer;
    v_main_responsible_doctor_id integer;
    v_family_doctor_id integer;
BEGIN
    -- Get basic appointment test information
    SELECT 
        o.practiceid,
        o.id,
        apptest.appointmentid,
        app.patientrecordid,
        app.appointmenttime,
        app.practicedoctorid,
        COALESCE(app.referraldoctorid, 0)
    INTO 
        v_practice_id,
        v_office_id,
        v_appointment_id,
        v_patient_id,
        v_appointment_time,
        v_practice_doctor_id,
        v_referral_doctor_id
    FROM appointmenttests apptest
    JOIN appointments app ON apptest.appointmentid = app.id
    JOIN office o ON app.officeid = o.id
    WHERE apptest.id = p_appointment_test_id;

    IF NOT FOUND THEN
        RETURN;
    END IF;

    -- Get demographic ID
    SELECT id INTO v_demographic_id 
    FROM demographics 
    WHERE patientrecordid = v_patient_id 
    ORDER BY id 
    LIMIT 1;

    -- Get appointment doctor's external ID
    SELECT externaldoctorid INTO v_appointment_doctor_id
    FROM practicedoctors
    WHERE id = v_practice_doctor_id
    LIMIT 1;

    -- Get family doctor
    SELECT e.id INTO v_family_doctor_id
    FROM externaldoctors e
    JOIN demographicsfamilydoctors fd ON e.id = fd.externaldoctorid
    WHERE fd.demographicid = v_demographic_id
      AND fd.isremoved = false 
      AND fd.isactive = true
    ORDER BY fd.id DESC
    LIMIT 1;

    -- Get main responsible doctor
    SELECT e.id INTO v_main_responsible_doctor_id
    FROM externaldoctors e
    JOIN demographicsmainresponsiblephysicians md ON e.id = md.externaldoctorid
    WHERE md.demographicid = v_demographic_id
      AND md.isremoved = false 
      AND md.isactive = true
    ORDER BY md.id DESC
    LIMIT 1;

    -- Return main appointment doctor if exists
    IF v_appointment_doctor_id > 0 THEN
        RETURN QUERY
        SELECT 
            e.id,
            COALESCE(pd.id, 0),
            rs.image,
            e.email,
            e.fax,
            e.hrm,
            e.mail,
            e.firstname,
            e.lastname,
            e.emailaddress,
            COALESCE(e.address, ''),
            COALESCE(e.city, ''),
            COALESCE(e.postalcode, ''),
            COALESCE(e.province, ''),
            COALESCE(e.faxnumber, ''),
            COALESCE(e.phonenumber, ''),
            COALESCE(e.phonenumberextension, ''),
            1, -- Reporting doctor type
            'Reporting Doctor',
            COALESCE(e.letterhead, ''),
            COALESCE(e.degrees, ''),
            COALESCE(e.cpso, ''),
            COALESCE(e.ohipphysicianid, ''),
            v_appointment_time,
            v_appointment_id,
            p_appointment_test_id,
            true, -- IsMainReportDoctor
            false, -- IsMainResponsibleDoctor
            false, -- IsMainReferralDoctor
            COALESCE(pd.issendtoself, false),
            COALESCE(e.physiciantype, ''),
            COALESCE(e.hrmmnemonic, '')
        FROM externaldoctors e
        LEFT JOIN practicedoctors pd ON pd.externaldoctorid = e.id AND pd.practiceid = v_practice_id AND pd.isactive = true
        LEFT JOIN practicedoctorreportsignature rs ON rs.practicedoctorid = pd.id
        WHERE e.id = v_appointment_doctor_id;
    END IF;

    -- Return referral doctor if exists and different from appointment doctor
    IF v_referral_doctor_id > 0 AND v_referral_doctor_id != v_appointment_doctor_id THEN
        RETURN QUERY
        SELECT 
            e.id,
            0, -- No practice doctor ID for external referral
            NULL::bytea,
            e.email,
            e.fax,
            e.hrm,
            e.mail,
            e.firstname,
            e.lastname,
            e.emailaddress,
            COALESCE(e.address, ''),
            COALESCE(e.city, ''),
            COALESCE(e.postalcode, ''),
            COALESCE(e.province, ''),
            COALESCE(e.faxnumber, ''),
            COALESCE(e.phonenumber, ''),
            COALESCE(e.phonenumberextension, ''),
            2, -- Referral doctor type
            'Referral Doctor',
            COALESCE(e.letterhead, ''),
            COALESCE(e.degrees, ''),
            COALESCE(e.cpso, ''),
            COALESCE(e.ohipphysicianid, ''),
            v_appointment_time,
            v_appointment_id,
            p_appointment_test_id,
            false, -- IsMainReportDoctor
            false, -- IsMainResponsibleDoctor
            true, -- IsMainReferralDoctor
            false, -- IsSendToSelf
            COALESCE(e.physiciantype, ''),
            COALESCE(e.hrmmnemonic, '')
        FROM externaldoctors e
        WHERE e.id = v_referral_doctor_id;
    END IF;

    -- Return family doctor if exists and different from others
    IF v_family_doctor_id > 0 AND v_family_doctor_id NOT IN (v_appointment_doctor_id, v_referral_doctor_id) THEN
        RETURN QUERY
        SELECT 
            e.id,
            0, -- No practice doctor ID
            NULL::bytea,
            e.email,
            e.fax,
            e.hrm,
            e.mail,
            e.firstname,
            e.lastname,
            e.emailaddress,
            COALESCE(e.address, ''),
            COALESCE(e.city, ''),
            COALESCE(e.postalcode, ''),
            COALESCE(e.province, ''),
            COALESCE(e.faxnumber, ''),
            COALESCE(e.phonenumber, ''),
            COALESCE(e.phonenumberextension, ''),
            3, -- Family doctor type
            'Family Doctor',
            COALESCE(e.letterhead, ''),
            COALESCE(e.degrees, ''),
            COALESCE(e.cpso, ''),
            COALESCE(e.ohipphysicianid, ''),
            v_appointment_time,
            v_appointment_id,
            p_appointment_test_id,
            false, -- IsMainReportDoctor
            false, -- IsMainResponsibleDoctor
            false, -- IsMainReferralDoctor
            false, -- IsSendToSelf
            COALESCE(e.physiciantype, ''),
            COALESCE(e.hrmmnemonic, '')
        FROM externaldoctors e
        WHERE e.id = v_family_doctor_id;
    END IF;

    -- Return main responsible doctor if exists and different from others
    IF v_main_responsible_doctor_id > 0 AND v_main_responsible_doctor_id NOT IN (v_appointment_doctor_id, v_referral_doctor_id, v_family_doctor_id) THEN
        RETURN QUERY
        SELECT 
            e.id,
            0, -- No practice doctor ID
            NULL::bytea,
            e.email,
            e.fax,
            e.hrm,
            e.mail,
            e.firstname,
            e.lastname,
            e.emailaddress,
            COALESCE(e.address, ''),
            COALESCE(e.city, ''),
            COALESCE(e.postalcode, ''),
            COALESCE(e.province, ''),
            COALESCE(e.faxnumber, ''),
            COALESCE(e.phonenumber, ''),
            COALESCE(e.phonenumberextension, ''),
            5, -- Main responsible doctor type
            'Main Responsible Doctor',
            COALESCE(e.letterhead, ''),
            COALESCE(e.degrees, ''),
            COALESCE(e.cpso, ''),
            COALESCE(e.ohipphysicianid, ''),
            v_appointment_time,
            v_appointment_id,
            p_appointment_test_id,
            false, -- IsMainReportDoctor
            true, -- IsMainResponsibleDoctor
            false, -- IsMainReferralDoctor
            false, -- IsSendToSelf
            COALESCE(e.physiciantype, ''),
            COALESCE(e.hrmmnemonic, '')
        FROM externaldoctors e
        WHERE e.id = v_main_responsible_doctor_id;
    END IF;

    -- Return CC doctors (associated doctors marked for CC)
    RETURN QUERY
    SELECT 
        e.id,
        0, -- No practice doctor ID
        NULL::bytea,
        e.email,
        e.fax,
        e.hrm,
        e.mail,
        e.firstname,
        e.lastname,
        e.emailaddress,
        COALESCE(e.address, ''),
        COALESCE(e.city, ''),
        COALESCE(e.postalcode, ''),
        COALESCE(e.province, ''),
        COALESCE(e.faxnumber, ''),
        COALESCE(e.phonenumber, ''),
        COALESCE(e.phonenumberextension, ''),
        4, -- CC doctor type
        'CC Doctor',
        COALESCE(e.letterhead, ''),
        COALESCE(e.degrees, ''),
        COALESCE(e.cpso, ''),
        COALESCE(e.ohipphysicianid, ''),
        v_appointment_time,
        v_appointment_id,
        p_appointment_test_id,
        false, -- IsMainReportDoctor
        false, -- IsMainResponsibleDoctor
        false, -- IsMainReferralDoctor
        false, -- IsSendToSelf
        COALESCE(e.physiciantype, ''),
        COALESCE(e.hrmmnemonic, '')
    FROM externaldoctors e
    JOIN demographicsassociateddoctors a ON e.id = a.externaldoctorid
    WHERE a.demographicid = v_demographic_id
      AND a.isactive = true 
      AND a.isremoved = false 
      AND a.iscc = true
      AND e.id NOT IN (
          COALESCE(v_appointment_doctor_id, 0),
          COALESCE(v_referral_doctor_id, 0),
          COALESCE(v_family_doctor_id, 0),
          COALESCE(v_main_responsible_doctor_id, 0)
      );

END;
$$;