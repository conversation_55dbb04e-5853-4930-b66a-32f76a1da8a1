-- Migration: SP_GetPatientDemographicInfo stored procedure to PostgreSQL
-- Purpose: Get comprehensive patient demographic information including health cards, addresses, and phone numbers
-- Migrated from: SQL Server stored procedure [dbo].[SP_GetPatientDemographicInfo]

CREATE OR REPLACE FUNCTION dbo.SP_GetPatientDemographicInfo(
    p_practice_id INTEGER,
    p_patient_id INTEGER
) 
RETURNS TABLE (
    PatientRecordId INTEGER,
    DemographicId BIGINT,
    PracticeId INTEGER,
    OHIP TEXT,
    OHIPVersionCode TEXT,
    OHIPExpiry TIMESTAMP,
    namePrefix INTEGER,
    aliasLastName TEXT,
    aliasFirstName TEXT,
    aliasMiddleName TEXT,
    use<PERSON><PERSON><PERSON> BOOLEAN,
    preferredName TEXT,
    FirstName TEXT,
    MiddleName TEXT,
    LastName TEXT,
    dateOfBirth DATE,
    AgeAccurate TEXT,
    gender INTEGER,
    active INTEGER,
    defaultPaymentMethod INTEGER,
    PatientPhoneNumbers JSONB,
    AddressLine1 TEXT,
    AddressLine2 TEXT,
    City TEXT,
    PostalCode TEXT,
    Province TEXT,
    Country TEXT
) AS $$
DECLARE
    v_current_date TIMESTAMP := NOW();
    v_ohip_id TEXT;
    v_ohip_version_code TEXT;
    v_ohip_expire_date TIMESTAMP;
    v_province_ohip TEXT;
    v_address_line1 TEXT;
    v_address_line2 TEXT;
    v_city TEXT;
    v_postal_code TEXT;
    v_province TEXT;
    v_country TEXT;
BEGIN
    -- Get health card information (latest entry)
    SELECT 
        COALESCE(hc.number, '') as ohip_id,
        hc.expirydate as expire_date,
        CASE 
            WHEN hc.provinceCode = 0 THEN 'AB'
            WHEN hc.provinceCode = 9 THEN 'PE'
            WHEN hc.provinceCode = 4 THEN 'NF'
            WHEN hc.provinceCode = 1 THEN 'BC'
            WHEN hc.provinceCode = 8 THEN 'ON'
            WHEN hc.provinceCode = 2 THEN 'MB'
            WHEN hc.provinceCode = 5 THEN 'NS'
            ELSE 'ON'
        END as province_code,
        COALESCE(hc.version, '') as version_code
    INTO v_ohip_id, v_ohip_expire_date, v_province_ohip, v_ohip_version_code
    FROM dbo.demographicshealthcards hc 
    INNER JOIN dbo.demographics d ON hc.DemographicId = d.id
    WHERE d.patientrecordid = p_patient_id 
    ORDER BY hc.id DESC
    LIMIT 1;

    -- Get address information (latest entry)
    SELECT 
        da.addressLine1,
        da.addressLine2,
        da.city,
        da.postalCode,
        da.province,
        da.country
    INTO v_address_line1, v_address_line2, v_city, v_postal_code, v_province, v_country
    FROM dbo.demographicsaddresses da 
    INNER JOIN dbo.demographics d ON da.DemographicId = d.id
    WHERE d.patientrecordid = p_patient_id
    ORDER BY da.id DESC
    LIMIT 1;

    RETURN QUERY
    SELECT
        d.PatientRecordId::INTEGER,
        d.Id::BIGINT as DemographicId,
        pr.PracticeId::INTEGER,
        COALESCE(v_ohip_id, '') as OHIP,
        COALESCE(v_ohip_version_code, '') as OHIPVersionCode,
        v_ohip_expire_date as OHIPExpiry,
        d.namePrefix::INTEGER,
        d.aliasLastName,
        d.aliasFirstName,
        d.aliasMiddleName,
        COALESCE(d.useAliases, false) as useAliases,
        d.preferredName,
        COALESCE(d.aliasFirstName, d.firstName) as FirstName,
        COALESCE(d.aliasMiddleName, d.middleName) as MiddleName,
        COALESCE(d.aliasLastName, d.lastName) as LastName,
        d.dateOfBirth,
        (EXTRACT(YEAR FROM AGE(v_current_date, d.dateOfBirth))::TEXT || ' years') as AgeAccurate,
        d.gender::INTEGER,
        COALESCE(d.active, 1) as active,
        d.defaultPaymentMethod::INTEGER,
        -- Build phone numbers JSON
        (SELECT COALESCE(jsonb_agg(
            jsonb_build_object(
                'Id', ph.Id,
                'phoneNumber', ph.phoneNumber,
                'extention', ph.extention,
                'TypeOfPhoneNumber', CASE ph.typeOfPhoneNumber
                    WHEN 0 THEN 'H'
                    WHEN 1 THEN 'C'
                    WHEN 2 THEN 'W'
                    ELSE ''
                END,
                'IsActive', ph.IsActive
            )
            ORDER BY ph.DemographicId, ph.typeOfPhoneNumber, ph.IsActive DESC, ph.Id DESC
        ), '[]'::jsonb)
        FROM dbo.demographicsphonenumbers ph 
        WHERE ph.phoneNumber IS NOT NULL 
            AND TRIM(ph.phoneNumber) <> '' 
            AND COALESCE(ph.isRemoved, false) = false
            AND ph.DemographicId = d.Id) as PatientPhoneNumbers,
        COALESCE(v_address_line1, '') as AddressLine1,
        COALESCE(v_address_line2, '') as AddressLine2,
        COALESCE(v_city, '') as City,
        COALESCE(v_postal_code, '') as PostalCode,
        COALESCE(v_province, '') as Province,
        COALESCE(v_country, '') as Country
    FROM dbo.patientrecords pr
    INNER JOIN dbo.demographics d ON pr.Id = d.PatientRecordId
    WHERE pr.Id = p_patient_id AND pr.PracticeId = p_practice_id;
END;
$$ LANGUAGE plpgsql;