-- PostgreSQL function for SP_RAD_PatientSearch
-- Migrated from SQL Server stored procedure
-- Radiology patient search functionality

CREATE OR REPLACE FUNCTION dbo.SP_RAD_PatientSearch(
    p_practice_id INTEGER,
    p_patient_last_name VARCHAR(100) DEFAULT NULL,
    p_patient_first_name VARCHAR(100) DEFAULT NULL
)
RETURNS TABLE(
    id integer,
    lastname varchar(100),
    firstname varchar(100),
    patientdob timestamp,
    gender varchar(1)
) AS $$
DECLARE
    v_where_clause TEXT := '';
    v_full_statement TEXT;
BEGIN
    -- Handle patient last name filter
    IF p_patient_last_name IS NOT NULL AND TRIM(p_patient_last_name) <> '' THEN
        v_full_statement := '
            SELECT TOP 20 Id, LastName, FirstName, PatientDOB, Gender
            FROM (
                SELECT DISTINCT 
                    RP.id AS Id,
                    RP.PatientLastName AS LastName,
                    RP.PatientFirstName AS FirstName,
                    RP.PatientBD AS PatientDOB,
                    RP.PatientSex AS Gender
                FROM dbo.RAD_Patient RP 
                JOIN (
                    SELECT RS.PatientId 
                    FROM dbo.RAD_Study RS 
                    WHERE RS.Institution IN (
                        SELECT ri.id 
                        FROM dbo.RAD_Institution ri 
                        WHERE ri.PracticeId = $1
                    )
                ) AS R ON RP.id = R.PatientId
                WHERE LOWER(RP.PatientLastName) LIKE LOWER($2 || ''%'')';

        -- Add first name filter if provided
        IF p_patient_first_name IS NOT NULL AND TRIM(p_patient_first_name) <> '' THEN
            v_full_statement := v_full_statement || ' AND LOWER(RP.PatientFirstName) LIKE LOWER($3 || ''%'')';
        END IF;

        v_full_statement := v_full_statement || '
            ) as z 
            ORDER BY FirstName 
            LIMIT 20';

        -- Execute the dynamic query
        IF p_patient_first_name IS NOT NULL AND TRIM(p_patient_first_name) <> '' THEN
            RETURN QUERY EXECUTE v_full_statement 
                USING p_practice_id, p_patient_last_name, p_patient_first_name;
        ELSE
            RETURN QUERY EXECUTE v_full_statement 
                USING p_practice_id, p_patient_last_name;
        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql;