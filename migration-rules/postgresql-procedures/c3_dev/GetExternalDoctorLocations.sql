-- PostgreSQL function equivalent of SQL Server GetExternalDoctorLocations stored procedure
-- This function gets external doctor addresses and associated phone numbers

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create GetExternalDoctorLocations function
CREATE OR REPLACE FUNCTION dbo.GetExternalDoctorLocations(
    p_external_doctor_id INTEGER
)
RETURNS TABLE(
    ExternalDoctorAddressId integer,
    ExternalDoctorId integer,
    AddressName text,
    AddressLine1 text,
    AddressLine2 text,
    AddressType integer,
    City text,
    PostalCode text,
    Province text,
    Country text,
    IsActiveAddress boolean,
    ExternalDoctorPhoneNumberId integer,
    FaxNumber text,
    PhoneNumber text,
    TypeOfPhoneNumber integer,
    PhoneNumberExtension text,
    IsActivePhone boolean,
    IsActiveLocation boolean,
    ExternalDoctorLocationId integer
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ea.Id AS ExternalDoctorAddressId,
        ea.ExternalDoctorId::integer,
        ea.addressName::text,
        ea.addressLine1::text,
        ea.addressLine2::text,
        ea.addressType::integer,
        ea.city::text,
        ea.postalCode::text,
        ea.province::text,
        ea.country::text,
        ea.IsActive AS IsActiveAddress,
        COALESCE(ph.ExternalDoctorPhoneNumberId, 0)::integer,
        ph.faxNumber::text,
        ph.phoneNumber::text,
        COALESCE(ph.typeOfPhoneNumber, 0)::integer,
        ph.extention::text AS PhoneNumberExtension,
        ph.IsActive AS IsActivePhone,
        ph.IsActiveLocation,
        COALESCE(ph.ExternalDoctorLocationId, 0)::integer
    FROM ExternalDoctorAddresses ea 
    LEFT JOIN (
        SELECT 
            el.ExternalDoctorAddressId,
            el.Id AS ExternalDoctorLocationId,
            el.ExternalDoctorPhoneNumberId, 
            ep.phoneNumber,
            ep.faxNumber,
            ep.typeOfPhoneNumber,
            ep.extention,
            ep.IsActive,
            el.IsActive AS IsActiveLocation
        FROM ExternalDoctorLocations el 
        JOIN ExternalDoctorPhoneNumbers ep ON ep.Id = el.ExternalDoctorPhoneNumberId
        WHERE ep.ExternalDoctorId = p_external_doctor_id
    ) AS ph ON ph.ExternalDoctorAddressId = ea.Id
    WHERE ea.ExternalDoctorId = p_external_doctor_id;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.GetExternalDoctorLocations TO postgres;