-- Migration: GetPatientLocations stored procedure to PostgreSQL
-- Purpose: Get patient location information including external doctor addresses and phone numbers
-- Migrated from: SQL Server stored procedure [dbo].[GetPatientLocations]

CREATE OR REPLACE FUNCTION dbo.GetPatientLocations(
    p_patient_id INTEGER,
    p_external_doctor_id INTEGER DEFAULT 0
) 
RETURNS TABLE (
    PatientId INTEGER,
    PatientLocationId BIGINT,
    IsActivePatientLocation BOOLEAN,
    ExternalDoctorLocationId INTEGER,
    ExternalDoctorAddressId BIGINT,
    ExternalDoctorId INTEGER,
    AddressName TEXT,
    AddressLine1 TEXT,
    AddressLine2 TEXT,
    AddressType INTEGER,
    City TEXT,
    PostalCode TEXT,
    Province TEXT,
    Country TEXT,
    IsActiveAddress BOOLEAN,
    ExternalDoctorPhoneNumberId BIGINT,
    FaxNumber TEXT,
    PhoneNumber TEXT,
    TypeOfPhoneNumber INTEGER,
    PhoneNumberExtension TEXT,
    IsActivePhone BOOLEAN,
    IsActiveLocation BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pl.PatientRecordId::INTEGER as PatientId,
        pl.Id::BIGINT as PatientLocationId,
        COALESCE(pl.IsActive, true) as IsActivePatientLocation,
        pl.ExternalDoctorLocationId::INTEGER,
        ea.Id::BIGINT as ExternalDoctorAddressId,
        ea.ExternalDoctorId::INTEGER,
        ea.addressName as AddressName,
        ea.addressLine1 as AddressLine1,
        ea.addressLine2 as AddressLine2,
        ea.addressType::INTEGER,
        ea.city as City,
        ea.postalCode as PostalCode,
        ea.province as Province,
        ea.country as Country,
        COALESCE(ea.IsActive, true) as IsActiveAddress,
        ph.Id::BIGINT as ExternalDoctorPhoneNumberId,
        ph.faxNumber as FaxNumber,
        ph.phoneNumber as PhoneNumber,
        ph.typeOfPhoneNumber::INTEGER,
        ph.extention as PhoneNumberExtension,
        COALESCE(ph.IsActive, true) as IsActivePhone,
        COALESCE(edl.IsActive, true) as IsActiveLocation
    FROM dbo.patientlocations pl
    INNER JOIN dbo.externaldoctorlocations edl ON pl.ExternalDoctorLocationId = edl.Id
    INNER JOIN dbo.externaldoctoraddresses ea ON edl.ExternalDoctorAddressId = ea.Id	
    INNER JOIN dbo.externaldoctorphonenumbers ph ON edl.ExternalDoctorPhoneNumberId = ph.Id	
    WHERE pl.PatientRecordId = p_patient_id	
        AND ea.ExternalDoctorId = CASE 
            WHEN p_external_doctor_id > 0 THEN p_external_doctor_id 
            ELSE ea.ExternalDoctorId 
        END
        AND ph.ExternalDoctorId = CASE 
            WHEN p_external_doctor_id > 0 THEN p_external_doctor_id 
            ELSE ph.ExternalDoctorId 
        END;
END;
$$ LANGUAGE plpgsql;