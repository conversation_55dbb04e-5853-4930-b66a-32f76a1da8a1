-- GetAppointmentTests procedure for PostgreSQL
-- Migrated from SQL Server stored procedure

CREATE OR REPLACE FUNCTION dbo.GetAppointmentTests(
    p_appointment_id integer
)
RETURNS TABLE (
    id integer,
    testid integer,
    starttime timestamp,
    appointmentteststatsusid integer,
    testduration integer,
    billstatusid integer,
    referraldoctorid integer,
    appointmentid integer,
    accessionnumber varchar(300),
    physiciancomments text,
    techniciancomments text,
    isactive boolean,
    datecreated timestamp,
    dateupdated timestamp,
    setforreview boolean,
    reassigndocid integer,
    reassigndate timestamp,
    assignedtouserid integer,
    performedbytouserid integer,
    permissionid integer,
    isdoctorrequiredinoffice boolean,
    resourcefullname varchar(500),
    testfullname varchar(500),
    testshortname varchar(300),
    requiredevice boolean,
    isradiology boolean,
    teststatuscolor varchar(100),
    teststatus varchar(300)
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        apptest.id,
        apptest.testid,
        apptest.starttime,
        apptest.appointmentteststatsusid,
        apptest.testduration,
        apptest.billstatusid,
        apptest.referraldoctorid,
        apptest.appointmentid,
        COALESCE(apptest.accessionnumber, '') AS accessionnumber,
        COALESCE(apptest.physiciancomments, '') AS physiciancomments,
        COALESCE(apptest.techniciancomments, '') AS techniciancomments,
        apptest.isactive,
        apptest.datecreated,
        apptest.dateupdated,
        apptest.setforreview,
        apptest.reassigndocid,
        apptest.reassigndate,
        COALESCE(res.assignedtouserid, 0) AS assignedtouserid,
        COALESCE(res.performedbytouserid, 0) AS performedbytouserid,
        res.permissionid,
        res.isdoctorrequiredinoffice,
        COALESCE(COALESCE(u.firstname, '') || ' ' || COALESCE(u.lastname, ''), '') AS resourcefullname,
        test.testfullname,
        test.testshortname,
        test.requiredevice,
        test.isradiology,
        teststatus.color AS teststatuscolor,
        teststatus.status AS teststatus
    FROM appointmenttests apptest
    JOIN appointmentteststatus teststatus ON apptest.appointmentteststatsusid = teststatus.id
    JOIN tests test ON apptest.testid = test.id
    JOIN appointmenttestresources res ON apptest.id = res.appointmenttestid 
    LEFT JOIN usersyncs u ON res.assignedtouserid = u.userid
    WHERE apptest.appointmentid = p_appointment_id
      AND apptest.isactive = true 
      AND res.isactive = true;
END;
$$;