-- GetInventoryItem procedure for PostgreSQL
-- Migrated from SQL Server stored procedure

CREATE OR REPLACE FUNCTION dbo.GetInventoryItem(
    p_practice_id integer,
    p_inventory_id integer
)
RETURNS TABLE (
    inventoryid bigint,
    officeid integer,
    officename text,
    devicetypeid integer,
    devicenumber text,
    devicetype text,
    statusid integer,
    statustype text,
    notes text,
    datecreated timestamp with time zone,
    historycount integer
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        i.id AS inventoryid,
        i.officeid,
        o.name AS officename,
        i.inventorytypeid AS devicetypeid,
        i.code AS devicenumber,
        it.name AS devicetype,
        i.statusid,
        s.name AS statustype,
        i.notes,
        i.datecreated,
        COALESCE((
            SELECT COUNT(pe.id)::integer 
            FROM dbo.patientequipments pe
            WHERE pe.inventoryid = i.id
        ), 0) AS historycount
    FROM dbo.storeinventories i
    JOIN dbo.storeinventorytypes it ON i.inventorytypeid = it.id
    JOIN dbo.office o ON i.officeid = o.id
    JOIN dbo.storeinventorystatus s ON i.statusid = s.id
    WHERE o.practiceid = p_practice_id 
      AND i.id = p_inventory_id;
END;
$$;