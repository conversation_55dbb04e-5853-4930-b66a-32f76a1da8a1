-- PostgreSQL function for SP_PracticeAccessionTestForRAD
-- Migrated from SQL Server stored procedure
-- Returns accession test information for radiology integration

CREATE OR REPLACE FUNCTION dbo.SP_PracticeAccessionTestForRAD(
    p_practice_id INTEGER,
    p_accessions VARCHAR(4000)
)
RETURNS TABLE(
    accessionnumber VARCHAR(50),
    testshortname VARCHAR(500),
    testfullname VARCHAR(500),
    appointmenttime TIMESTAMP,
    officeid INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        APT.AccessionNumber::VARCHAR(50) as accessionnumber,
        T.testShortName::VARCHAR(500) as testshortname,
        T.testFullName::VARCHAR(500) as testfullname,
        AP.appointmentTime as appointmenttime,
        AP.OfficeId as officeid
    FROM dbo.Appointments AP 
    JOIN dbo.PatientRecords P ON AP.PatientRecordId = P.Id
    JOIN dbo.AppointmentTests APT ON AP.Id = APT.AppointmentId
    JOIN dbo.Tests T ON APT.TestId = T.Id
    WHERE P.PracticeId = p_practice_id 
        AND APT.AccessionNumber = ANY(
            SELECT TRIM(unnest(string_to_array(p_accessions, ',')))
        )
    ORDER BY AP.appointmentTime;
END;
$$ LANGUAGE plpgsql;