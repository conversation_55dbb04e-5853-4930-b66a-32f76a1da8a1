-- PostgreSQL function migration of fn_GetPatientInfo
-- Original: fn_GetPatientInfo(@patientId INT, @dateToCalulateAge DATETIME = null) RETURNS TABLE
-- Purpose: Retrieves comprehensive patient information including demographics, health cards, and contact info
-- Modified for PostgreSQL compatibility with proper schema references

CREATE OR REPLACE FUNCTION dbo.fn_GetPatientInfo(
    p_patient_id INTEGER,
    p_date_to_calculate_age TIMESTAMP DEFAULT NULL
)
RETURNS TABLE(
    patientid INTEGER,
    fhirid UUID,
    practiceid INTEGER,
    demographicid INTEGER,
    salutation INTEGER,
    aliaslastname VARCHAR(100),
    aliasfirstname VARCHAR(100),
    aliasmiddlename VARCHAR(100),
    usealiases BOOLEAN,
    preferredname VA<PERSON>HA<PERSON>(100),
    firstname VARCHAR(100),
    middlename VARCHAR(100),
    lastname VARCHAR(100),
    dateofbirth TIMESTAMP,
    ageaccurate TEXT,
    gender INTEGER,
    healthcard VARCHAR(100),
    healthcardcode VARCHAR(100),
    healthcardexpirydate TIMESTAMP,
    healthcardprovince VARCHAR(2),
    defaultpaymentmethod INTEGER,
    phonenumbers VARCHAR(500),
    addressline1 VARCHAR(200),
    addressline2 VARCHAR(200),
    city VARCHAR(100),
    postalcode VARCHAR(10),
    province VARCHAR(100),
    country VARCHAR(100)
) AS $$
DECLARE
    v_ohip_id VARCHAR(20);
    v_ohip_version_code VARCHAR(20);
    v_ohip_expire_date TIMESTAMP;
    v_province_ohip VARCHAR(2) := 'ON';
    v_address_line1 VARCHAR(200);
    v_address_line2 VARCHAR(200);
    v_city VARCHAR(100);
    v_postal_code VARCHAR(10);
    v_province VARCHAR(100);
    v_country VARCHAR(100);
    v_age_accurate TEXT;
BEGIN
    -- Get health card information
    SELECT 
        COALESCE(hc.number, ''),
        hc.expirydate,
        CASE 
            WHEN hc.provincecode = 0 THEN 'AB'
            WHEN hc.provincecode = 9 THEN 'PE'
            WHEN hc.provincecode = 4 THEN 'NL'
            WHEN hc.provincecode = 1 THEN 'BC'
            WHEN hc.provincecode = 8 THEN 'ON'
            WHEN hc.provincecode = 2 THEN 'MB'
            WHEN hc.provincecode = 5 THEN 'NS'
            ELSE 'ON'
        END,
        COALESCE(hc.version, '')
    INTO v_ohip_id, v_ohip_expire_date, v_province_ohip, v_ohip_version_code
    FROM dbo.demographicshealthcards hc 
    JOIN dbo.demographics d ON hc.demographicid = d.id
    WHERE d.patientrecordid = p_patient_id
    ORDER BY hc.id DESC
    LIMIT 1;
    
    -- Get address information
    SELECT 
        da.addressline1,
        da.addressline2,
        da.city,
        da.postalcode,
        da.province,
        da.country
    INTO v_address_line1, v_address_line2, v_city, v_postal_code, v_province, v_country
    FROM dbo.demographicsaddresses da 
    JOIN dbo.demographics d ON da.demographicid = d.id
    WHERE d.patientrecordid = p_patient_id 
      AND da.isactive = true
    ORDER BY da.id DESC
    LIMIT 1;
    
    -- Return patient information
    RETURN QUERY
    SELECT
        d.patientrecordid::INTEGER,
        d.fhirid,
        pr.practiceid::INTEGER,
        d.id::INTEGER,
        d.nameprefix::INTEGER,
        d.aliaslastname::VARCHAR(100),
        d.aliasfirstname::VARCHAR(100),
        d.aliasmiddlename::VARCHAR(100),
        d.usealiases,
        d.preferredname::VARCHAR(100),
        COALESCE(d.aliasfirstname, d.firstname)::VARCHAR(100) as firstname,
        COALESCE(d.aliasmiddlename, d.middlename)::VARCHAR(100) as middlename,
        COALESCE(d.aliaslastname, d.lastname)::VARCHAR(100) as lastname,
        d.dateofbirth AT TIME ZONE 'UTC',
        (SELECT age_accurate FROM dbo.fn_CalculateAge(d.dateofbirth, p_date_to_calculate_age) LIMIT 1)::TEXT,
        d.gender::INTEGER,
        v_ohip_id::VARCHAR(100),
        v_ohip_version_code::VARCHAR(100),
        v_ohip_expire_date,
        v_province_ohip::VARCHAR(2),
        d.defaultpaymentmethod::INTEGER,
        (
            SELECT STRING_AGG(
                ph.phonenumber || ' ' || 
                CASE ph.typeofphonenumber
                    WHEN 0 THEN 'H'
                    WHEN 1 THEN 'C'
                    WHEN 2 THEN 'W'
                    ELSE ''
                END, 
                ', ' 
                ORDER BY ph.demographicid, ph.typeofphonenumber, ph.isactive DESC, ph.id DESC
            )
            FROM dbo.demographicsphonenumbers ph
            WHERE ph.phonenumber IS NOT NULL 
              AND TRIM(ph.phonenumber) != '' 
              AND ph.isremoved = false
              AND ph.demographicid = d.id
        )::VARCHAR(500) as phonenumbers,
        v_address_line1::VARCHAR(200),
        v_address_line2::VARCHAR(200),
        v_city::VARCHAR(100),
        v_postal_code::VARCHAR(10),
        v_province::VARCHAR(100),
        v_country::VARCHAR(100)
    FROM dbo.patientrecords pr
    JOIN (
        SELECT * FROM dbo.demographics 
        WHERE patientrecordid = p_patient_id 
        ORDER BY id 
        LIMIT 1
    ) d ON pr.id = d.patientrecordid
    WHERE pr.id = p_patient_id;
END;
$$ LANGUAGE plpgsql;