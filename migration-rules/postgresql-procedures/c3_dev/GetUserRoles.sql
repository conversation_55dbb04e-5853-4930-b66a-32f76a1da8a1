-- =============================================
-- Author:        Migration from SQL Server
-- Create date:   PostgreSQL Migration
-- Description:   Get user roles - PostgreSQL equivalent of [dbo].[GetUserRoles]
-- =============================================

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

CREATE OR REPLACE FUNCTION dbo.GetUserRoles(p_userId INTEGER)
RETURNS TABLE(
    "PracticeId" INTEGER,
    "Id" VARCHAR,
    "Name" VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        r.PracticeId AS "PracticeId",
        r.Id::VARCHAR AS "Id",
        r.Name::VARCHAR AS "Name"
    FROM dbo.aspnetusers u
    JOIN dbo.aspnetuserroles AS ur ON u.Id = ur.UserId
    JOIN dbo.aspnetroles AS r ON ur.RoleId = r.Id
    WHERE u.UserID = p_userId;
END;
$$ LANGUAGE plpgsql;