-- PostgreSQL function for SCH_ExternalDoctorSearch
-- Migrated from SQL Server stored procedure
-- Searches for external doctors based on practice, search terms, or specific external doctor ID

CREATE OR REPLACE FUNCTION dbo.SCH_ExternalDoctorSearch(
    p_practice_id INTEGER,
    p_search_term VARCHAR(200),
    p_external_doctor_id INTEGER DEFAULT 0
)
RETURNS TABLE(
    externaldoctorid integer,
    firstname text,
    lastname text,
    cpso text,
    ohipphysicianid text,
    fax boolean,
    email boolean,
    hrm boolean,
    hrmid text,
    emailaddress text,
    practicedoctorid integer,
    isvip boolean,
    preferreddoctorid integer,
    externaldoctoraddressid integer,
    externaldoctorlocationid integer,
    externaldoctorphoneid integer,
    addressline1 text,
    addressline2 text,
    city text,
    province text,
    postalcode text,
    faxnumber text,
    phonenumber text,
    hasfaxnumber boolean,
    isactive boolean
) AS $$
DECLARE
    v_is_ohip_id BOOLEAN := false;
    v_has_comma BOOLEAN := false;
    v_first_name VARCHAR(50) := '';
    v_last_name VARCHAR(50) := '';
    v_search_terms TEXT[];
BEGIN
    -- Check if we're searching by specific external doctor ID
    IF p_external_doctor_id > 0 THEN
        RETURN QUERY
        SELECT 
            DISTINCT exd.Id::integer AS ExternalDoctorId,
            exd.firstName::text AS FirstName,
            exd.lastName::text AS LastName,
            COALESCE(exd.CPSO, '')::text AS CPSO,
            COALESCE(exd.OHIPPhysicianId, '')::text AS OHIPPhysicianId,
            exd.fax::boolean AS Fax,
            exd.email::boolean AS Email,
            exd.HRM::boolean AS HRM,
            exd.HRMId::text AS HRMId,
            exd.emailAddress::text AS EmailAddress,
            COALESCE(pexd.Id, 0)::integer AS PracticeDoctorId,
            COALESCE(pexd.IsVIP, false)::boolean AS IsVIP,
            COALESCE(pexd.PreferredDoctorId, 0)::integer AS PreferredDoctorId,
            COALESCE(ea.Id, 0)::integer AS ExternalDoctorAddressId,
            COALESCE(el.Id, 0)::integer AS ExternalDoctorLocationId,
            COALESCE(el.ExternalDoctorPhoneNumberId, 0)::integer AS ExternalDoctorPhoneId,
            ea.addressLine1::text AS AddressLine1,
            ea.addressLine2::text AS AddressLine2,
            ea.city::text AS City,
            ea.province::text AS Province,
            ea.postalCode::text AS PostalCode,
            CASE 
                WHEN el.ExternalDoctorPhoneNumberId > 0 THEN 
                    COALESCE((SELECT ep.faxNumber FROM dbo.ExternalDoctorPhoneNumbers ep WHERE ep.Id = el.ExternalDoctorPhoneNumberId LIMIT 1), '')
                ELSE ''
            END::text AS FaxNumber,
            CASE 
                WHEN el.ExternalDoctorPhoneNumberId > 0 THEN 
                    COALESCE((SELECT ep.phoneNumber FROM dbo.ExternalDoctorPhoneNumbers ep WHERE ep.Id = el.ExternalDoctorPhoneNumberId LIMIT 1), '')
                ELSE ''
            END::text AS PhoneNumber,
            COALESCE((
                SELECT 1 FROM dbo.ExternalDoctorPhoneNumbers ep
                WHERE ep.ExternalDoctorId = exd.Id 
                AND ep.faxNumber IS NOT NULL 
                AND TRIM(ep.faxNumber) != ''
                LIMIT 1
            ), 0)::boolean AS HasFaxNumber,
            exd.active::boolean AS IsActive
        FROM dbo.ExternalDoctors exd
        LEFT JOIN dbo.ExternalDoctorAddresses ea ON exd.Id = ea.ExternalDoctorId AND ea.IsActive = true
        LEFT JOIN dbo.ExternalDoctorLocations el ON el.ExternalDoctorAddressId = ea.Id AND el.IsActive = true
        LEFT JOIN dbo.PracticeExternalDoctors pexd ON pexd.ExternalDoctorId = exd.Id AND pexd.PracticeId = p_practice_id
        WHERE exd.active = true AND exd.Id = p_external_doctor_id
        ORDER BY exd.lastName, exd.firstName;
        
        RETURN;
    END IF;

    -- Parse search term
    v_is_ohip_id := p_search_term ~ '^[0-9]+$';
    v_has_comma := POSITION(',' IN p_search_term) > 0;

    IF v_has_comma THEN
        -- Split by comma and trim
        v_search_terms := string_to_array(p_search_term, ',');
        v_last_name := TRIM(v_search_terms[1]);
        IF array_length(v_search_terms, 1) > 1 THEN
            v_first_name := TRIM(v_search_terms[2]);
        END IF;
    ELSE
        v_last_name := p_search_term;
    END IF;

    -- Main search query
    RETURN QUERY
    SELECT 
        DISTINCT exd.Id::integer AS ExternalDoctorId,
        exd.firstName::text AS FirstName,
        exd.lastName::text AS LastName,
        COALESCE(exd.CPSO, '')::text AS CPSO,
        COALESCE(exd.OHIPPhysicianId, '')::text AS OHIPPhysicianId,
        exd.fax::boolean AS Fax,
        exd.email::boolean AS Email,
        exd.HRM::boolean AS HRM,
        exd.HRMId::text AS HRMId,
        exd.emailAddress::text AS EmailAddress,
        COALESCE(pexd.Id, 0)::integer AS PracticeDoctorId,
        COALESCE(pexd.IsVIP, false)::boolean AS IsVIP,
        COALESCE(pexd.PreferredDoctorId, 0)::integer AS PreferredDoctorId,
        COALESCE(ea.Id, 0)::integer AS ExternalDoctorAddressId,
        COALESCE(el.Id, 0)::integer AS ExternalDoctorLocationId,
        COALESCE(el.ExternalDoctorPhoneNumberId, 0)::integer AS ExternalDoctorPhoneId,
        ea.addressLine1::text AS AddressLine1,
        ea.addressLine2::text AS AddressLine2,
        ea.city::text AS City,
        ea.province::text AS Province,
        ea.postalCode::text AS PostalCode,
        CASE 
            WHEN el.ExternalDoctorPhoneNumberId > 0 THEN 
                COALESCE((SELECT ep.faxNumber FROM dbo.ExternalDoctorPhoneNumbers ep WHERE ep.Id = el.ExternalDoctorPhoneNumberId LIMIT 1), '')
            ELSE ''
        END::text AS FaxNumber,
        CASE 
            WHEN el.ExternalDoctorPhoneNumberId > 0 THEN 
                COALESCE((SELECT ep.phoneNumber FROM dbo.ExternalDoctorPhoneNumbers ep WHERE ep.Id = el.ExternalDoctorPhoneNumberId LIMIT 1), '')
            ELSE ''
        END::text AS PhoneNumber,
        COALESCE((
            SELECT 1 FROM dbo.ExternalDoctorPhoneNumbers ep
            WHERE ep.ExternalDoctorId = exd.Id 
            AND ep.faxNumber IS NOT NULL 
            AND TRIM(ep.faxNumber) != ''
            LIMIT 1
        ), 0)::boolean AS HasFaxNumber,
        exd.active::boolean AS IsActive
    FROM dbo.ExternalDoctors exd
    LEFT JOIN dbo.ExternalDoctorAddresses ea ON exd.Id = ea.ExternalDoctorId AND ea.IsActive = true
    LEFT JOIN dbo.ExternalDoctorLocations el ON el.ExternalDoctorAddressId = ea.Id AND el.IsActive = true
    LEFT JOIN dbo.PracticeExternalDoctors pexd ON pexd.ExternalDoctorId = exd.Id AND pexd.PracticeId = p_practice_id
    WHERE 
        exd.active = true
        AND (
            CASE 
                WHEN v_is_ohip_id THEN 
                    exd.OHIPPhysicianId LIKE p_search_term || '%'
                ELSE 
                    (v_last_name = '' OR exd.lastName ILIKE v_last_name || '%')
                    AND (v_first_name = '' OR exd.firstName ILIKE v_first_name || '%')
            END
        )
    ORDER BY exd.lastName, exd.firstName;

END;
$$ LANGUAGE plpgsql;