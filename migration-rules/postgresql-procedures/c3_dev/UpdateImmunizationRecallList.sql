-- PostgreSQL function for UpdateImmunizationRecallList
-- Migrated from SQL Server stored procedure
-- Complex immunization recall processing with patient population targeting

CREATE OR REPLACE FUNCTION dbo.UpdateImmunizationRecallList(
    p_immunization_type_id INTEGER,
    p_doctor_id INTEGER
)
RETURNS TABLE(count integer) AS $$
DECLARE
    v_count INTEGER := 0;
    v_patient_id INTEGER;
    v_gender INTEGER;
    v_frequency INTEGER;
    v_curr_immun_id INTEGER;
    v_age_from INTEGER;
    v_age_to INTEGER;
    v_age_as_of_date TIMESTAMP;
    v_date_cutoff_service TIMESTAMP;
    v_immunization_type_name VARCHAR(255);
    v_age_category VARCHAR(255);
    v_curr_immun_status INTEGER;
    v_row_to_make_inactive INTEGER;
    v_pat_record_id INTEGER;
    v_new_immun_id INTEGER;
    v_current_date TIMESTAMP := NOW();
    v_current_immun_date TIMESTAMP;
    v_last_immun_date TIMESTAMP;
    v_last_immun_status INTEGER;
    
    rec RECORD;
BEGIN
    -- Get immunization type parameters
    SELECT 
        t.AgeFrom,
        t.AgeTo,
        t.DateFrom,
        t.DateTo,
        t.Gender,
        t.Period,
        t.Name,
        t.agecategory
    INTO 
        v_age_from,
        v_age_to,
        v_age_as_of_date,
        v_date_cutoff_service,
        v_gender,
        v_frequency,
        v_immunization_type_name,
        v_age_category
    FROM dbo.VP_CPP_ImmunizationType t
    WHERE t.Id = p_immunization_type_id;

    -- Create temp table for target population
    CREATE TEMP TABLE target_population AS
    SELECT d.*
    FROM dbo.Demographics d 
    JOIN dbo.DemographicsMainResponsiblePhysicians mrp ON d.Id = mrp.DemographicId
    WHERE 1=2; -- Empty initial table

    -- Populate target population based on age category
    IF v_age_category = 'Routine Infants & Children' THEN
        INSERT INTO target_population 
        SELECT d.*
        FROM dbo.Demographics d 
        JOIN dbo.DemographicsMainResponsiblePhysicians mrp ON d.Id = mrp.DemographicId
        WHERE
            mrp.PracticeDoctorId = p_doctor_id 
            AND mrp.isactive = true 
            AND d.active = false -- Means TRUE in legacy system
            AND d.gender = CASE WHEN v_gender = 2 THEN d.gender ELSE v_gender END
            AND EXTRACT(YEAR FROM AGE(v_age_as_of_date, d.dateOfBirth)) * 12 + 
                EXTRACT(MONTH FROM AGE(v_age_as_of_date, d.dateOfBirth)) >= v_age_from
            AND EXTRACT(YEAR FROM AGE(v_age_as_of_date, d.dateOfBirth)) * 12 + 
                EXTRACT(MONTH FROM AGE(v_age_as_of_date, d.dateOfBirth)) <= v_age_to;
    ELSE
        INSERT INTO target_population 
        SELECT d.*
        FROM dbo.Demographics d 
        JOIN dbo.DemographicsMainResponsiblePhysicians mrp ON d.Id = mrp.DemographicId
        WHERE
            mrp.PracticeDoctorId = p_doctor_id 
            AND mrp.isactive = true 
            AND d.active = false -- Means TRUE in legacy system
            AND d.gender = CASE WHEN v_gender = 2 THEN d.gender ELSE v_gender END
            AND EXTRACT(YEAR FROM AGE(v_age_as_of_date, d.dateOfBirth)) >= v_age_from
            AND EXTRACT(YEAR FROM AGE(v_age_as_of_date, d.dateOfBirth)) <= v_age_to;
    END IF;

    -- Create temp table for existing immunizations
    CREATE TEMP TABLE target_population_existing AS
    SELECT i.Id
    FROM target_population t 
    JOIN dbo.VP_CPP_Immunization i ON t.PatientRecordId = i.PatientRecordId 
        AND i.VP_CPP_ImmunizationTypeId = p_immunization_type_id;

    -- Process existing immunizations
    FOR rec IN SELECT * FROM target_population_existing LOOP
        v_curr_immun_id := rec.Id;
        v_row_to_make_inactive := 0;
        v_curr_immun_status := 0;

        -- Find immunization recalls that need processing
        SELECT
            r.VP_CPP_Immunization_ID,
            r.VP_CPP_ImmunizationStatusId,
            r.ID
        INTO 
            v_curr_immun_id,
            v_curr_immun_status,
            v_row_to_make_inactive
        FROM dbo.ImmunizationRecalls r 
        WHERE
            r.VP_CPP_Immunization_ID = v_curr_immun_id 
            AND r.Active = true 
            AND r.VP_CPP_ImmunizationStatusId <> 5 -- Not already overdue
            AND r.DateCreated + INTERVAL '1 month' * (v_frequency - 6) <= v_current_date
        LIMIT 1;

        IF v_row_to_make_inactive > 0 THEN
            IF v_curr_immun_status IN (2, 3, 7) THEN -- refused, completed, tracked
                -- Get patient record ID
                SELECT PatientRecordId INTO v_pat_record_id
                FROM dbo.VP_CPP_Immunization 
                WHERE Id = v_curr_immun_id
                LIMIT 1;

                -- Get current immunization date
                SELECT DateCreated INTO v_current_immun_date
                FROM dbo.ImmunizationRecalls 
                WHERE Id = v_row_to_make_inactive
                LIMIT 1;
                
                -- Get last immunization info
                SELECT 
                    COALESCE(ir.DateCreated, v_current_date),
                    COALESCE(ir.VP_CPP_ImmunizationStatusId, 0)
                INTO 
                    v_last_immun_date,
                    v_last_immun_status
                FROM dbo.VP_CPP_Immunization vp 
                JOIN dbo.ImmunizationRecalls ir ON vp.Id = ir.VP_CPP_Immunization_ID 
                WHERE vp.PatientRecordId = v_pat_record_id 
                    AND vp.VP_CPP_ImmunizationTypeId = p_immunization_type_id 
                    AND ir.Active = true
                ORDER BY ir.DateCreated DESC
                LIMIT 1;

                -- Check if we need to create new immunization record
                IF v_last_immun_status <> 5 THEN
                    -- Insert new immunization
                    INSERT INTO dbo.VP_CPP_Immunization (
                        PatientRecordId, Name, SubmitDate, VP_CPP_ImmunizationTypeId,
                        VP_CPP_ImmunizationStatusId, PhysicianId, refuse, NotRemind,
                        isactive, Setid, Colonoscopy
                    ) VALUES (
                        v_pat_record_id, v_immunization_type_name, v_current_date,
                        p_immunization_type_id, 5, p_doctor_id, false, false,
                        true, 0, false
                    ) RETURNING Id INTO v_new_immun_id;

                    -- Insert immunization recall
                    INSERT INTO dbo.ImmunizationRecalls (
                        Active, VP_CPP_Immunization_ID, VP_CPP_ImmunizationStatusId, DateCreated
                    ) VALUES (
                        true, v_new_immun_id, 5, v_current_date
                    );

                    v_count := v_count + 1;
                END IF;

            ELSIF v_curr_immun_status NOT IN (4, 6) THEN -- Not ineligible
                -- Insert new recall
                INSERT INTO dbo.ImmunizationRecalls (
                    Active, VP_CPP_Immunization_ID, VP_CPP_ImmunizationStatusId, DateCreated
                ) VALUES (
                    true, v_curr_immun_id, 5, v_current_date
                );

                -- Make previous recall inactive
                UPDATE dbo.ImmunizationRecalls
                SET Active = false
                WHERE ID = v_row_to_make_inactive;

                v_count := v_count + 1;
            END IF;
        END IF;
    END LOOP;

    -- Create temp table for missing immunizations
    CREATE TEMP TABLE target_population_missing AS
    SELECT t.PatientRecordId
    FROM target_population t 
    LEFT JOIN dbo.VP_CPP_Immunization i ON t.PatientRecordId = i.PatientRecordId 
        AND i.VP_CPP_ImmunizationTypeId = p_immunization_type_id
    WHERE i.PatientRecordId IS NULL;

    -- Process patients without immunizations
    FOR rec IN SELECT * FROM target_population_missing LOOP
        v_patient_id := rec.PatientRecordId;

        -- Insert new immunization
        INSERT INTO dbo.VP_CPP_Immunization (
            PatientRecordId, Name, SubmitDate, VP_CPP_ImmunizationTypeId,
            VP_CPP_ImmunizationStatusId, PhysicianId, refuse, NotRemind,
            isactive, Setid, Colonoscopy
        ) VALUES (
            v_patient_id, v_immunization_type_name, v_current_date,
            p_immunization_type_id, 0, p_doctor_id, false, false,
            false, 0, false
        ) RETURNING Id INTO v_new_immun_id;

        -- Insert immunization recall
        INSERT INTO dbo.ImmunizationRecalls (
            Active, VP_CPP_Immunization_ID, VP_CPP_ImmunizationStatusId, DateCreated
        ) VALUES (
            true, v_new_immun_id, 5, v_current_date
        );

        v_count := v_count + 1;
    END LOOP;

    -- Clean up temp tables
    DROP TABLE IF EXISTS target_population;
    DROP TABLE IF EXISTS target_population_existing;
    DROP TABLE IF EXISTS target_population_missing;

    -- Return the count
    RETURN QUERY SELECT v_count;
END;
$$ LANGUAGE plpgsql;