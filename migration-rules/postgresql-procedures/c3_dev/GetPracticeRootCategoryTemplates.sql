-- PostgreSQL function for GetPracticeRootCategoryTemplates
-- Migrated from SQL Server stored procedure
-- Gets practice root category templates by practice and group

CREATE OR REPLACE FUNCTION dbo.GetPracticeRootCategoryTemplates(
    p_practice_id INTEGER,
    p_group_id INTEGER
)
RETURNS TABLE(
    practicetemplateid integer,
    practiceid integer,
    datecreated timestamp,
    datelastmodified timestamp,
    lastmodifiedbyuserid integer,
    templateid integer,
    templatename varchar,
    issystem boolean,
    isactive boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        prt.Id AS PracticeTemplateId,
        prt.PracticeId,
        prt.DateCreated,
        prt.DateLastModified,
        prt.LastModifiedByUserId,
        prt.TemplateId,
        rt.TemplateName,
        rt.IsSystem,
        prt.IsActive
    FROM dbo.PracticeRootCategoryTemplates prt
    JOIN dbo.RootTemplates rt ON prt.TemplateId = rt.Id
    WHERE prt.PracticeId = p_practice_id
        AND rt.IsActive = true
        AND rt.GroupId = p_group_id;
END;
$$ LANGUAGE plpgsql;