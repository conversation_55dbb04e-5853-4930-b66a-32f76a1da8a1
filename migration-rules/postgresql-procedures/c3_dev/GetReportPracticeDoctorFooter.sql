-- GetReportPracticeDoctorFooter procedure for PostgreSQL
-- Migrated from SQL Server stored procedure
-- Gets practice doctor footer information for report generation

CREATE OR REPLACE FUNCTION dbo.GetReportPracticeDoctorFooter(
    p_appointment_test_id integer
)
RETURNS TABLE (
    practicedoctorreportfooterid integer,
    practicedoctorid integer,
    practicedoctorfooterimage bytea
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_practice_doctor_id integer;
BEGIN
    -- Get the practice doctor ID from appointment test
    SELECT a.practicedoctorid 
    INTO v_practice_doctor_id
    FROM appointments a
    JOIN appointmenttests t ON a.id = t.appointmentid
    WHERE t.id = p_appointment_test_id;

    -- Return the footer information for that practice doctor
    RETURN QUERY
    SELECT 
        f.id AS practicedoctorreportfooterid,
        f.practicedoctorid,
        f.image AS practicedoctorfooterimage
    FROM practicedoctorreportfooter f 
    WHERE f.practicedoctorid = v_practice_doctor_id;
END;
$$;