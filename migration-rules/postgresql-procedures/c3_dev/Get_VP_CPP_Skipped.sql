-- PostgreSQL function for Get_VP_CPP_Skipped
-- Migrated from SQL Server stored procedure
-- Returns skipped CPP categories for a specific doctor/user

CREATE OR REPLACE FUNCTION dbo.Get_VP_CPP_Skipped(
    p_doctor_id INTEGER
)
RETURNS TABLE(
    Id INTEGER,
    Skipped BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        v.CPP_Category_ID AS Id,
        v.Skip AS Skipped
    FROM 
        VP_CPP_Skipped v
    WHERE
        v.UserID = p_doctor_id;
END;
$$ LANGUAGE plpgsql;