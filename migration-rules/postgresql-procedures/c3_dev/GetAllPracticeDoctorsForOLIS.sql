-- GetAllPracticeDoctorsForOLIS PostgreSQL Function
-- Retrieves all practice doctors configured for OLIS integration

CREATE OR REPLACE FUNCTION GetAllPracticeDoctorsForOLIS(
    p_practiceId INTEGER
)
RETURNS TABLE(
    Id INTEGER,
    PracticeId INTEGER,
    specialtyCodes VARCHAR(255),
    OLIS_AssiningJuridictionId VARCHAR(255),
    OLIS_AssiningJuridictionIdCode VARCHAR(255),
    OLIS_IdType VARCHAR(255),
    OLISActive BOOLEAN,
    ExternalDoctorId INTEGER,
    LastName VARCHAR(255),
    FirstName VARCHAR(255),
    MiddleName VARCHAR(255),
    HRMId VARCHAR(255),
    OHIPPhysicianId VARCHAR(255),
    OLIS_RequestingHIC VARCHAR(255),
    emailAddress VARCHAR(255),
    PreloadState INTEGER,
    PreloadStartDate TIMESTAMP,
    PreloadEndDate TIMESTAMP,
    OLISLastSuccessRunDate TIMESTAMP
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pd.Id,
        pd.PracticeId,
        pd.specialtyCodes,
        pd.OLIS_AssiningJuridictionId,
        pd.OLIS_AssiningJuridictionIdCode,
        pd.OLIS_IdType,
        pd.OLISActive,
        ex.Id AS ExternalDoctorId,
        ex.lastName,
        ex.firstName,
        ex.middleName,
        ex.HRMId,
        ex.OHIPPhysicianId,
        ex.CPSO AS OLIS_RequestingHIC,
        ex.emailAddress,
        pd.OLISPreloadState AS PreloadState,
        pd.OLISPreloadStartDate AS PreloadStartDate,
        pd.OLISPreloadEndDate AS PreloadEndDate,
        (SELECT L.createdDateTime 
         FROM OLISCommunicationLogs L 
         WHERE L.RequestingHIC = ex.CPSO AND L.EMRQueryType = 0  
         ORDER BY L.createdDateTime DESC 
         LIMIT 1) AS OLISLastSuccessRunDate
    FROM PracticeDoctors pd
    JOIN ExternalDoctors ex ON pd.ExternalDoctorId = ex.Id
    WHERE pd.PracticeId = p_practiceId 
      AND pd.OLISActive = TRUE 
      AND ex.active = TRUE 
      AND pd.IsActive = TRUE;
END;
$$;