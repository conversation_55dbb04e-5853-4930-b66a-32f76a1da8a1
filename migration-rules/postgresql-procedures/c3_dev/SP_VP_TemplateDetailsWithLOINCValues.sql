-- PostgreSQL function migration of SP_VP_TemplateDetailsWithLOINCValues stored procedure
-- Original: SP_VP_TemplateDetailsWithLOINCValues(@patientRecordId INT, @templateId INT)
-- Purpose: Gets VP template details with LOINC values

CREATE OR REPLACE FUNCTION dbo.SP_VP_TemplateDetailsWithLOINCValues(
    p_patient_record_id INTEGER,
    p_template_id INTEGER
)
RETURNS TABLE(
    id integer,
    value text,
    vptemplatelfield integer,
    vp_templateid integer,
    templateitemname text,
    templatename text,
    testcode text,
    loinccode text,
    units text,
    testresult text,
    resultunits text,
    testdate timestamp,
    istext boolean,
    normallaw decimal,
    normalhigh decimal
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id,
        d.value,
        d.vptemplatelfield,
        d.vp_templateid,
        f.name as templateitemname,
        t.name as templatename,
        f.testcode,
        f.loinccode,
        f.units,
        lr.testresult,
        lr.units as resultunits,
        lr.testdate AT TIME ZONE 'UTC' as testdate,
        f.istext,
        f.normallaw,
        f.normalhigh
    FROM dbo.vp_template_patient_detail d
    JOIN dbo.vpuniquemeasurements f ON d.vptemplatelfield = f.id
    JOIN dbo.vp_template t ON d.vp_templateid = t.id
    LEFT JOIN dbo.labresults lr ON f.loinccode = lr.loinccode 
        AND lr.patientrecordid = p_patient_record_id
    WHERE d.patientrecordid = p_patient_record_id
      AND d.vp_templateid = p_template_id
    ORDER BY f.name;
END;
$$ LANGUAGE plpgsql;