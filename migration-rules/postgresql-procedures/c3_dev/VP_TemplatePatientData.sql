-- PostgreSQL function migration of VP_TemplatePatientData stored procedure
-- Original: VP_TemplatePatientData(@patientRecordId INT, @templateId INT = 0)
-- Purpose: Gets VP template patient data

CREATE OR REPLACE FUNCTION dbo.VP_TemplatePatientData(
    p_patient_record_id INTEGER,
    p_template_id INTEGER DEFAULT 0
)
RETURNS TABLE(
    id integer,
    value text,
    vptemplatelfield integer,
    vp_templateid integer,
    patientrecordid integer,
    templateitemname text,
    templatename text,
    datecreated timestamp,
    datemodified timestamp,
    isactive boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id,
        d.value,
        d.vptemplatelfield,
        d.vp_templateid,
        d.patientrecordid,
        f.name as templateitemname,
        t.name as templatename,
        d.datecreated AT TIME ZONE 'UTC' as datecreated,
        d.datemodified AT TIME ZONE 'UTC' as datemodified,
        COALESCE(d.isactive, true) as isactive
    FROM dbo.vp_template_patient_detail d
    JOIN dbo.vpuniquemeasurements f ON d.vptemplatelfield = f.id
    JOIN dbo.vp_template t ON d.vp_templateid = t.id
    WHERE d.patientrecordid = p_patient_record_id
      AND (p_template_id = 0 OR d.vp_templateid = p_template_id)
    ORDER BY d.datecreated DESC;
END;
$$ LANGUAGE plpgsql;