-- PostgreSQL function for GetDoctorRootCategoryPhraseSubItems
-- Migrated from SQL Server stored procedure
-- Gets doctor root category phrase sub-items for hierarchical phrase management

CREATE OR REPLACE FUNCTION dbo.GetDoctorRootCategoryPhraseSubItems(
    p_phrase_id INTEGER,
    p_root_category_id INTEGER,
    p_external_doctor_id INTEGER,
    p_practice_id INTEGER,
    p_practice_template_id INTEGER
)
RETURNS TABLE(
    pracrootcatphraseid INTEGER,
    rootcategoryphraseid INTEGER,
    pracrootcategorytempid INTEGER,
    rootcategoryid INTEGER,
    practiceid INTEGER,
    externaldoctorid INTEGER,
    phrasename TEXT,
    phrasevalue TEXT,
    parentid INTEGER,
    doctorrootcategoryphraseid INTEGER,
    displayorder INTEGER,
    isvisible BOOLEAN,
    iscategory BOOLEAN,
    isvisibletoolbar BOOLEAN
) AS $$
DECLARE
    is_root_category BOOLEAN := FALSE;
BEGIN
    -- Check if phraseId equals rootCategoryId (means it's a root category for parent dropdowns)
    IF p_phrase_id = p_root_category_id THEN
        is_root_category := TRUE;
    END IF;

    RETURN QUERY
    SELECT 
        prp.Id AS pracrootcatphraseid,
        prp.RootCategoryPhraseId as rootcategoryphraseid,
        prp.PracRootCategoryTempId as pracrootcategorytempid,
        rcp.RootCategoryId as rootcategoryid,
        p_practice_id AS practiceid,
        p_external_doctor_id AS externaldoctorid,
        COALESCE(dcp.PhraseName, rcp.PhraseName) AS phrasename,
        COALESCE(dcp.PhraseValue, rcp.PhraseValue) AS phrasevalue,
        COALESCE(rcp.ParentId, -1) AS parentid,
        COALESCE(dcp.Id, 0) AS doctorrootcategoryphraseid,
        COALESCE(dcp.DisplayOrder, 1000000) AS displayorder,
        CAST(COALESCE(dcp.IsVisible, TRUE) AS BOOLEAN) AS isvisible, -- make visible if not set by doctor
        rcp.IsSubCategory AS iscategory,
        CAST(TRUE AS BOOLEAN) AS isvisibletoolbar
    FROM dbo.PracticeRootCategoryPhrases prp
    JOIN dbo.RootCategoryPhrases rcp ON prp.RootCategoryPhraseId = rcp.Id
    JOIN dbo.DoctorRootCategoryPhrases dcp ON prp.RootCategoryPhraseId = dcp.RootCategoryPhraseId 
        AND dcp.PracRootCategoryTempId = p_practice_template_id
        AND dcp.ExternalDoctorId = p_external_doctor_id
    WHERE prp.PracRootCategoryTempId = p_practice_template_id
    AND COALESCE(rcp.ParentId, 0) = CASE WHEN is_root_category THEN 0 ELSE p_phrase_id END
    AND rcp.RootCategoryId = p_root_category_id
    AND rcp.IsActive = TRUE
    AND prp.IsActive = TRUE
    AND CASE WHEN dcp.IsActive IS NULL THEN TRUE ELSE dcp.IsActive END = TRUE
    ORDER BY displayorder, phrasename;
    
END;
$$ LANGUAGE plpgsql;