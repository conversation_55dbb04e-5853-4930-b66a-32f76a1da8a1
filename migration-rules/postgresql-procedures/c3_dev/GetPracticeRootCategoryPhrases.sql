-- PostgreSQL function for GetPracticeRootCategoryPhrases
-- Migrated from SQL Server stored procedure
-- Gets practice root category phrases with hierarchical structure and breadcrumb navigation

CREATE OR REPLACE FUNCTION dbo.GetPracticeRootCategoryPhrases(
    p_group_id INTEGER,
    p_practice_id INTEGER,
    p_practice_template_id INTEGER,
    p_root_category_id INTEGER DEFAULT 0
)
RETURNS TABLE(
    pracrootcatphraseid INTEGER,
    rootcategoryphraseid INTEGER,
    pracrootcategorytempid INTEGER,
    roottemplateid INTEGER,
    rootcategoryid INTEGER,
    categoryname TEXT,
    groupid INTEGER,
    phrasename TEXT,
    phrasevalue TEXT,
    parentid INTEGER,
    issubcategory BOOLEAN,
    breadcrum TEXT,
    level INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH phrase_data AS (
        SELECT 
            prcp.Id as pracrootcategoryphraseid,
            prcp.PracRootCategoryTempId as pracrootcategorytempid,
            prcp.RootCategoryPhraseId as rootcategoryphraseid,
            rcp.PhraseName as phrasename,
            rcp.PhraseValue as phrasevalue,
            rcp.ParentId as parentid,
            rcp.IsSubCategory as issubcategory,
            rc.Id as rootcategoryid,
            rc.CategoryName as categoryname,
            rc.GroupId as groupid,
            pt.TemplateId as roottemplateid
        FROM dbo.PracticeRootCategoryPhrases prcp
        JOIN dbo.PracticeRootCategoryTemplates pt ON prcp.PracRootCategoryTempId = pt.Id
        JOIN dbo.RootCategoryPhrases rcp ON prcp.RootCategoryPhraseId = rcp.Id
        JOIN dbo.RootCategories rc ON rcp.RootCategoryId = rc.Id
        WHERE prcp.PracRootCategoryTempId = p_practice_template_id        
        AND rc.GroupId = p_group_id
        AND rcp.RootCategoryId = CASE WHEN p_root_category_id > 0 THEN p_root_category_id ELSE rcp.RootCategoryId END
    ),
    RECURSIVE CustomPhraseCTE AS (
        -- Base case - root level phrases (no parent)
        SELECT 
            phrases.pracrootcategoryphraseid as pracrootcatphraseid,
            phrases.rootcategoryphraseid,
            phrases.pracrootcategorytempid,
            phrases.roottemplateid,
            phrases.rootcategoryid,
            phrases.categoryname,
            phrases.groupid,
            phrases.phrasename,
            phrases.phrasevalue,
            phrases.parentid,
            phrases.issubcategory,
            CAST(phrases.categoryname || ' -> ' || phrases.phrasename AS TEXT) as breadcrum,
            0 as level
        FROM phrase_data phrases        
        WHERE phrases.parentid IS NULL        
        
        UNION ALL
        
        -- Recursive case - child phrases
        SELECT 
            phrases.pracrootcategoryphraseid as pracrootcatphraseid,
            phrases.rootcategoryphraseid,
            cte.pracrootcategorytempid,
            cte.roottemplateid,
            cte.rootcategoryid,
            cte.categoryname,
            cte.groupid,
            phrases.phrasename,
            phrases.phrasevalue,
            phrases.parentid,
            phrases.issubcategory,
            CAST(cte.breadcrum || ' -> ' || phrases.phrasename AS TEXT) as breadcrum,
            cte.level + 1 as level
        FROM phrase_data phrases
        JOIN CustomPhraseCTE cte ON phrases.parentid = cte.rootcategoryphraseid                
        WHERE cte.rootcategoryid = phrases.rootcategoryid
    )
    SELECT 
        cte.pracrootcatphraseid,
        cte.rootcategoryphraseid,
        cte.pracrootcategorytempid,
        cte.roottemplateid,
        cte.rootcategoryid,
        cte.categoryname,
        cte.groupid,
        cte.phrasename,
        cte.phrasevalue,
        COALESCE(cte.parentid, 0) AS parentid,
        cte.issubcategory,
        cte.breadcrum,
        cte.level
    FROM CustomPhraseCTE cte 
    ORDER BY cte.parentid ASC, cte.categoryname ASC, cte.phrasename ASC;
    
END;
$$ LANGUAGE plpgsql;