-- PostgreSQL function for GetRootCategorySavedValuesLogIds
-- Migrated from SQL Server stored procedure
-- Gets root category saved values by appointment test log IDs using array parameter

CREATE OR REPLACE FUNCTION dbo.GetRootCategorySavedValuesLogIds(
    p_appointment_test_log_ids INTEGER[]
)
RETURNS TABLE(
    id INTEGER,
    savedvalue TEXT,
    rootcategoryid INTEGER,
    appointmenttestsavelogid INTEGER,
    appointmenttestid INTEGER,
    logdate TIMESTAMP,
    pracrootcategorytempid INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        rcs.Id as id,
        rcs.SavedValue as savedvalue,
        rcs.RootCategoryId as rootcategoryid,
        rcs.AppointmentTestSaveLogId as appointmenttestsavelogid,
        ats.AppointmentTestId as appointmenttestid,
        ats.LogDate as logdate,
        ats.PracRootCategoryTempId as pracrootcategorytempid
    FROM dbo.RootCategorySavedValues rcs  
    JOIN dbo.AppointmentTestSaveLogs ats ON rcs.AppointmentTestSaveLogId = ats.Id
    WHERE rcs.AppointmentTestSaveLogId = ANY(p_appointment_test_log_ids);
    
END;
$$ LANGUAGE plpgsql;