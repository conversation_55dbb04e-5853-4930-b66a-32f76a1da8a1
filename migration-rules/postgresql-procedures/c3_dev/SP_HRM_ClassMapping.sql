-- PostgreSQL function equivalent of SQL Server SP_HRM_ClassMapping stored procedure
-- This function returns HRM class mapping data for a practice

CREATE OR REPLACE FUNCTION dbo.SP_HRM_ClassMapping(
    p_practice_id INTEGER
)
RETURNS TABLE(
    hrmreportclassid integer,
    hrmreportclass text,
    hrmreportsubclassid integer,
    hrmreportsubclass text,
    accompanyingsubclass text,
    facilityname text,
    loosereportcatetory text,
    status text,
    practiceid integer,
    createddate timestamp
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        hc.id::integer as hrmreportclassid,
        hc.name::text as hrmreportclass,
        hsc.id::integer as hrmreportsubclassid,
        hsc.subclassname::text as hrmreportsubclass,
        hsc.accompanyingsubclassname::text as accompanyingsubclass,
        rsf.facilityname::text as facilityname,
        lrc.category::text as loosereportcatetory,
        rsm.status::text as status,
        rsm.practiceid::integer as practiceid,
        rsm.datecreated::timestamp as createddate
    FROM dbo.hrmreportclasses hc
    JOIN dbo.hrmreportsubclasses hsc ON hc.id = hsc.hrmreportclassid
    JOIN dbo.reportsendingfacilities rsf ON hsc.facilityid = rsf.id
    JOIN dbo.reportsubclassmaps rsm ON hsc.id = rsm.originalsubclassids
    JOIN dbo.loosereportcategories lrc ON rsm.loosereportcategoryid = lrc.id
    WHERE rsm.practiceid = p_practice_id;
END;
$$ LANGUAGE plpgsql;