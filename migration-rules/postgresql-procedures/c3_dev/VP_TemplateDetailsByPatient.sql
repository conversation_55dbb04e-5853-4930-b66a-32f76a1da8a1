-- PostgreSQL function migration of VP_TemplateDetailsByPatient stored procedure
-- Original: VP_TemplateDetailsByPatient(@patientRecordId INT)
-- Purpose: Gets VP template details for a specific patient

CREATE OR REPLACE FUNCTION dbo.VP_TemplateDetailsByPatient(
    p_patient_record_id INTEGER
)
RETURNS TABLE(
    id integer,
    value text,
    vptemplatelfield integer,
    vp_templateid integer,
    templateitemname text,
    templatename text,
    nh integer,
    nl integer,
    th integer,
    tl integer,
    frequency text,
    istext boolean,
    testcode text,
    units text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id,
        d.value,
        d.vptemplatelfield,
        d.vp_templateid,
        f.name as templateitemname,
        t.name as templatename,
        COALESCE(d.nh, 0) as nh,
        COALESCE(d.nl, 0) as nl,
        COALESCE(d.th, 0) as th,
        COALESCE(d.tl, 0) as tl,
        d.frequency,
        f.istext,
        f.testcode,
        f.units
    FROM dbo.vp_template_patient_detail d
    JOIN dbo.vpuniquemeasurements f ON d.vptemplatelfield = f.id
    JOIN dbo.vp_template t ON d.vp_templateid = t.id
    WHERE d.patientrecordid = p_patient_record_id
    ORDER BY f.units DESC;
END;
$$ LANGUAGE plpgsql;