-- PostgreSQL function for SP_ScheduledAppointmentsForBatchHCV  
-- Migrated from SQL Server stored procedure
-- Returns scheduled appointments for batch HCV processing

CREATE OR REPLACE FUNCTION dbo.SP_ScheduledAppointmentsForBatchHCV(
    p_from_date TIMESTAMP,
    p_to_date TIMESTAMP
)
RETURNS TABLE(
    practiceid integer,
    appointmentid integer,
    patientrecordid integer,
    demographicid integer,
    healthcardid integer,
    healthcardnumber text
) AS $$
BEGIN
    RETURN QUERY
    WITH app_data AS (
        SELECT 
            o.PracticeId,
            A.Id AS AppointmentId,
            A.PatientRecordId
        FROM dbo.Appointments A
        JOIN dbo.Office o ON o.Id = A.OfficeId
        WHERE 
            A.appointmentTime >= p_from_date 
            AND A.appointmentTime <= p_to_date
            AND A.IsActive = true 
            AND A.appointmentStatus NOT IN (7, 1, 0, 16) -- cancelled, waitlist, cancellationlist, triage
    ),
    health_card_data AS (
        SELECT 
            a.PracticeId,
            a.AppointmentId,
            a.<PERSON><PERSON><PERSON><PERSON>,
            <PERSON><PERSON>DemographicId,
            HC.Id as HealthCardId,
            (TRIM(HC.number) || COALESCE(UPPER(TRIM(HC.version)), ''))::text AS HealthCardNumber,
            row_number() OVER (PARTITION BY HC.DemographicId ORDER BY HC.Id DESC) as rn
        FROM app_data a
        JOIN dbo.Demographics D ON a.PatientRecordId = D.PatientRecordId
        JOIN dbo.DemographicsHealthCards HC ON D.Id = HC.DemographicId
        WHERE 
            HC.IsActive = false 
            AND HC.IsRemoved = false
            AND HC.number IS NOT NULL
    )
    SELECT 
        h.PracticeId::integer,
        h.AppointmentId::integer,
        h.PatientRecordId::integer,
        h.DemographicId::integer,
        h.HealthCardId::integer,
        h.HealthCardNumber::text
    FROM health_card_data h
    WHERE h.rn = 1;
END;
$$ LANGUAGE plpgsql;