-- PostgreSQL function migration of GetReportPhraseSaveTextByLogIds stored procedure
-- Original: GetReportPhraseSaveTextByLogIds(@appointmentTestLogIds AS dbo.IntegerList READONLY)
-- Purpose: Retrieves report phrase saved text data by appointment test log IDs

CREATE OR REPLACE FUNCTION dbo.GetReportPhraseSaveTextByLogIds(
    p_appointment_test_log_ids INTEGER[] DEFAULT ARRAY[]::INTEGER[]
)
RETURNS TABLE(
    id integer,
    value text,
    appointmentid integer,
    testid integer,
    appointmenttestlogid integer,
    toplevelreportphraseid integer,
    toplevelreportphrasetext varchar(255)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        rpst.id,
        rpst.value,
        rpst.appointmentid,
        rpst.testid,
        rpst.appointmenttestlogid,
        rpst.toplevelreportphraseid,
        rp.name as toplevelreportphrasetext
    FROM dbo.reportphrasesavedtexts rpst
    JOIN dbo.reportphrases rp ON rpst.toplevelreportphraseid = rp.id
    WHERE rpst.appointmenttestlogid = ANY(p_appointment_test_log_ids);
END;
$$ LANGUAGE plpgsql;