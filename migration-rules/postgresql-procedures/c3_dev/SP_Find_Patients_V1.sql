-- PostgreSQL function equivalent of SQL Server SP_Find_Patients_V1 stored procedure
-- This function provides patient search functionality with multiple search criteria
-- Simplified from complex dynamic SQL to static queries for better maintainability

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create function in dbo schema for compatibility
CREATE OR REPLACE FUNCTION dbo.SP_Find_Patients_V1(
    p_practice_id INTEGER,
    p_last_name VARCHAR(200) DEFAULT NULL,
    p_first_name VARCHAR(200) DEFAULT NULL,
    p_date_of_birth DATE DEFAULT NULL,
    p_year_of_birth INTEGER DEFAULT NULL,
    p_patient_id INTEGER DEFAULT NULL,
    p_phone_number VARCHAR(20) DEFAULT NULL,
    p_ohip VARCHAR(15) DEFAULT NULL,
    p_top_result INTEGER DEFAULT 20,
    p_active INTEGER DEFAULT NULL
)
RETURNS TABLE(
    patientid integer,
    patientfhirid uuid,
    firstname text,
    lastname text,
    middlename text,
    dateofbirth timestamp,
    gender integer,
    practiceid integer,
    defaultpaymentmethod integer,
    ohip text,
    ohipversioncode text,
    lastreferringdoctorid integer,
    active integer
) AS $$
DECLARE
    clean_phone_number TEXT;
    year_start_date DATE;
    year_end_date DATE;
    base_query TEXT;
    where_conditions TEXT;
    final_query TEXT;
BEGIN
    -- Clean phone number (remove special characters)
    IF p_phone_number IS NOT NULL THEN
        clean_phone_number := REGEXP_REPLACE(TRIM(p_phone_number), '[^0-9]', '', 'g');
    END IF;
    
    -- Set up year of birth date range
    IF p_year_of_birth IS NOT NULL AND LENGTH(p_year_of_birth::TEXT) = 4 THEN
        year_start_date := MAKE_DATE(p_year_of_birth, 1, 1);
        year_end_date := MAKE_DATE(p_year_of_birth, 12, 31);
    END IF;

    -- OHIP-based search (prioritized for performance)
    IF p_ohip IS NOT NULL AND TRIM(p_ohip) <> '' THEN
        RETURN QUERY
        SELECT 
            d.patientrecordid::integer,
            d.fhirid,
            d.firstname::text,
            d.lastname::text,
            COALESCE(d.middlename, '')::text,
            d.dateofbirth AT TIME ZONE 'UTC',
            d.gender,
            p.practiceid::integer,
            d.defaultpaymentmethod,
            COALESCE(hc.number, '')::text,
            COALESCE(hc.version, '')::text,
            0::integer,
            d.active
        FROM dbo.patientrecords p
        JOIN dbo.demographics d ON p.id = d.patientrecordid
        JOIN (
            SELECT DISTINCT ON (hc.demographicid) 
                hc.demographicid, 
                hc.number, 
                hc.version
            FROM dbo.demographicshealthcards hc 
            WHERE hc.number ILIKE (p_ohip || '%')
            ORDER BY hc.demographicid, hc.id DESC
        ) hc ON d.id = hc.demographicid
        WHERE p.practiceid = p_practice_id
        AND (p_active IS NULL OR d.active = p_active)
        LIMIT p_top_result;
        RETURN;
    END IF;

    -- Multi-criteria search
    IF (p_last_name IS NOT NULL AND TRIM(p_last_name) <> '') OR
       (p_first_name IS NOT NULL AND TRIM(p_first_name) <> '') OR
       p_date_of_birth IS NOT NULL OR
       p_year_of_birth IS NOT NULL OR
       p_patient_id IS NOT NULL OR
       (clean_phone_number IS NOT NULL AND clean_phone_number ~ '^[0-9]+$') THEN

        RETURN QUERY
        WITH patient_search AS (
            SELECT DISTINCT ON (d.id)
                d.patientrecordid,
                d.fhirid,
                d.firstname,
                d.lastname,
                COALESCE(d.middlename, '') as middlename,
                d.dateofbirth,
                d.gender,
                p.practiceid,
                d.defaultpaymentmethod,
                d.active,
                -- Get health card info (concatenated format)
                COALESCE(
                    (SELECT 
                        CASE 
                            WHEN hc.version IS NOT NULL AND hc.version <> '' 
                            THEN hc.number
                            ELSE hc.number 
                        END
                     FROM dbo.demographicshealthcards hc 
                     WHERE hc.demographicid = d.id 
                     ORDER BY hc.id DESC 
                     LIMIT 1), 
                    ''
                ) as ohip_combined,
                -- Get version separately 
                COALESCE(
                    (SELECT hc.version
                     FROM dbo.demographicshealthcards hc 
                     WHERE hc.demographicid = d.id 
                     ORDER BY hc.id DESC 
                     LIMIT 1), 
                    ''
                ) as ohip_version
            FROM dbo.demographics d 
            JOIN dbo.patientrecords p ON p.id = d.patientrecordid
            WHERE p.practiceid = p_practice_id
            AND (p_active IS NULL OR d.active = p_active)
            -- Name searches using ILIKE for case-insensitive partial matching
            AND (p_last_name IS NULL OR TRIM(p_last_name) = '' OR 
                 d.lastname ILIKE ('%' || TRIM(p_last_name) || '%'))
            AND (p_first_name IS NULL OR TRIM(p_first_name) = '' OR 
                 d.firstname ILIKE ('%' || TRIM(p_first_name) || '%'))
            -- Date searches
            AND (p_date_of_birth IS NULL OR d.dateofbirth::date = p_date_of_birth)
            AND (year_start_date IS NULL OR 
                 (d.dateofbirth >= year_start_date AND d.dateofbirth <= year_end_date))
            -- Patient ID search
            AND (p_patient_id IS NULL OR d.patientrecordid = p_patient_id)
            -- Phone number search
            AND (clean_phone_number IS NULL OR 
                 EXISTS (
                     SELECT 1 FROM dbo.demographicsphonenumbers ph 
                     WHERE ph.demographicid = d.id 
                     AND ph.phonenumbersearch = clean_phone_number 
                     AND ph.isremoved = false
                 ))
            ORDER BY d.id, d.lastname, d.firstname
            LIMIT p_top_result
        )
        SELECT 
            ps.patientrecordid::integer,
            ps.fhirid,
            ps.firstname::text,
            ps.lastname::text,
            ps.middlename::text,
            ps.dateofbirth AT TIME ZONE 'UTC',
            ps.gender,
            ps.practiceid::integer,
            ps.defaultpaymentmethod,
            ps.ohip_combined::text,
            ps.ohip_version::text,
            0::integer as lastreferringdoctorid,
            ps.active
        FROM patient_search ps
        ORDER BY ps.lastname, ps.firstname, ps.patientrecordid;

        RETURN;
    END IF;

    -- No search criteria provided - return empty result
    RETURN;
    
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.SP_Find_Patients_V1 TO postgres;