-- PostgreSQL function for GetExternalDocumentPracticePatients
-- Migrated from SQL Server stored procedure
-- Finds patients for external document integration with flexible search criteria

CREATE OR REPLACE FUNCTION dbo.GetExternalDocumentPracticePatients(
    p_practice_id INTEGER,
    p_last_name VA<PERSON>HAR(200) DEFAULT NULL,
    p_first_name VARCHAR(200) DEFAULT NULL,
    p_ohip VARCHAR(15) DEFAULT NULL,
    p_top_result INTEGER DEFAULT 100
)
RETURNS TABLE(
    id INTEGER,
    patientrecordid INTEGER,
    practicedoctorid INTEGER,
    practiceid INTEGER,
    lastname VARCHAR(200),
    firstname VA<PERSON>HAR(200),
    dateofbirth TIMESTAMP,
    ohip VARCHAR(15),
    active BOOLEAN
) AS $$
DECLARE
    query_sql TEXT;
    search_last_name TEXT;
    search_first_name TEXT;
BEGIN
    -- Set static limit to prevent performance issues
    IF p_top_result IS NULL OR p_top_result > 100 THEN
        p_top_result := 100;
    END IF;
    
    -- OHIP-based search (highest priority)
    IF p_ohip IS NOT NULL AND TRIM(p_ohip) <> '' THEN
        RETURN QUERY
        SELECT 
            D.Id as id,
            D.PatientRecordId as patientrecordid,
            (SELECT DMP.PracticeDoctorId 
             FROM dbo.DemographicsMainResponsiblePhysicians DMP 
             WHERE DMP.DemographicId = D.Id 
             ORDER BY DMP.Id DESC 
             LIMIT 1) as practicedoctorid,
            p_practice_id as practiceid,
            D.lastName as lastname,
            D.firstName as firstname,
            D.dateOfBirth as dateofbirth,
            COALESCE(HC.number, '') as ohip,
            D.active as active
        FROM dbo.PatientRecords P
        JOIN dbo.Demographics D ON P.Id = D.PatientRecordId
        JOIN (
            SELECT hc.DemographicId, hc.number
            FROM dbo.DemographicsHealthCards hc 
            WHERE hc.number ILIKE p_ohip || '%'
            LIMIT 20000
        ) HC ON D.Id = HC.DemographicId
        WHERE P.PracticeId = p_practice_id 
        ORDER BY D.lastname, D.firstname
        LIMIT p_top_result;
        
    -- Name-based search using full-text search equivalent
    ELSIF (p_last_name IS NOT NULL AND TRIM(p_last_name) <> '') 
       OR (p_first_name IS NOT NULL AND TRIM(p_first_name) <> '') THEN
        
        -- Prepare search terms (convert SQL Server CONTAINS to PostgreSQL patterns)
        search_last_name := TRIM(COALESCE(p_last_name, ''));
        search_first_name := TRIM(COALESCE(p_first_name, ''));
        
        -- If both names are the same, search both fields with same term
        IF LOWER(search_last_name) = LOWER(search_first_name) AND search_last_name <> '' THEN
            RETURN QUERY
            SELECT 
                D.Id as id,
                D.PatientRecordId as patientrecordid,
                (SELECT DMP.PracticeDoctorId 
                 FROM dbo.DemographicsMainResponsiblePhysicians DMP 
                 WHERE DMP.DemographicId = D.Id 
                 ORDER BY DMP.Id DESC 
                 LIMIT 1) as practicedoctorid,
                p_practice_id as practiceid,
                D.lastName as lastname,
                D.firstName as firstname,
                D.dateOfBirth as dateofbirth,
                COALESCE((SELECT hc.number 
                         FROM dbo.DemographicsHealthCards hc 
                         WHERE D.Id = hc.DemographicId 
                         ORDER BY hc.Id DESC 
                         LIMIT 1), '') as ohip,
                D.active as active
            FROM dbo.PatientRecords P
            JOIN dbo.Demographics D ON P.Id = D.PatientRecordId
            WHERE P.PracticeId = p_practice_id 
                AND (D.lastName ILIKE search_last_name || '%' 
                     OR D.firstName ILIKE search_last_name || '%')
            ORDER BY D.lastname, D.firstname
            LIMIT p_top_result;
        ELSE
            -- Separate name search
            RETURN QUERY
            SELECT 
                D.Id as id,
                D.PatientRecordId as patientrecordid,
                (SELECT DMP.PracticeDoctorId 
                 FROM dbo.DemographicsMainResponsiblePhysicians DMP 
                 WHERE DMP.DemographicId = D.Id 
                 ORDER BY DMP.Id DESC 
                 LIMIT 1) as practicedoctorid,
                p_practice_id as practiceid,
                D.lastName as lastname,
                D.firstName as firstname,
                D.dateOfBirth as dateofbirth,
                COALESCE((SELECT hc.number 
                         FROM dbo.DemographicsHealthCards hc 
                         WHERE D.Id = hc.DemographicId 
                         ORDER BY hc.Id DESC 
                         LIMIT 1), '') as ohip,
                D.active as active
            FROM dbo.PatientRecords P
            JOIN dbo.Demographics D ON P.Id = D.PatientRecordId
            WHERE P.PracticeId = p_practice_id 
                AND (search_last_name = '' OR D.lastName ILIKE search_last_name || '%')
                AND (search_first_name = '' OR D.firstName ILIKE search_first_name || '%')
            ORDER BY D.lastname, D.firstname
            LIMIT p_top_result;
        END IF;
    END IF;
    
END;
$$ LANGUAGE plpgsql;