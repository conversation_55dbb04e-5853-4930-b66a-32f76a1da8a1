-- PostgreSQL function for GetPracticeTemplateDoctors
-- Migrated from SQL Server stored procedure
-- Gets practice template doctors by practice, group, and template

CREATE OR REPLACE FUNCTION dbo.GetPracticeTemplateDoctors(
    p_practice_id INTEGER,
    p_group_id INTEGER,
    p_practice_template_id INTEGER
)
RETURNS TABLE(
    doctorpracticetemplateid INTEGER,
    practicetemplateid INTEGER,
    practiceid INTEGER,
    datecreated TIMESTAMP,
    datelastmodified TIMESTAMP,
    lastmodifiedbyuserid INTEGER,
    templateid INTEGER,
    templatename VARCHAR,
    issystem BOOLEAN,
    isactive BOOLEAN,
    isactivedoctortemplate BOOLEAN,
    isdefault BOOLEAN,
    externaldoctorid INTEGER,
    firstname VA<PERSON>HA<PERSON>,
    lastname VA<PERSON>HAR
) AS $$
DECLARE
    tbd_doctor_id INTEGER := 17428;
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(drt.Id, 0) AS doctorpracticetemplateid,
        prt.Id AS practicetemplateid,
        prt.PracticeId AS practiceid,
        prt.DateCreated AS datecreated,
        prt.DateLastModified AS datelastmodified,
        prt.LastModifiedByUserId AS lastmodifiedbyuserid,
        prt.TemplateId AS templateid,
        rt.TemplateName AS templatename,
        rt.IsSystem AS issystem,
        prt.IsActive AS isactive,
        COALESCE(drt.IsActive, false) AS isactivedoctortemplate,
        COALESCE(drt.IsDefault, false) AS isdefault,
        pd.ExternalDoctorId AS externaldoctorid,
        ex.Firstname AS firstname,
        ex.lastName AS lastname
    FROM dbo.PracticeRootCategoryTemplates prt
    JOIN dbo.PracticeDoctors pd ON prt.PracticeId = pd.PracticeId
    JOIN dbo.ExternalDoctors ex ON pd.ExternalDoctorId = ex.Id
    JOIN dbo.RootTemplates rt ON prt.TemplateId = rt.Id
    LEFT JOIN dbo.DoctorRootCategoryTemplates drt ON drt.PracRootCategoryTempId = prt.Id 
        AND drt.ExternalDoctorId = pd.ExternalDoctorId
    WHERE prt.PracticeId = p_practice_id
        AND rt.IsActive = true
        AND rt.GroupId = p_group_id
        AND prt.PracticeId = p_practice_id
        AND pd.PracticeId = p_practice_id
        AND prt.Id = p_practice_template_id
        AND pd.ExternalDoctorId != tbd_doctor_id
    ORDER BY ex.lastName;
    
END;
$$ LANGUAGE plpgsql;