-- PostgreSQL function migration of Get_VP_ReportPhrases_Skipped stored procedure
-- Original: Get_VP_ReportPhrases_Skipped(@doctorID INT)
-- Purpose: Gets skipped VP report phrases for a specific doctor

CREATE OR REPLACE FUNCTION dbo.Get_VP_ReportPhrases_Skipped(
    p_doctor_id INTEGER
)
RETURNS TABLE(
    id integer,
    userid integer,
    reportphraseid integer,
    isskipped boolean,
    datecreated timestamp
    -- Note: Add other columns as needed based on actual table structure
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.userid,
        c.reportphraseid,
        c.isskipped,
        c.datecreated AT TIME ZONE 'UTC' as datecreated
    FROM dbo.vp_reportphrases_skipped c
    WHERE c.userid = p_doctor_id;
END;
$$ LANGUAGE plpgsql;