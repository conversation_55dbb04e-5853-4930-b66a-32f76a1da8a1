-- GetPracticeScheduledUserWeekDays procedure for PostgreSQL
-- Migrated from SQL Server stored procedure

CREATE OR REPLACE FUNCTION dbo.GetPracticeScheduledUserWeekDays(
    p_practice_id integer,
    p_office_id integer
)
RETURNS TABLE (
    userid integer,
    scheduleweekdayid integer,
    officeid integer,
    starttime time,
    finishtime time,
    reservedtime time,
    absenttime time,
    date timestamp,
    dayofweek integer,
    dayoff boolean,
    comment varchar(500)
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.userid,
        s.id AS scheduleweekdayid,
        s.officeid,
        s.starttime,
        s.finishtime,
        s.reservedtime,
        s.absenttime,
        s.date,
        s.dayofweek,
        s.dayoff,
        COALESCE(s.comment, '') AS comment
    FROM scheduleweekdays s
    JOIN scheduleusers u ON s.scheduleuserid = u.id
    WHERE s.officeid = p_office_id;
    -- Note: Practice ID filtering not present in original procedure
    -- but parameter kept for interface consistency
END;
$$;