-- PostgreSQL function migration of SP_Get_VP_MeasurementSavedValue stored procedure
-- Original: SP_Get_VP_MeasurementSavedValue(@appointmentId INT, @patientRecordId INT, @VP_AppointmentTestLogId INT)
-- Purpose: Gets saved measurement values for a VP appointment

CREATE OR REPLACE FUNCTION dbo.SP_Get_VP_MeasurementSavedValue(
    p_appointment_id INTEGER,
    p_patient_record_id INTEGER,
    p_vp_appointment_test_log_id INTEGER
)
RETURNS TABLE(
    appointmentid integer,
    patientrecordid integer,
    vp_appointmenttestlogid integer,
    vpmeasurementid integer,
    value text,
    valuetype text,
    userid integer,
    username varchar(200)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.appointmentid,
        m.patientrecordid,
        m.vp_appointmenttestlogid,
        m.vpuniquemeasurementid as vpmeasurementid,
        m.value,
        um.valuetype,
        l.userid,
        COALESCE(u.lastname || ' ' || u.firstname, '') as username
    FROM dbo.vp_measurementsavedvalue m
    JOIN dbo.vpuniquemeasurements um ON m.vpuniquemeasurementid = um.id
    JOIN dbo.vp_appointmenttestlog l ON m.vp_appointmenttestlogid = l.id
    JOIN dbo.aspnetusers u ON l.userid = u.userid
    WHERE m.appointmentid = p_appointment_id 
      AND m.patientrecordid = p_patient_record_id;
      -- Note: VP_AppointmentTestLogId filter is commented out in original
END;
$$ LANGUAGE plpgsql;