CREATE OR REPLACE FUNCTION dbo.GetEconsultMetadata(
    p_practice_id INTEGER DEFAULT NULL,
    p_patient_last_name TEXT DEFAULT NULL,
    p_requester_name TEXT DEFAULT NULL,
    p_recipient_name TEXT DEFAULT NULL,
    p_consult_state TEXT DEFAULT NULL,
    p_date_submitted TIMESTAMP DEFAULT NULL,
    p_consult_flag TEXT DEFAULT NULL,
    p_patient_needs_to_be_seen INTEGER DEFAULT 0, -- 0 - all, 1 - true, 2 - false
    p_saved_completed_flag INTEGER DEFAULT 0,     -- 0 - all, 1 - true, 2 - false  
    p_patient_association_flag INTEGER DEFAULT 0  -- 0 - all, 1 - true, 2 - false
)
RETURNS TABLE(
    id INTEGER,
    practiceid INTEGER,
    caseid TEXT,
    datesaved TIMESTAMP,
    datelastupdated TIMESTAMP,
    datesubmitted TIMESTAMP,
    requestername TEXT,
    recipientname TEXT,
    requesterpractitionerid TEXT,
    issuername TEXT,
    issuergroup TEXT,
    coverageid INTEGER,
    title TEXT,
    description TEXT,
    emrinitialrequest BOOLEAN,
    useid INTEGER,
    respondentspecialty TEXT,
    respondentsubspecialty TEXT,
    respondentgroupororganizationname TEXT,
    patientfamily TEXT,
    patientgiven TEXT,
    patientgender TEXT,
    patientbirthdate TIMESTAMP,
    patient_hcn TEXT,
    patient_hcn_vc TEXT,
    authorname TEXT,
    patientneedstobeseen BOOLEAN,
    consultstate TEXT,
    savedcompletedflag BOOLEAN,
    consultflag TEXT,
    patientid INTEGER,
    patientassociationflag BOOLEAN
) AS $$
DECLARE
    v_start_date TIMESTAMP;
    v_end_date TIMESTAMP;
BEGIN
    -- Calculate date range if date submitted is provided (3 months before to end of that day)
    IF p_date_submitted IS NOT NULL THEN
        v_start_date := (p_date_submitted - INTERVAL '3 months')::DATE;
        v_end_date := p_date_submitted::DATE + INTERVAL '23 hours 59 minutes 59 seconds';
    END IF;

    RETURN QUERY
    SELECT 
        M.Id,
        M.PracticeId,
        M.CaseId,
        M.DateSaved AT TIME ZONE 'UTC',
        M.DateLastUpdated AT TIME ZONE 'UTC',
        M.DateSubmitted AT TIME ZONE 'UTC',
        M.RequesterName,
        M.RecipientName,
        M.RequesterPractitionerId,
        M.IssuerName,
        M.IssuerGroup,
        M.CoverageId,
        M.Title,
        M.Description,
        M.EmrInitialRequest,
        M.UseId,
        M.RespondentSpecialty,
        M.RespondentSubSpecialty,
        M.RespondentGroupOrOrganizationName,
        M.PatientFamily,
        M.PatientGiven,
        M.PatientGender,
        M.PatientBirthDate AT TIME ZONE 'UTC',
        M.Patient_HCN,
        M.Patient_HCN_VC,
        M.AuthorName,
        M.PatientNeedsToBeSeen,
        M.ConsultState,
        M.SavedCompletedFlag,
        COALESCE(M.ConsultFlag, 'NOFLAG') AS ConsultFlag,
        A.PatientId,
        CASE WHEN A.PatientId IS NOT NULL THEN true ELSE false END AS PatientAssociationFlag
    FROM dbo.EconsultMetadata M
    LEFT JOIN dbo.EconsultPatientAssociations A ON M.CaseId = A.CaseId
    WHERE 1=1
        -- Practice ID filter
        AND (p_practice_id IS NULL OR M.PracticeId = p_practice_id)
        -- Patient last name filter (case-insensitive partial match)
        AND (p_patient_last_name IS NULL OR 
             TRIM(p_patient_last_name) = '' OR 
             M.PatientFamily ILIKE '%' || p_patient_last_name || '%')
        -- Requester name filter (case-insensitive partial match)
        AND (p_requester_name IS NULL OR 
             TRIM(p_requester_name) = '' OR 
             M.RequesterName ILIKE '%' || p_requester_name || '%')
        -- Recipient name filter (case-insensitive partial match)
        AND (p_recipient_name IS NULL OR 
             TRIM(p_recipient_name) = '' OR 
             M.RecipientName ILIKE '%' || p_recipient_name || '%')
        -- Consult state filter (case-insensitive partial match)
        AND (p_consult_state IS NULL OR 
             TRIM(p_consult_state) = '' OR 
             M.ConsultState ILIKE '%' || p_consult_state || '%')
        -- Consult flag filter (case-insensitive partial match)
        AND (p_consult_flag IS NULL OR 
             TRIM(p_consult_flag) = '' OR 
             M.ConsultFlag ILIKE '%' || p_consult_flag || '%')
        -- Patient needs to be seen filter
        AND (p_patient_needs_to_be_seen = 0 OR
             (p_patient_needs_to_be_seen = 1 AND M.PatientNeedsToBeSeen = true) OR
             (p_patient_needs_to_be_seen = 2 AND M.PatientNeedsToBeSeen = false))
        -- Saved completed flag filter
        AND (p_saved_completed_flag = 0 OR
             (p_saved_completed_flag = 1 AND M.SavedCompletedFlag = true) OR
             (p_saved_completed_flag = 2 AND M.SavedCompletedFlag = false))
        -- Patient association filter
        AND (p_patient_association_flag = 0 OR
             (p_patient_association_flag = 1 AND A.PatientId IS NOT NULL) OR
             (p_patient_association_flag = 2 AND A.PatientId IS NULL))
        -- Date submitted filter (3 months range)
        AND (p_date_submitted IS NULL OR 
             (M.DateSubmitted >= v_start_date AND M.DateSubmitted <= v_end_date));
END;
$$ LANGUAGE plpgsql;