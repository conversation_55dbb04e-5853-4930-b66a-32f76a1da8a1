-- PostgreSQL function for SP_Batch_HCV_Responses
-- Migrated from SQL Server stored procedure
-- Returns batch HCV responses for appointments within date range

CREATE OR REPLACE FUNCTION dbo.SP_Batch_HCV_Responses(
    p_practice_id INTEGER,
    p_office_id INTEGER DEFAULT NULL,
    p_from_date TIMESTAMP,
    p_to_date TIMESTAMP,
    p_is_valid BOOLEAN
)
RETURNS TABLE(
    id integer,
    patientrecordid integer,
    patientname text,
    doctorname text,
    appointmentdatetime timestamp,
    healthcard text,
    responsecode text,
    responsedescription text,
    responseaction text,
    isvalid boolean,
    createdate timestamp
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        V.Id::integer,
        V.PatientRecordId::integer,
        (D.lastName || ', ' || D.firstName)::text AS PatientName,
        (ED.lastName || ', ' || ED.firstName)::text AS DoctorName,
        A.appointmentTime AT TIME ZONE 'UTC' AS AppointmentDateTime,
        V.HealthCard::text,
        V.ResponseCode::text,
        V.ResponseDescription::text,
        V.ResponseAction::text,
        V.IsValid::boolean,
        V.CreateDate AT TIME ZONE 'UTC'
    FROM dbo.AppointmentHealthCardValidation V
    JOIN dbo.PatientRecords R ON V.PatientRecordId = R.Id
    JOIN dbo.Demographics D ON R.Id = D.PatientRecordId
    JOIN dbo.Appointments A ON V.AppointmentId = A.Id
    JOIN dbo.PracticeDoctors PD ON PD.Id = A.PracticeDoctorId
    JOIN dbo.ExternalDoctors ED ON PD.ExternalDoctorId = ED.Id
    JOIN dbo.Office o ON (o.id = a.officeid AND o.practiceid = r.practiceid)
    WHERE 
        R.PracticeId = p_practice_id
        AND (p_office_id IS NULL OR p_office_id = 0 OR A.OfficeId = p_office_id)
        AND A.appointmentTime >= p_from_date 
        AND A.appointmentTime <= p_to_date
        AND V.IsValid = p_is_valid
    ORDER BY A.appointmentTime
    LIMIT 1000;
END;
$$ LANGUAGE plpgsql;