-- PostgreSQL function migration of SP_VP_GetVitalsAndLabs_Acc stored procedure
-- Original: SP_VP_GetVitalsAndLabs_Acc(@patientRecordId INT, @appointmentId INT = 0, @accessionNumber INT = NULL)
-- Purpose: Gets VP vitals and labs with accession numbers

CREATE OR REPLACE FUNCTION dbo.SP_VP_GetVitalsAndLabs_Acc(
    p_patient_record_id INTEGER,
    p_appointment_id INTEGER DEFAULT 0,
    p_accession_number INTEGER DEFAULT NULL
)
RETURNS TABLE(
    id integer,
    patientrecordid integer,
    appointmentid integer,
    vitalname text,
    vitalvalue text,
    units text,
    measurementdate timestamp,
    category text,
    isvital boolean,
    islab boolean,
    testcode text,
    referencerange text,
    resultflag text,
    accessionnumber integer
) AS $$
BEGIN
    RETURN QUERY
    -- Vitals
    SELECT 
        v.id,
        v.patientrecordid,
        v.appointmentid,
        v.vitalname,
        v.vitalvalue,
        v.units,
        v.measurementdate AT TIME ZONE 'UTC' as measurementdate,
        'Vitals'::text as category,
        true as isvital,
        false as islab,
        v.testcode,
        v.referencerange,
        v.resultflag,
        NULL::integer as accessionnumber
    FROM dbo.vitals v
    WHERE v.patientrecordid = p_patient_record_id
      AND (p_appointment_id = 0 OR v.appointmentid = p_appointment_id)
    
    UNION ALL
    
    -- Labs
    SELECT 
        lr.id,
        lr.patientrecordid,
        lr.appointmentid,
        lr.testname as vitalname,
        lr.testresult as vitalvalue,
        lr.units,
        lr.testdate as measurementdate,
        'Labs'::text as category,
        false as isvital,
        true as islab,
        lr.testcode,
        lr.referencerange,
        lr.resultflag,
        lr.accessionnumber
    FROM dbo.labresults lr
    WHERE lr.patientrecordid = p_patient_record_id
      AND (p_appointment_id = 0 OR lr.appointmentid = p_appointment_id)
      AND (p_accession_number IS NULL OR lr.accessionnumber = p_accession_number)
    
    ORDER BY measurementdate DESC;
END;
$$ LANGUAGE plpgsql;