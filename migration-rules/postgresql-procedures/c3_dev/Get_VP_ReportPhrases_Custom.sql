-- PostgreSQL function migration of Get_VP_ReportPhrases_Custom stored procedure
-- Original: Get_VP_ReportPhrases_Custom(@doctorID INT)
-- Purpose: Gets custom VP report phrases for a specific doctor

CREATE OR REPLACE FUNCTION dbo.Get_VP_ReportPhrases_Custom(
    p_doctor_id INTEGER
)
RETURNS TABLE(
    id integer,
    doctorid integer,
    customphrase text,
    rank integer,
    isactive boolean
    -- Note: Add other columns as needed based on actual table structure
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        c.doctorid,
        c.customphrase,
        c.rank,
        c.isactive
    FROM dbo.vp_reportphrase_custom c
    WHERE c.doctorid = p_doctor_id
    ORDER BY c.rank;
END;
$$ LANGUAGE plpgsql;