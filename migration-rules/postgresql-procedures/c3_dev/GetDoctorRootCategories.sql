-- PostgreSQL function for GetDoctorRootCategories
-- Migrated from SQL Server stored procedure
-- Gets doctor root categories by calling the PostgreSQL equivalent function

CREATE OR REPLACE FUNCTION dbo.GetDoctorRootCategories(
    p_group_id INTEGER,
    p_external_doctor_id INTEGER,
    p_practice_id INTEGER,
    p_practice_template_id INTEGER DEFAULT 0,
    p_root_category_id INTEGER DEFAULT 0
)
RETURNS TABLE(
    categorynameoriginal TEXT,
    categorynamecustom TEXT,
    accumulative BOOLEAN,
    externaldoctorid INTEGER,
    rootcategoryid INTEGER,
    groupid INTEGER,
    practiceid INTEGER,
    pracrootcategorytempid INTEGER,
    templateid INTEGER,
    templatename TEXT,
    isvisible BOOLEAN,
    isvisiblelabel BOOLEAN,
    isvisibleinletter BOOLEAN,
    isvisibletoolbar BOOLEAN,
    doctorrootcategoryid INTEGER,
    displayorder INTEGER
) AS $$
BEGIN
    -- This procedure calls the PostgreSQL function equivalent of SQL Server fn_GetDoctorRootCategories
    -- The function should be migrated separately and contain the main logic
    RETURN QUERY
    SELECT 
        drc.categorynameoriginal,
        drc.categorynamecustom,
        drc.accumulative,
        drc.externaldoctorid,
        drc.rootcategoryid,
        drc.groupid,
        drc.practiceid,
        drc.pracrootcategorytempid,
        drc.templateid,
        drc.templatename,
        drc.isvisible,
        drc.isvisiblelabel,
        drc.isvisibleinletter,
        drc.isvisibletoolbar,
        drc.doctorrootcategoryid,
        drc.displayorder
    FROM dbo.fn_GetDoctorRootCategories(
        p_group_id, 
        p_external_doctor_id, 
        p_practice_id, 
        p_practice_template_id, 
        p_root_category_id
    ) drc
    ORDER BY drc.displayorder;
    
END;
$$ LANGUAGE plpgsql;