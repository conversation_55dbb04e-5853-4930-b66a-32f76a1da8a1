-- =============================================
-- Author:        Migration from SQL Server
-- Create date:   PostgreSQL Migration
-- Description:   Get practice scheduled users - PostgreSQL equivalent of [dbo].[GetPracticeScheduledUsers]
-- =============================================

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- First, create the GetTechnicianTypes function if it doesn't exist
CREATE OR REPLACE FUNCTION dbo.fn_GetTechnicianTypes()
RETURNS TABLE(
    Id INTEGER,
    TypeName VARCHAR(50)
) AS $$
BEGIN
    RETURN QUERY SELECT * FROM (VALUES 
        (4, 'CardiologyTech'::VARCHAR(50)),
        (6, 'VascularTech'::VARCHAR(50)),
        (7, 'SETech'::VARCHAR(50)),
        (8, 'GXTTech'::VARCHAR(50)),
        (9, 'EchoTech'::VARCHAR(50)),
        (10, 'HolterTech'::VARCHAR(50)),
        (11, 'ECGTech'::VARCHAR(50)),
        (15, 'NuclearTech'::VARCHAR(50)),
        (16, 'USTech'::VARCHAR(50)),
        (17, 'XRayTech'::VARCHAR(50)),
        (18, 'PulmonaryTech'::VARCHAR(50)),
        (19, 'Nurse'::VARCHAR(50))
    ) AS tech_types(Id, TypeName);
END;
$$ LANGUAGE plpgsql;

-- Main GetPracticeScheduledUsers function
CREATE OR REPLACE FUNCTION dbo.GetPracticeScheduledUsers(
    p_practiceId INTEGER, 
    p_officeId INTEGER DEFAULT NULL, 
    p_userId INTEGER DEFAULT 0
)
RETURNS TABLE(
    PracticeId INTEGER,
    PracticeDoctorId BIGINT,
    UserId INTEGER,
    ApplicationUserId VARCHAR,
    UserName VARCHAR,
    FirstName VARCHAR,
    LastName VARCHAR,
    MiddleName VARCHAR,
    CerebrumUserType INTEGER,
    Status INTEGER,
    StartDate TIMESTAMP WITH TIME ZONE,
    EndDate TIMESTAMP WITH TIME ZONE,
    UpdatedDate TIMESTAMP WITH TIME ZONE,
    PermissionIds VARCHAR
) AS $$
DECLARE
    filter_count INTEGER := 0;
    office_ids INTEGER[];
    active_user_filter_ids INTEGER[];
    user_doctor_filter_ids INTEGER[];
    user_tech_filter_ids INTEGER[];
BEGIN
    -- Get office IDs (equivalent to @tblOffices table variable)
    IF p_officeId IS NULL THEN
        SELECT ARRAY(SELECT o.Id FROM dbo.office o WHERE o.PracticeId = p_practiceId) INTO office_ids;
    ELSE
        office_ids := ARRAY[p_officeId];
    END IF;
    
    -- Get active user filters (equivalent to @tblActiveUserFilters table variable)
    SELECT ARRAY(
        SELECT uf.UserFilterId 
        FROM dbo.userscheduleviewfilters uf 
        WHERE uf.UserId = p_userId 
        AND uf.IsActive = true
        AND uf.OfficeId = ANY(office_ids)
    ) INTO active_user_filter_ids;
    
    filter_count := COALESCE(array_length(active_user_filter_ids, 1), 0);
    
    IF p_userId > 0 AND filter_count > 0 THEN
        -- Get user tech filter IDs
        SELECT ARRAY(
            SELECT DISTINCT u.UserID 
            FROM unnest(active_user_filter_ids) AS uf(UserFilterId)
            JOIN dbo.aspnetusers u ON uf.UserFilterId = u.UserID
            JOIN dbo.fn_GetTechnicianTypes() tp ON u.CerebrumUserType = tp.Id
        ) INTO user_tech_filter_ids;
        
        -- Get user doctor filter IDs
        SELECT ARRAY(
            SELECT DISTINCT u.UserID 
            FROM unnest(active_user_filter_ids) AS uf(UserFilterId)
            JOIN dbo.aspnetusers u ON uf.UserFilterId = u.UserID
            JOIN dbo.practicedoctors pd ON u.Id = pd.ApplicationUserId
            WHERE u.CerebrumUserType = 5 -- doctor
            AND pd.ApplicationUserId IS NOT NULL
        ) INTO user_doctor_filter_ids;
        
        -- Main query with filters
        RETURN QUERY
        SELECT 
            su.PracticeId,
            COALESCE(pd.Id, 0) AS PracticeDoctorId,
            su.UserId,
            u.Id::VARCHAR AS ApplicationUserId,
            u.UserName::VARCHAR,
            u.FirstName::VARCHAR,
            u.LastName::VARCHAR,
            u.MiddleName::VARCHAR,
            u.CerebrumUserType,
            u.Status,
            su.startDate AS StartDate,
            su.endDate AS EndDate,
            su.updatedDate,
            su.permission::VARCHAR AS PermissionIds
        FROM dbo.scheduleusers su
        JOIN dbo.aspnetusers u ON su.UserId = u.UserId
        LEFT JOIN dbo.practicedoctors pd ON (u.Id = pd.ApplicationUserId AND pd.PracticeId = p_practiceId)
        WHERE su.PracticeId = p_practiceId 
        AND u.Status = 0
        AND u.PracticeID = su.PracticeId
        AND (su.endDate IS NULL OR su.endDate > CURRENT_TIMESTAMP)
        AND EXISTS (
            SELECT 1 FROM dbo.scheduleweekdays sw 
            WHERE su.Id = sw.ScheduleUserId
            AND sw.officeId = ANY(office_ids)
        )
        AND (
            u.UserId = ANY(user_doctor_filter_ids) OR
            u.UserId = ANY(user_tech_filter_ids) OR
            (u.UserId != ALL(COALESCE(user_doctor_filter_ids, ARRAY[]::INTEGER[])) 
             AND array_length(user_tech_filter_ids, 1) IS NULL 
             AND u.CerebrumUserType IN (SELECT Id FROM dbo.fn_GetTechnicianTypes()))
        );
    ELSE
        -- Main query without filters
        RETURN QUERY
        SELECT 
            su.PracticeId,
            COALESCE(pd.Id, 0) AS PracticeDoctorId,
            su.UserId,
            u.Id::VARCHAR AS ApplicationUserId,
            u.UserName::VARCHAR,
            u.FirstName::VARCHAR,
            u.LastName::VARCHAR,
            u.MiddleName::VARCHAR,
            u.CerebrumUserType,
            u.Status,
            su.startDate AS StartDate,
            su.endDate AS EndDate,
            su.updatedDate,
            su.permission::VARCHAR AS PermissionIds
        FROM dbo.scheduleusers su
        JOIN dbo.aspnetusers u ON su.UserId = u.UserId
        LEFT JOIN dbo.practicedoctors pd ON (u.Id = pd.ApplicationUserId AND pd.PracticeId = p_practiceId)
        WHERE su.PracticeId = p_practiceId 
        AND u.Status = 0
        AND u.PracticeID = su.PracticeId
        AND (su.endDate IS NULL OR su.endDate > CURRENT_TIMESTAMP)
        AND EXISTS (
            SELECT 1 FROM dbo.scheduleweekdays sw 
            WHERE su.Id = sw.ScheduleUserId
            AND sw.officeId = ANY(office_ids)
        );
    END IF;
END;
$$ LANGUAGE plpgsql;