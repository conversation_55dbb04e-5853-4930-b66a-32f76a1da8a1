-- =============================================
-- Author:        Migration from SQL Server
-- Create date:   PostgreSQL Migration
-- Description:   Get user offices - PostgreSQL equivalent of [dbo].[GetUserOffices]
-- =============================================

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

CREATE OR REPLACE FUNCTION dbo.GetUserOffices(p_userId INTEGER, p_practiceId INTEGER, p_isAdmin BOOLEAN)
RETURNS TABLE(
    PracticeId INTEGER,
    OfficeId BIGINT,
    OfficeName VARCHAR,
    BusinessName VARCHAR,
    OfficeUrl VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        o.PracticeId,
        o.Id AS OfficeId,
        o.name::VARCHAR AS OfficeName,
        o.businessName::VARCHAR AS BusinessName,
        COALESCE(
            (SELECT l.url FROM dbo.officeurls l 
             JOIN dbo.officeurltypes t ON t.Id = l.urlTypeId 
             WHERE l.officeId = o.Id AND t.urlType = 'FaxSentReport'
             LIMIT 1),
            ''
        )::VARCHAR AS OfficeUrl
    FROM dbo.office o
    JOIN dbo.useroffices uo ON o.Id = uo.OfficeId
    WHERE o.PracticeId = p_practiceId
    AND uo.ApplicationUserId = (
        SELECT Id FROM dbo.aspnetusers WHERE UserId = p_userId LIMIT 1
    );
END;
$$ LANGUAGE plpgsql;