-- PostgreSQL function equivalent of SQL Server GetScheduleAppointments stored procedure  
-- This function gets scheduled appointments for a date range with optional user filtering

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create GetScheduleAppointments function
CREATE OR REPLACE FUNCTION dbo.GetScheduleAppointments(
    p_office_id INTEGER,
    p_from_date TIMESTAMP,
    p_to_date TIMESTAMP,
    p_user_ids INTEGER[] DEFAULT ARRAY[]::INTEGER[]
)
RETURNS TABLE(
    appointmentid integer,
    appointmenttypeid integer,
    parenttypeid integer,
    appointmenttypename text,
    typename text,
    appointmentstatus integer,
    practicedoctorid integer,
    practicedoctor text,
    applicationuserid text,
    patientrecordid integer,
    patientfirstname text,
    patientlastname text,
    patientpreferredname text,
    appointmenttestid integer,
    appointmenttestresourceid integer,
    testid integer,
    testshortname text,
    testactive boolean,
    testresourceid integer,
    assignedtouserid integer,
    isdoctorrequiredinoffice boolean,
    appointmenttime timestamp,
    teststartime timestamp,
    testduration integer,
    permissionid integer,
    testcolor text
) AS $$
DECLARE
    v_appointment_status INTEGER := 1;
    v_is_active BOOLEAN := true;
    v_cancelled_status INTEGER := 7;
    v_triaged_status INTEGER := 16;
    v_has_user_filter BOOLEAN := false;
BEGIN
    -- Check if user filtering is needed
    v_has_user_filter := array_length(p_user_ids, 1) > 0 AND p_user_ids[1] > 0;

    RETURN QUERY
    WITH exclude_app_statuses AS (
        SELECT unnest(ARRAY[7, 1, 0, 16]) as appointment_status  -- cancelled, waitlist, cancellationlist, triage
    ),
    appointments_core AS (
        SELECT 
            ap.id as app_id,
            ap.appointmenttime,
            ap.appointmentstatus,
            ap.practicedoctorid,
            ap.appointmenttypeid,
            ap.patientrecordid,
            apt.id as app_test_id,
            apt.testid,
            apt.starttime,
            apt.testduration
        FROM dbo.appointments ap
        JOIN dbo.appointmenttests apt ON apt.appointmentid = ap.id
        WHERE ap.appointmentstatus NOT IN (SELECT appointment_status FROM exclude_app_statuses)
        AND ap.appointmenttime BETWEEN p_from_date AND p_to_date
        AND ap.officeid = p_office_id
        AND ap.isactive = v_is_active
        AND apt.isactive = v_is_active
        ORDER BY apt.appointmentid ASC, apt.testid ASC
    )
    SELECT 
        ap.app_id,
        ap.appointmenttypeid,
        typ.appointmenttypeid as parent_type_id,
        typ.name::text,
        aptyp.name::text,
        ap.appointmentstatus,
        ap.practicedoctorid,
        ('Dr.' || ext_docs.firstname || ' ' || ext_docs.lastname)::text,
        prac_docs.applicationuserid,
        ap.patientrecordid,
        demo.firstname::text,
        demo.lastname::text,
        COALESCE(demo.preferredname, '')::text,
        res.appointmenttestid,
        res.id,
        tst.id,
        tst.testshortname::text,
        tst.isactive,
        res.id,
        res.assignedtouserid,
        res.isdoctorrequiredinoffice,
        ap.appointmenttime AT TIME ZONE 'UTC',
        ap.starttime AT TIME ZONE 'UTC',
        ap.testduration,
        res.permissionid,
        tst.color::text
    FROM dbo.appointmenttestresources res
    JOIN appointments_core ap ON res.appointmenttestid = ap.app_test_id
    RIGHT JOIN dbo.appointmenttypes typ ON ap.appointmenttypeid = typ.id
    JOIN dbo.appointmenttypes aptyp ON typ.appointmenttypeid = aptyp.id
    JOIN dbo.practicedoctors prac_docs ON ap.practicedoctorid = prac_docs.id
    JOIN dbo.externaldoctors ext_docs ON prac_docs.externaldoctorid = ext_docs.id
    JOIN dbo.demographics demo ON ap.patientrecordid = demo.patientrecordid
    JOIN dbo.tests tst ON ap.testid = tst.id
    WHERE res.isactive = v_is_active
    AND (NOT v_has_user_filter OR res.assignedtouserid = ANY(p_user_ids))
    ORDER BY ap.starttime, ap.testid;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.GetScheduleAppointments TO postgres;