-- SP_Get_Patient_ImmunizationType procedure for PostgreSQL
-- Migrated from SQL Server stored procedure
-- Gets immunization information for a specific patient and immunization type

CREATE OR REPLACE FUNCTION dbo.SP_Get_Patient_ImmunizationType(
    p_patient_record_id integer,
    p_vp_cpp_immunization_type_id integer
)
RETURNS TABLE (
    id integer,
    patientrecordid integer,
    recallid integer,
    immunizationdate timestamp,
    immunizationday integer,
    immunizationmonth integer,
    immunizationyear integer,
    name text,
    datecreated timestamp,
    dateservicedday integer,
    dateservicedmonth integer,
    dateservicedyear integer,
    vp_cpp_immunizationstatusid integer,
    proceduretype text,
    proceduretypeid integer,
    status text
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        x.id,
        x.patientrecordid,
        r.id AS recallid,
        x.immunizationdate,
        x.immunizationday,
        x.immunizationmonth,
        x.immunizationyear,
        x.name,
        r.datecreated,
        r.dateservicedday,
        r.dateservicedmonth,
        r.dateser<PERSON><PERSON><PERSON>,
        r.vp_cpp_immunizationstatusid,
        t.name AS proceduretype,
        t.id AS proceduretypeid,
        s.status
    FROM vp_cpp_immunization x 
    JOIN immunizationrecalls r ON x.id = r.vp_cpp_immunization_id
    JOIN vp_cpp_immunizationtype t ON x.vp_cpp_immunizationtypeid = t.id
    JOIN vp_cpp_immunizationstatus s ON r.vp_cpp_immunizationstatusid = s.id
    WHERE r.active = true 
        AND x.patientrecordid = p_patient_record_id
        AND x.vp_cpp_immunizationtypeid = p_vp_cpp_immunization_type_id
        AND r.vp_cpp_immunizationstatusid NOT IN (1, 5, 6); -- not todbdone, overdue, deleted
END;
$$;