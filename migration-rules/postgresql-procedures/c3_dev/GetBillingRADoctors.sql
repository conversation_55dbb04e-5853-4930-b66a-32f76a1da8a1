-- PostgreSQL function for GetBillingRADoctors
-- Migrated from SQL Server stored procedure
-- Gets billing RA doctors by practice ID and billing numbers

CREATE OR REPLACE FUNCTION dbo.GetBillingRADoctors(
    p_practice_id INTEGER,
    p_billing_numbers TEXT
)
RETURNS TABLE(
    billingnumber text,
    firstname text,
    lastname text,
    externaldoctorid integer,
    practicedoctorid integer
) AS $$
BEGIN
    -- Check if billing numbers parameter is empty
    IF p_billing_numbers IS NULL OR TRIM(p_billing_numbers) = '' THEN
        RETURN;
    END IF;

    RETURN QUERY
    SELECT 
        a.OHIPPhysicianId AS billingNumber,
        a.firstName,
        a.lastName,
        b.ExternalDoctorId AS externalDoctorId,
        b.Id AS practiceDoctorId
    FROM dbo.ExternalDoctors a 
    INNER JOIN dbo.PracticeDoctors b ON a.Id = b.ExternalDoctorId
    WHERE b.PracticeId = p_practice_id 
        AND TRIM(a.OHIPPhysicianId) = ANY(string_to_array(p_billing_numbers, ','));
END;
$$ LANGUAGE plpgsql;