-- PostgreSQL function for GetSSRSReportByAppointmentTestId
-- Migrated from SQL Server stored procedure

CREATE OR REPLACE FUNCTION dbo.GetSSRSReportByAppointmentTestId(
    p_appointment_test_id INTEGER,
    p_appointment_test_log_id INTEGER DEFAULT 0
)
RETURNS TABLE(
    appointmentid integer,
    appointmenttime timestamp,
    patientid integer,
    practiceid integer,
    attachrawdata boolean,
    rawdatapages integer,
    genericreportid integer,
    genericheaderheight integer,
    officeid integer,
    officename varchar(100),
    officebusinessname varchar(500),
    officeaddress1 varchar(500),
    officeaddress2 varchar(500),
    officecity varchar(500),
    officephone varchar(500),
    officefax varchar(100),
    officecountry varchar(100),
    officezip varchar(100),
    officestate varchar(100),
    officeurl varchar(500),
    officeprovince integer,
    officepostalcode varchar(100),
    officestatus integer,
    officehrmid varchar(100),
    officedownloadurl varchar(500),
    officeuploadurl varchar(500),
    imagedownloadurl varchar(500),
    appointmenttestid integer,
    testtime timestamp,
    testid integer,
    testshortname varchar(500),
    testlongname varchar(500),
    testaccessionumber varchar(100),
    testhrmtypelong varchar(100),
    testhrmtypeshort varchar(100),
    testhrmodality varchar(100),
    testmodalities varchar(500),
    testgroupid integer,
    testgroupname varchar(100),
    isvp boolean,
    isamended boolean,
    demographicid integer,
    firstname varchar(100),
    lastname varchar(100),
    middlename varchar(100),
    dateofbirth timestamp,
    ageaccurate varchar(100),
    gender integer,
    healthcard varchar(100),
    healthcardcode varchar(100),
    addressline1 varchar(500),
    addressline2 varchar(500),
    city varchar(100),
    postalcode varchar(100),
    province varchar(100),
    country varchar(100),
    phonenumbers text,
    ssrsreportid integer,
    reportname varchar(500),
    reportssrsname varchar(500),
    reportdescription text,
    reportpath varchar(500),
    isactive boolean,
    datecreated timestamp,
    datelastmodified timestamp,
    loinccode varchar(30),
    hrm_upi varchar(30),
    reportqueuechangestatus boolean
) AS $$
DECLARE
    v_patient_id INTEGER;
    v_appointment_id INTEGER;
    v_test_id INTEGER;
    v_url_type_id_office INTEGER := 5; -- office download url type
    v_url_type_id_office_upload INTEGER := 6; -- office upload url type
    v_url_type_id_image INTEGER := 3; -- image download url type
    v_vp_test_id INTEGER := 29;
    v_completed_test_status_id INTEGER := 11;
    v_being_sent_test_status_id INTEGER := 18;
    v_is_amended_vp BOOLEAN := false;
    v_is_amended_ws BOOLEAN := false;
    v_clinic_send_type INTEGER := 6;
    v_save_type INTEGER := 3; -- send letter for vp
    v_completed_log_id INTEGER := 0; -- send letter for vp
    v_first_report_queue_id INTEGER := 0; -- for the first queue item for this test
    v_report_queue_change_status BOOLEAN := false; -- for report queue preliminary report
    v_report_queue_id INTEGER := 0; -- for the current queue item for this test
BEGIN
    -- Get basic appointment test info
    SELECT a.patientrecordid, a.id, t.testid 
    INTO v_patient_id, v_appointment_id, v_test_id
    FROM dbo.appointmenttests t 
    JOIN dbo.appointments a ON t.appointmentid = a.id 
    WHERE t.id = p_appointment_test_id
    LIMIT 1;

    -- VP - check if it's amended
    IF v_test_id = v_vp_test_id THEN
        SELECT COALESCE(sl.id, 0) INTO v_completed_log_id 
        FROM dbo.appointmenttestsavelogs sl 
        WHERE sl.appointmenttestid = p_appointment_test_id 
        AND sl.savetype = v_save_type 
        ORDER BY sl.logdate ASC 
        LIMIT 1;
        
        IF v_completed_log_id > 0 THEN
            IF p_appointment_test_log_id > 0 THEN
                IF v_completed_log_id != p_appointment_test_log_id THEN
                    v_is_amended_vp := true;
                END IF;
            ELSE
                v_is_amended_vp := true;
            END IF;
        END IF;
    ELSE
        -- When it's WS (worksheet)
        -- Check if any was in the report queue
        SELECT COALESCE(rq.id, 0) INTO v_first_report_queue_id 
        FROM dbo.reportqueues rq 
        WHERE rq.appointmenttestid = p_appointment_test_id 
        AND rq.changestatus = true 
        ORDER BY rq.datecreated ASC 
        LIMIT 1;

        IF p_appointment_test_log_id <= 0 THEN
            SELECT l.id INTO p_appointment_test_log_id 
            FROM dbo.appointmenttestlogs l 
            JOIN dbo.appointmenttests t ON l.appointmentid = t.appointmentid 
            WHERE t.id = p_appointment_test_id 
            AND l.status = 0 
            ORDER BY l.date DESC 
            LIMIT 1;
        END IF;
        
        SELECT COALESCE(rq.id, 0), COALESCE(rq.changestatus, false) 
        INTO v_report_queue_id, v_report_queue_change_status
        FROM dbo.reportqueues rq 
        WHERE rq.appointmenttestid = p_appointment_test_id 
        AND rq.appointmenttestlogid = p_appointment_test_log_id
        LIMIT 1;
        
        -- Check amended
        IF v_report_queue_change_status = true THEN
            IF v_report_queue_id > v_first_report_queue_id THEN
                v_is_amended_ws := true;
            END IF;
        END IF;
    END IF;

    -- Return the main result set
    RETURN QUERY
    SELECT 
        app.id::integer,
        app.appointmenttime AT TIME ZONE 'UTC',
        app.patientrecordid::integer,
        o.practiceid::integer,
        COALESCE(ptg.attachrawdata, false),
        COALESCE(ptg.rawdatapages, 0)::integer,
        COALESCE(ptg.genericreportid, 0)::integer,
        COALESCE(ptg.genericheaderheight, 0)::integer,
        o.id::integer,
        o.name::varchar(100),
        o.businessname::varchar(500),
        o.address1::varchar(500),
        o.address2::varchar(500),
        o.city::varchar(500),
        o.phone::varchar(500),
        o.fax::varchar(100),
        o.country::varchar(100),
        o.zip::varchar(100),
        o.state::varchar(100),
        o.url::varchar(500),
        COALESCE(o.province, 0)::integer,
        o.postalcode::varchar(100),
        COALESCE(o.status, 0)::integer,
        o.hrm_id::varchar(100),
        COALESCE((SELECT url FROM dbo.officeurls WHERE officeid = o.id AND urltypeid = v_url_type_id_office LIMIT 1), '')::varchar(500),
        COALESCE((SELECT url FROM dbo.officeurls WHERE officeid = o.id AND urltypeid = v_url_type_id_office_upload LIMIT 1), '')::varchar(500),
        COALESCE((SELECT url FROM dbo.officeurls WHERE officeid = o.id AND urltypeid = v_url_type_id_image LIMIT 1), '')::varchar(500),
        apptest.id::integer,
        apptest.starttime AT TIME ZONE 'UTC',
        apptest.testid::integer,
        t.testshortname::varchar(500),
        t.testfullname::varchar(500),
        apptest.accessionnumber::varchar(100),
        t.hrmtypelong::varchar(100),
        t.hrmtypeshort::varchar(100),
        t.hrmodality::varchar(100),
        (SELECT STRING_AGG(m.modalityname::text, ', ' ORDER BY m.modalityname)
         FROM dbo.testmodalities tm
         JOIN dbo.modalities m ON tm.modalityid = m.id
         WHERE tm.testid = apptest.testid)::varchar(500),
        g.id::integer,
        g.name::varchar(100),
        CASE WHEN apptest.testid = v_vp_test_id THEN true ELSE false END,
        CASE 
            WHEN apptest.testid = v_vp_test_id THEN v_is_amended_vp
            ELSE v_is_amended_ws 
        END,
        demo.demographicid::integer,
        demo.firstname::varchar(100),
        demo.lastname::varchar(100),
        demo.middlename::varchar(100),
        demo.dateofbirth AT TIME ZONE 'UTC',
        demo.ageaccurate::varchar(100),
        demo.gender::integer,
        demo.healthcard::varchar(100),
        demo.healthcardcode::varchar(100),
        demo.addressline1::varchar(500),
        demo.addressline2::varchar(500),
        demo.city::varchar(100),
        demo.postalcode::varchar(100),
        demo.province::varchar(100),
        demo.country::varchar(100),
        demo.phonenumbers,
        sr.id::integer,
        sr.reportname::varchar(500),
        sr.reportssrsname::varchar(500),
        sr.reportdescription,
        sr.reportpath::varchar(500),
        sr.isactive,
        sr.datecreated AT TIME ZONE 'UTC',
        sr.datelastmodified AT TIME ZONE 'UTC',
        t.loinccode::varchar(30),
        o.hrm_upi::varchar(30),
        v_report_queue_change_status
    FROM dbo.appointmenttests apptest
    JOIN dbo.appointments app ON apptest.appointmentid = app.id
    JOIN dbo.office o ON app.officeid = o.id	
    JOIN dbo.tests t ON apptest.testid = t.id
    JOIN dbo.testgroups tg ON apptest.testid = tg.testid
    JOIN dbo.groups g ON g.id = tg.groupid	
    LEFT JOIN dbo.practicetestgroups ptg ON g.id = ptg.groupid AND o.practiceid = ptg.practiceid
    JOIN dbo.ssrsreports sr ON sr.id = CASE WHEN COALESCE(ptg.ssrsreportid, 0) > 0 THEN ptg.ssrsreportid ELSE g.ssrsreportid END
    JOIN dbo.fn_getpatientinfo(v_patient_id, null) demo ON app.patientrecordid = demo.patientid
    WHERE apptest.id = p_appointment_test_id;

END;
$$ LANGUAGE plpgsql;