-- Migration: GetMainDoctorInfo stored procedure to PostgreSQL
-- Purpose: Get main doctor information for a patient including enrollment details
-- Migrated from: SQL Server stored procedure [dbo].[GetMainDoctorInfo]

CREATE OR REPLACE FUNCTION dbo.GetMainDoctorInfo(
    p_patient_record_id INTEGER
) 
RETURNS TABLE (
    mainDoctorId INTEGER,
    docFullName TEXT,
    EnrolledDocName TEXT
) AS $$
DECLARE
    v_min_date DATE := DATE '0001-01-01';
    v_max_date DATE := DATE '9999-01-01';
BEGIN
    RETURN QUERY
    SELECT
        dmm.PracticeDoctorId::INTEGER as mainDoctorId,
        CONCAT(COALESCE(ex.lastName, ''), ', ', COALESCE(ex.firstName, '')) as docFullName,
        -- Subquery for enrolled doctor name
        (SELECT CONCAT(COALESCE(extDoc.lastName, ''), ', ', COALESCE(extDoc.firstName, ''))
         FROM dbo.demographicsenrollments as enr  
         INNER JOIN dbo.demographicsmainresponsiblephysicians as mainDoc ON enr.DemographicsMRPId = mainDoc.Id
         INNER JOIN dbo.externaldoctors as extDoc ON mainDoc.ExternalDoctorId = extDoc.Id
         WHERE enr.DemographicsMRPId = p_patient_record_id 
             AND (enr.enrollmentTerminationDate IS NOT NULL 
                  AND enr.enrollmentTerminationDate > v_min_date 
                  AND enr.enrollmentTerminationDate < v_max_date)
         ORDER BY enr.Id DESC
         LIMIT 1) as EnrolledDocName
    FROM dbo.demographics as d
    INNER JOIN dbo.patientrecords as pr ON d.PatientRecordId = pr.Id
    INNER JOIN dbo.demographicsmainresponsiblephysicians as dmm ON d.id = dmm.DemographicId
    INNER JOIN dbo.externaldoctors as ex ON dmm.ExternalDoctorId = ex.Id
    WHERE d.PatientRecordId = p_patient_record_id 
        AND dmm.IsActive = true
    ORDER BY dmm.PracticeDoctorId DESC;
END;
$$ LANGUAGE plpgsql;