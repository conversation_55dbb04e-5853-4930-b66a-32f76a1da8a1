-- PostgreSQL function for Get_VP_DoctorOptions stored procedure
-- Migrated from SQL Server procedure Get_VP_DoctorOptions

CREATE OR REPLACE FUNCTION dbo.Get_VP_DoctorOptions(
    p_doctor_id INTEGER
)
RETURNS TABLE(
    id integer,
    patientrecordid integer
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        vbo.OptionID::integer as id, 
        vbo.PatientRecordId::integer as patientrecordid
    FROM dbo.VPOptionByPatients vbo
    WHERE vbo.PatientRecordId = p_doctor_id;
END;
$$ LANGUAGE plpgsql;