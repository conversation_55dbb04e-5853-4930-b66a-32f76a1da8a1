-- PostgreSQL function for usp_GetRadStudyC3
-- Migrated from SQL Server stored procedure
-- Gets radiology study information with flexible search criteria

CREATE OR REPLACE FUNCTION dbo.usp_GetRadStudyC3(
    p_accession_number VARCHAR(50) DEFAULT NULL,
    p_test_date TIMESTAMP DEFAULT NULL,
    p_patient_record_id INTEGER DEFAULT NULL,
    p_patient_dob TIMESTAMP DEFAULT NULL
)
RETURNS TABLE(
    accessionnumber VARCHAR(50),
    firstname VARCHAR(100),
    lastname VARCHAR(100),
    dateofbirth VARCHAR(50),
    starttime VARCHAR(50)
) AS $$
DECLARE
    where_clause TEXT := '';
    init_dt TEXT;
    end_dt TEXT;
BEGIN
    -- Build WHERE clause dynamically based on provided parameters
    
    -- Patient Record ID filter
    IF p_patient_record_id IS NOT NULL AND p_patient_record_id > 0 THEN
        where_clause := where_clause || ' AND ap.PatientRecordId = ' || p_patient_record_id::TEXT;
    END IF;
    
    -- Accession Number filter
    IF p_accession_number IS NOT NULL AND TRIM(COALESCE(p_accession_number, '')) <> '' THEN
        where_clause := where_clause || ' AND apt.accessionnumber = ' || QUOTE_LITERAL(p_accession_number);
    END IF;
    
    -- Test Date filter (full day range)
    IF p_test_date IS NOT NULL THEN
        init_dt := TO_CHAR(p_test_date, 'YYYY-MM-DD') || ' 00:00:00';
        end_dt := TO_CHAR(p_test_date, 'YYYY-MM-DD') || ' 23:59:59';
        where_clause := where_clause || ' AND apt.StartTime BETWEEN ' || 
                       QUOTE_LITERAL(init_dt) || ' AND ' || QUOTE_LITERAL(end_dt);
    END IF;
    
    -- Patient Date of Birth filter
    IF p_patient_dob IS NOT NULL THEN
        where_clause := where_clause || ' AND d.dateOfBirth = ' || 
                       QUOTE_LITERAL(TO_CHAR(p_patient_dob, 'YYYY-MM-DD HH24:MI:SS'));
    END IF;
    
    -- Execute the dynamic query using RETURN QUERY EXECUTE
    RETURN QUERY EXECUTE '
        SELECT 
            COALESCE(apt.accessionnumber, '''') as accessionnumber,
            d.firstname,
            d.lastname,
            COALESCE(TO_CHAR(d.dateofbirth, ''YYYY-MM-DD HH24:MI:SS''), '''') as dateofbirth,
            COALESCE(TO_CHAR(apt.starttime, ''YYYY-MM-DD HH24:MI:SS''), '''') as starttime
        FROM dbo.AppointmentTests apt
        INNER JOIN dbo.Appointments ap ON ap.Id = apt.AppointmentId
        INNER JOIN dbo.Demographics d ON ap.PatientRecordId = d.PatientRecordId
        WHERE 1=1 ' || where_clause;
        
END;
$$ LANGUAGE plpgsql;