-- PostgreSQL function for Get_VP_Summary
-- Migrated from SQL Server stored procedure
-- Returns Virtual Visit appointment summary information including doctor, billing, and diagnostic codes

CREATE OR REPLACE FUNCTION dbo.Get_VP_Summary(
    p_practice_id INTEGER,
    p_appointment_id INTEGER,
    p_username VARCHAR(512)
)
RETURNS TABLE(
    MainDoctorID INTEGER,
    PracticeDoctorID INTEGER,
    UserID INTEGER,
    PatientID INTEGER,
    OfficeID INTEGER,
    SpecialtyCodes INTEGER,
    ReportTemplate INTEGER,
    AppointmentDate TIMESTAMP,
    TestID INTEGER,
    IsImported INTEGER,
    ConsultCode INTEGER,
    ConsultCode2 INTEGER,
    ConsultCode3 INTEGER,
    ConsultCodeCode VARCHAR(200),
    ConsultCodeCode2 VARCHAR(200),
    ConsultCodeCode3 VARCHAR(200),
    DignosisCode INTEGER,
    DignosisCode2 INTEGER,
    DignosisCode3 INTEGER,
    DCCode VARCHAR(1024),
    DCDiagnosis VARCHAR(2048),
    DCCode2 VARCHAR(1024),
    DCDiagnosis2 VARCHAR(2048),
    DCCode3 VARCHAR(1024),
    DCDiagnosis3 VARCHAR(2048)
) AS $$
DECLARE
    v_main_doctor_id INTEGER := 0;
    v_practice_doctor_id INTEGER := 0;
    v_user_id INTEGER := 0;
    v_patient_id INTEGER := 0;
    v_office_id INTEGER := 0;
    v_specialty_codes INTEGER := 0;
    v_report_template INTEGER := 0;
    v_test_id INTEGER := 0;
    v_is_imported INTEGER := 0;
    v_bill_status_id INTEGER := 0;
    v_consult_code INTEGER := 0;
    v_consult_code2 INTEGER := 0;
    v_consult_code3 INTEGER := 0;
    v_consult_code_code VARCHAR(200);
    v_consult_code_code2 VARCHAR(200);
    v_consult_code_code3 VARCHAR(200);
    v_diagnosis_code INTEGER := 0;
    v_diagnosis_code2 INTEGER := 0;
    v_diagnosis_code3 INTEGER := 0;
    v_dc_code VARCHAR(1024);
    v_dc_diagnosis VARCHAR(2048);
    v_dc_code2 VARCHAR(1024);
    v_dc_diagnosis2 VARCHAR(2048);
    v_dc_code3 VARCHAR(1024);
    v_dc_diagnosis3 VARCHAR(2048);
    v_appointment_date TIMESTAMP;
BEGIN
    -- Get basic appointment and patient information
    SELECT 
        a.PatientRecordId,
        a.OfficeId,
        a.appointmentTime,
        0 -- IsImported
    INTO 
        v_patient_id,
        v_office_id,
        v_appointment_date,
        v_is_imported
    FROM 
        Appointments a
        JOIN PatientRecords pr ON a.PatientRecordId = pr.Id
    WHERE 
        pr.PracticeId = p_practice_id 
        AND a.Id = p_appointment_id;

    -- Get user ID from username
    SELECT u.UserID
    INTO v_user_id
    FROM aspnetusers u 
    WHERE u.UserName = p_username;

    -- Get main doctor and report template information
    SELECT 
        e.Id,
        COALESCE(e.ReportTemplate, 0),
        P.Id
    INTO 
        v_main_doctor_id,
        v_report_template,
        v_practice_doctor_id
    FROM 
        ExternalDoctors e 
        JOIN PracticeDoctors P ON e.Id = p.ExternalDoctorId
    WHERE
        p.Id = (SELECT PracticeDoctorId FROM Appointments WHERE Id = p_appointment_id);

    -- Get specialty codes
    SELECT p.specialtyCodes
    INTO v_specialty_codes
    FROM
        Appointments a 
        JOIN PracticeDoctors p ON a.PracticeDoctorId = p.Id
    WHERE 
        p.PracticeId = p_practice_id 
        AND a.Id = p_appointment_id;

    -- Get test ID for VP
    SELECT T.Id
    INTO v_test_id
    FROM Tests t 
    WHERE T.testShortName = 'VP';

    -- Get billing information
    SELECT 
        COALESCE(b.ConsultCode, 0),
        COALESCE(b.ConsultCode2, 0),
        COALESCE(b.ConsultCode3, 0),
        COALESCE(b.DiagnosticCode, 0),
        COALESCE(b.DiagnosticCode2, 0),
        COALESCE(b.DiagnosticCode3, 0),
        COALESCE(b.billStatusId, 0)
    INTO 
        v_consult_code,
        v_consult_code2,
        v_consult_code3,
        v_diagnosis_code,
        v_diagnosis_code2,
        v_diagnosis_code3,
        v_bill_status_id
    FROM AppointmentBills b 
    WHERE b.AppointmentID = p_appointment_id;

    -- Get consult code details
    SELECT C.Code INTO v_consult_code_code
    FROM ConsultCodes C WHERE C.Id = v_consult_code;

    SELECT C.Code INTO v_consult_code_code2
    FROM ConsultCodes C WHERE C.Id = v_consult_code2;

    SELECT C.Code INTO v_consult_code_code3
    FROM ConsultCodes C WHERE C.Id = v_consult_code3;

    -- Get diagnosis code details
    SELECT D.Code, D.Diagnosis 
    INTO v_dc_code, v_dc_diagnosis
    FROM DiagnoseCodes d WHERE D.Id = v_diagnosis_code;

    SELECT D.Code, D.Diagnosis 
    INTO v_dc_code2, v_dc_diagnosis2
    FROM DiagnoseCodes d WHERE D.Id = v_diagnosis_code2;

    SELECT D.Code, D.Diagnosis 
    INTO v_dc_code3, v_dc_diagnosis3
    FROM DiagnoseCodes d WHERE D.Id = v_diagnosis_code3;

    -- Return the summary result
    RETURN QUERY SELECT 
        COALESCE(v_main_doctor_id, 0) AS MainDoctorID,
        COALESCE(v_practice_doctor_id, 0) AS PracticeDoctorID,
        COALESCE(v_user_id, 0) AS UserID,
        v_patient_id AS PatientID,
        v_office_id AS OfficeID,
        COALESCE(v_specialty_codes, 0) AS SpecialtyCodes,
        COALESCE(v_report_template, 0) AS ReportTemplate,
        v_appointment_date AS AppointmentDate,
        COALESCE(v_test_id, 0) AS TestID,
        v_is_imported AS IsImported,
        v_consult_code AS ConsultCode,
        v_consult_code2 AS ConsultCode2,
        v_consult_code3 AS ConsultCode3,
        v_consult_code_code AS ConsultCodeCode,
        v_consult_code_code2 AS ConsultCodeCode2,
        v_consult_code_code3 AS ConsultCodeCode3,
        v_diagnosis_code AS DignosisCode,
        v_diagnosis_code2 AS DignosisCode2,
        v_diagnosis_code3 AS DignosisCode3,
        v_dc_code AS DCCode,
        v_dc_diagnosis AS DCDiagnosis,
        v_dc_code2 AS DCCode2,
        v_dc_diagnosis2 AS DCDiagnosis2,
        v_dc_code3 AS DCCode3,
        v_dc_diagnosis3 AS DCDiagnosis3;
END;
$$ LANGUAGE plpgsql;