-- PostgreSQL function migration of fn_GetBillStatusId
-- Original: fn_GetBillStatusId(@billStatusId_NotBilled int, @billStatusId_Mixed int, @BillStatusIdTable BillStatusIdTableType READONLY) RETURNS int
-- Purpose: Determines consolidated billing status from a collection of bill status IDs
-- Modified to work with PostgreSQL arrays instead of table-valued parameters

CREATE OR REPLACE FUNCTION dbo.fn_GetBillStatusId(
    p_bill_status_id_not_billed INTEGER,
    p_bill_status_id_mixed INTEGER,
    p_bill_status_ids INTEGER[]
)
RETURNS TABLE(
    bill_status_id INTEGER
) AS $$
DECLARE
    v_bill_status_id INTEGER := -1;
    v_current_bill_status_id INTEGER;
    v_bill_status_id_item INTEGER;
BEGIN
    -- Process each bill status ID in the array
    FOREACH v_bill_status_id_item IN ARRAY p_bill_status_ids
    LOOP
        -- Handle null values by setting to not billed status
        IF v_bill_status_id_item IS NULL THEN
            v_current_bill_status_id := p_bill_status_id_not_billed;
        ELSE
            v_current_bill_status_id := v_bill_status_id_item;
        END IF;
        
        -- Initialize with first status
        IF v_bill_status_id = -1 THEN
            v_bill_status_id := v_current_bill_status_id;
        ELSE
            -- If statuses differ, set to mixed status
            IF v_bill_status_id != v_current_bill_status_id THEN
                v_bill_status_id := p_bill_status_id_mixed;
            END IF;
        END IF;
    END LOOP;
    
    -- If no statuses were processed, default to not billed
    IF v_bill_status_id = -1 THEN
        v_bill_status_id := p_bill_status_id_not_billed;
    END IF;
    
    -- Return as table
    RETURN QUERY SELECT v_bill_status_id;
END;
$$ LANGUAGE plpgsql;