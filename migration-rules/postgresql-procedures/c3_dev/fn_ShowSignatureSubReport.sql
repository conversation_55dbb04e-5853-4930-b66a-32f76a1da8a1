-- PostgreSQL function migration of fn_ShowSignatureSubReport
-- Original: fn_ShowSignatureSubReport(@OfficeName varchar(30), @testShortName nvarchar(30)) RETURNS int
-- Purpose: Determines whether to show signature subreport based on office and test type
-- Modified to return TABLE for repository pattern compatibility

CREATE OR REPLACE FUNCTION dbo.fn_ShowSignatureSubReport(
    p_office_name VARCHAR(30),
    p_test_short_name VARCHAR(30)
)
RETURNS TABLE(
    show_signature INTEGER
) AS $$
DECLARE
    v_result INTEGER;
BEGIN
    -- Return 0 if office is 'CPS' and test is 'BP', otherwise return 1
    v_result := CASE 
        WHEN p_office_name = 'CPS' AND p_test_short_name = 'BP' THEN 0 
        ELSE 1 
    END;
    
    -- Return as table
    RETURN QUERY SELECT v_result;
END;
$$ LANGUAGE plpgsql;