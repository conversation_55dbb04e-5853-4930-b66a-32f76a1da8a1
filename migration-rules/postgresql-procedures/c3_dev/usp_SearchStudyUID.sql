-- PostgreSQL function for usp_SearchStudyUID
-- Migrated from SQL Server stored procedure
-- Searches for study by <PERSON><PERSON><PERSON> using hash-based lookup

CREATE OR REPLACE FUNCTION dbo.usp_SearchStudyUID(
    p_study_uid VARCHAR(250)
)
RETURNS TABLE(
    id INTEGER,
    studyuid VARCHAR(250),
    studydate TIMESTAMP,
    accessionnumber VARCHAR(100),
    studydescription VARCHAR(500),
    studystatus VARCHAR(50),
    modality VARCHAR(50),
    patientid INTEGER,
    refphysicianid INTEGER,
    studyid INTEGER,
    institution INTEGER,
    dateadded TIMESTAMP,
    imagecount INTEGER,
    srcount INTEGER,
    createddatetime TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id,
        s.StudyUID as studyuid,
        s.StudyDate as studydate,
        s.AccessionNum as accessionnumber,
        s.StudyDescription as studydescription,
        s.StudyStatus as studystatus,
        s.Modality as modality,
        s.PatientId as patientid,
        s.RefPhysicianId as refphysicianid,
        s.StudyId as studyid,
        s.Institution as institution,
        s.DateAdded as dateadded,
        s.ImageCount as imagecount,
        s.SRCount as srcount,
        s.CreatedDateTime as createddatetime
    FROM dbo.RAD_Study s
    WHERE s.StudyUIDHash = MD5(p_study_uid)::UUID::TEXT
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;