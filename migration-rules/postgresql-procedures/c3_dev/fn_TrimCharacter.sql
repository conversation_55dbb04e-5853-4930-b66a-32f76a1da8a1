-- PostgreSQL function migration of fn_<PERSON><PERSON><PERSON><PERSON>cter
-- Original: fn_<PERSON><PERSON><PERSON><PERSON><PERSON>(@Value NVARCHAR(4000), @CharacterToTrim NVARCHAR(1)) RETURNS NVARCHAR(4000)
-- Purpose: Trims specified character from beginning and end of string value
-- Modified to return TABLE for repository pattern compatibility

CREATE OR REPLACE FUNCTION dbo.fn_TrimCharacter(
    p_value TEXT,
    p_character_to_trim VARCHAR(1)
)
RETURNS TABLE(
    trimmed_value TEXT
) AS $$
DECLARE
    v_result TEXT;
BEGIN
    -- Start with standard trim of whitespace
    v_result := TRIM(p_value);
    
    -- Trim specified character from beginning and end
    -- PostgreSQL TRIM function can trim specific characters
    IF p_character_to_trim IS NOT NULL AND p_character_to_trim != '' THEN
        v_result := TRIM(BOTH p_character_to_trim FROM v_result);
    END IF;
    
    -- Return as table
    RETURN QUERY SELECT v_result;
END;
$$ LANGUAGE plpgsql;