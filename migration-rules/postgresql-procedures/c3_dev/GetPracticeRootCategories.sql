-- PostgreSQL function for GetPracticeRootCategories
-- Migrated from SQL Server stored procedure
-- Gets practice root categories by group, practice, and template

CREATE OR REPLACE FUNCTION dbo.GetPracticeRootCategories(
    p_group_id INTEGER,
    p_practice_id INTEGER,
    p_practice_template_id INTEGER,
    p_root_category_id INTEGER DEFAULT 0
)
RETURNS TABLE(
    practicerootcategoryid integer,
    categoryname varchar,
    displayorder integer,
    rootcategoryid integer,
    groupid integer,
    practiceid integer,
    pracrootcategorytempid integer,
    templateid integer,
    templatename varchar,
    isactive boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        1000000 AS PracticeRootCategoryId,
        rc.CategoryName,
        1000000 AS DisplayOrder,
        rct.RootCategoryId,
        rc.GroupId,
        pt.PracticeId,
        pt.Id AS PracRootCategoryTempId,
        rt.Id AS TemplateId,
        rt.TemplateName,
        rc.IsActive
    FROM dbo.RootCategoryTemplates rct
    JOIN dbo.RootTemplates rt ON rct.RootTemplateId = rt.Id
    JOIN dbo.PracticeRootCategoryTemplates pt ON rct.RootTemplateId = pt.TemplateId
    JOIN dbo.RootCategories rc ON rct.RootCategoryId = rc.Id
    WHERE rc.GroupId = p_group_id
        AND rt.GroupId = p_group_id
        AND pt.Id = p_practice_template_id
        AND pt.PracticeId = p_practice_id
        AND pt.IsActive = true
        AND rc.Id = CASE 
            WHEN p_root_category_id > 0 THEN p_root_category_id 
            ELSE rct.RootCategoryId 
        END
    ORDER BY rc.CategoryName;
END;
$$ LANGUAGE plpgsql;