-- Migration: GetAppointmentTestSavedLogs stored procedure to PostgreSQL
-- Purpose: Get appointment test save logs with user and template information
-- Migrated from: SQL Server stored procedure [dbo].[GetAppointmentTestSavedLogs]

CREATE OR REPLACE FUNCTION dbo.GetAppointmentTestSavedLogs(
    p_appointment_test_id INTEGER
) 
RETURNS TABLE (
    Id INTEGER,
    AppointmentTestId INTEGER,
    LogDate TIMESTAMP,
    <PERSON><PERSON><PERSON>dd<PERSON> VARCHAR(50),
    SavedByUserId INTEGER,
    SaveType INTEGER,
    PracticeTemplateId INTEGER,
    TemplateName VARCHAR(200),
    UserName VARCHAR(256),
    FirstName VARCHAR(256),
    LastName VARCHAR(256)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sl.Id::INTEGER,
        sl.AppointmentTestId::INTEGER,
        sl.LogDate,
        COALESCE(sl.IpAddress, '') as Ip<PERSON>dd<PERSON>,
        sl.SavedByUserId::INTEGER,
        sl.SaveType::INTEGER,
        sl.PracRootCategoryTempId::INTEGER as PracticeTemplateId,
        COALESCE(rt.TemplateName, '') as TemplateName,
        COALESCE(u.UserName, '') as UserName,
        COALESCE(u.FirstName, '') as FirstName,
        COALESCE(u.LastName, '') as LastName
    FROM AppointmentTestSaveLogs sl
    INNER JOIN PracticeRootCategoryTemplates pt ON sl.PracRootCategoryTempId = pt.Id
    INNER JOIN RootTemplates rt ON rt.Id = pt.TemplateId
    LEFT JOIN AspNetUsers u ON sl.SavedByUserId = u.UserID
    WHERE sl.AppointmentTestId = p_appointment_test_id
    ORDER BY sl.LogDate DESC;
END;
$$ LANGUAGE plpgsql;