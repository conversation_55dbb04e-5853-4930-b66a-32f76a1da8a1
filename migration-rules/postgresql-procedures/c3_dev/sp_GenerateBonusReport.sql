CREATE OR REPLACE FUNCTION dbo.sp_GenerateBonusReport(
    p_doctor_id INTEGER,
    p_fiscal_year INTEGER,
    p_immunization_type_id INTEGER
)
RETURNS TABLE(
    patientrecordid INTEGER,
    firstname TEXT,
    lastname TEXT,
    healthcardnum TEXT,
    dateofbirth TIMESTAMP,
    gender INTEGER,
    enrollmentdate TIMESTAMP,
    age INTEGER,
    lastprocdate TIMESTAMP,
    bonusstatus TEXT,
    bonuscalculation DECIMAL(18,2)
) AS $$
DECLARE
    v_age INTEGER;
    v_gender INTEGER;
    v_operator INTEGER;
    v_age_from INTEGER;
    v_age_to INTEGER;
    v_age_as_of_date TIMESTAMP;
    v_age_category TEXT;
    v_frequency INTEGER;
    v_patient_record_id INTEGER;
    v_date_cuttoff_service TIMESTAMP;
    v_eligible SMALLINT;
    v_num_of_patients DECIMAL(18,2);
    v_num_covered_patients DECIMAL(18,2);
    v_num_ineligible DECIMAL(18,2);
    v_calculated_cuttoff TIMESTAMP;
    v_result DECIMAL(18,2);
    patient_record RECORD;
BEGIN
    -- Get immunization type parameters
    SELECT t.AgeFrom, t.AgeTo, t.DateFrom, t.DateTo, t.Operator, t.Gender, t.Period, t.agecategory
    INTO v_age_from, v_age_to, v_age_as_of_date, v_date_cuttoff_service, v_operator, v_gender, v_frequency, v_age_category
    FROM dbo.VP_CPP_ImmunizationType t
    WHERE t.Id = p_immunization_type_id;

    -- Hardcoded for Influenza (due seasonal requirement)
    IF p_immunization_type_id = 2 THEN
        v_frequency := 4; -- 4 months
    END IF;

    -- Calculate cutoff date
    v_calculated_cuttoff := TO_TIMESTAMP(
        p_fiscal_year::TEXT || LPAD(EXTRACT(MONTH FROM v_date_cuttoff_service)::TEXT, 2, '0') || 
        LPAD(EXTRACT(DAY FROM v_date_cuttoff_service)::TEXT, 2, '0'), 'YYYYMMDD'
    );

    -- Initialize counters
    v_num_covered_patients := 0;
    v_num_of_patients := 0;
    v_num_ineligible := 0;

    -- Create temporary table (using CTE approach in PostgreSQL)
    IF v_age_category = 'Routine Infants & Children' THEN
        -- For routine infants & children, calculate age in months at last procedure date
        RETURN QUERY
        WITH target_population AS (
            SELECT 
                d.patientrecordid,
                d.firstname,
                d.lastname,
                dhc.number AS healthcardnum,
                d.dateofbirth,
                d.gender,
                de.enrollmentdate,
                NULL::INTEGER AS age,
                EXTRACT(MONTH FROM AGE(lastproc.lastprocdate, d.dateofbirth))::INTEGER AS age_lpd,
                lastproc.lastprocdate,
                CASE 
                    WHEN EXTRACT(MONTH FROM AGE(lastproc.lastprocdate, d.dateofbirth)) < 30 THEN 'Y' 
                    ELSE 'N' 
                END AS bonusstatus,
                NULL::DECIMAL(18,2) AS bonuscalculation
            FROM dbo.Demographics d 
            JOIN dbo.DemographicsMainResponsiblePhysicians mrp ON d.Id = mrp.DemographicId
            JOIN dbo.DemographicsEnrollments de ON de.DemographicsMRPId = mrp.Id
            LEFT JOIN dbo.DemographicsHealthCards dhc ON dhc.DemographicId = d.id
            LEFT JOIN (
                SELECT 
                    i.patientrecordid,
                    MAX(ir.datecreated) AS lastprocdate
                FROM dbo.VP_CPP_Immunization i 
                JOIN dbo.ImmunizationRecalls ir ON ir.VP_CPP_Immunization_ID = i.ID 
                WHERE ir.vp_cpp_immunizationstatusid IN (3,4,7)
                AND i.VP_CPP_ImmunizationTypeId = p_immunization_type_id
                AND ir.active = true
                GROUP BY i.patientrecordid
            ) lastproc ON lastproc.patientrecordid = d.patientrecordid
            WHERE mrp.PracticeDoctorId = p_doctor_id
            AND d.active = false  -- Means TRUE in this system
            AND d.gender = CASE WHEN v_gender = 2 THEN d.gender ELSE v_gender END
            AND COALESCE(de.enrollmentdate, v_calculated_cuttoff) <= v_calculated_cuttoff
            AND COALESCE(de.enrollmentterminationdate, v_calculated_cuttoff + INTERVAL '1 day') > v_calculated_cuttoff
            AND EXTRACT(MONTH FROM AGE(v_calculated_cuttoff, d.dateofbirth)) >= v_age_from 
            AND EXTRACT(MONTH FROM AGE(v_calculated_cuttoff, d.dateofbirth)) <= v_age_to
        ),
        stats AS (
            SELECT 
                COUNT(*) AS total_patients,
                SUM(CASE WHEN age_lpd < 30 THEN 1 ELSE 0 END) AS covered_patients
            FROM target_population
        ),
        final_result AS (
            SELECT 
                tp.*,
                CASE 
                    WHEN s.total_patients > 0 THEN 
                        (s.covered_patients::DECIMAL / s.total_patients::DECIMAL) * 100 
                    ELSE 0 
                END AS final_bonus_calculation
            FROM target_population tp, stats s
        )
        SELECT 
            fr.patientrecordid,
            fr.firstname,
            fr.lastname,
            fr.healthcardnum,
            fr.dateofbirth,
            fr.gender,
            fr.enrollmentdate,
            fr.age_lpd AS age,
            fr.lastprocdate,
            fr.bonusstatus,
            fr.final_bonus_calculation AS bonuscalculation
        FROM final_result fr
        ORDER BY fr.lastname;

    ELSE
        -- For other age categories, use standard age calculation
        RETURN QUERY
        WITH target_population AS (
            SELECT 
                d.patientrecordid,
                d.firstname,
                d.lastname,
                dhc.number AS healthcardnum,
                d.dateofbirth,
                d.gender,
                de.enrollmentdate,
                EXTRACT(YEAR FROM AGE(v_age_as_of_date, d.dateofbirth))::INTEGER AS age,
                lastproc.lastprocdate,
                CASE 
                    WHEN eligible_status.is_eligible = 1 THEN 'Y'
                    WHEN eligible_status.is_eligible = 0 THEN 'I'
                    ELSE 'N'
                END AS bonusstatus
            FROM dbo.Demographics d 
            JOIN dbo.DemographicsMainResponsiblePhysicians mrp ON d.Id = mrp.DemographicId
            JOIN dbo.DemographicsEnrollments de ON de.DemographicsMRPId = mrp.Id
            LEFT JOIN dbo.DemographicsHealthCards dhc ON dhc.DemographicId = d.id
            LEFT JOIN (
                SELECT 
                    i.patientrecordid,
                    MAX(ir.datecreated) AS lastprocdate
                FROM dbo.VP_CPP_Immunization i 
                JOIN dbo.ImmunizationRecalls ir ON ir.VP_CPP_Immunization_ID = i.ID 
                WHERE ir.vp_cpp_immunizationstatusid IN (3,4,7)
                AND i.VP_CPP_ImmunizationTypeId = p_immunization_type_id
                AND ir.active = true
                GROUP BY i.patientrecordid
            ) lastproc ON lastproc.patientrecordid = d.patientrecordid
            LEFT JOIN (
                SELECT 
                    i.patientrecordid,
                    CASE 
                        WHEN MIN(CASE WHEN ir.vp_cpp_immunizationstatusid = 4 THEN 0 ELSE 1 END) = 0 THEN 0
                        WHEN COUNT(CASE WHEN ir.vp_cpp_immunizationstatusid IN (3,7) 
                                          AND ir.DateCreated >= (v_calculated_cuttoff - INTERVAL '1 month' * v_frequency)
                                          AND ir.DateCreated <= v_calculated_cuttoff THEN 1 END) > 0 THEN 1
                        ELSE NULL
                    END AS is_eligible
                FROM dbo.VP_CPP_Immunization i 
                JOIN dbo.ImmunizationRecalls ir ON ir.VP_CPP_Immunization_ID = i.ID 
                WHERE i.VP_CPP_ImmunizationTypeId = p_immunization_type_id
                AND ir.active = true
                GROUP BY i.patientrecordid
            ) eligible_status ON eligible_status.patientrecordid = d.patientrecordid
            WHERE mrp.PracticeDoctorId = p_doctor_id
            AND d.active = false  -- Means TRUE in this system
            AND d.gender = CASE WHEN v_gender = 2 THEN d.gender ELSE v_gender END
            AND COALESCE(de.enrollmentdate, v_calculated_cuttoff) <= v_calculated_cuttoff
            AND COALESCE(de.enrollmentterminationdate, v_calculated_cuttoff + INTERVAL '1 day') > v_calculated_cuttoff
            AND EXTRACT(YEAR FROM AGE(v_age_as_of_date, d.dateofbirth)) >= v_age_from 
            AND EXTRACT(YEAR FROM AGE(v_age_as_of_date, d.dateofbirth)) <= v_age_to
        ),
        stats AS (
            SELECT 
                COUNT(*) AS total_patients,
                SUM(CASE WHEN bonusstatus = 'Y' THEN 1 ELSE 0 END) AS covered_patients,
                SUM(CASE WHEN bonusstatus = 'I' THEN 1 ELSE 0 END) AS ineligible_patients
            FROM target_population
        ),
        final_result AS (
            SELECT 
                tp.*,
                CASE 
                    WHEN (s.total_patients - s.ineligible_patients) > 0 THEN 
                        (s.covered_patients::DECIMAL / (s.total_patients - s.ineligible_patients)::DECIMAL) * 100 
                    ELSE 0 
                END AS final_bonus_calculation
            FROM target_population tp, stats s
        )
        SELECT 
            fr.patientrecordid,
            fr.firstname,
            fr.lastname,
            fr.healthcardnum,
            fr.dateofbirth,
            fr.gender,
            fr.enrollmentdate,
            fr.age,
            fr.lastprocdate,
            fr.bonusstatus,
            fr.final_bonus_calculation AS bonuscalculation
        FROM final_result fr
        ORDER BY fr.lastname;

    END IF;
END;
$$ LANGUAGE plpgsql;