-- PostgreSQL function for HC_UpdateBRAND_NAME
-- Migrated from SQL Server stored procedure
-- Updates brand names for specific drugs in health card database

CREATE OR REPLACE FUNCTION dbo.HC_UpdateBRAND_NAME()
RETURNS VOID AS $$
BEGIN
    -- LANTUS
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = 'LANTUS VIAL' WHERE DRUG_IDENTIFICATION_NUMBER = '02245689';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = 'LANTUS CARTRIDGE' WHERE DRUG_IDENTIFICATION_NUMBER = '02251930';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = 'LANTUS PEN' WHERE DRUG_IDENTIFICATION_NUMBER = '02294338';

    -- TRESIBA
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - CARTRIDGE' WHERE DRUG_IDENTIFICATION_NUMBER = '02467860';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - PREFILLED PEN' WHERE DRUG_IDENTIFICATION_NUMBER = '02467879';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - PREFILLED PEN' WHERE DRUG_IDENTIFICATION_NUMBER = '02467887';

    -- APIDRA
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - VIAL' WHERE DRUG_IDENTIFICATION_NUMBER = '02279460';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - CARTRIDGE' WHERE DRUG_IDENTIFICATION_NUMBER = '02279479';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - OPTISET' WHERE DRUG_IDENTIFICATION_NUMBER = '02279487';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - SOLOSTAR' WHERE DRUG_IDENTIFICATION_NUMBER = '02294346';

    -- NOVORAPID
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - CARTRIDGE' WHERE DRUG_IDENTIFICATION_NUMBER = '02244353';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - VIAL' WHERE DRUG_IDENTIFICATION_NUMBER = '02245397';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - PREFILLED MULTIDOSE' WHERE DRUG_IDENTIFICATION_NUMBER = '02377209';

    -- FIASP
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - VIAL' WHERE DRUG_IDENTIFICATION_NUMBER = '02460408';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - CARTRIDGE' WHERE DRUG_IDENTIFICATION_NUMBER = '02460416';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - PREFILLED PEN' WHERE DRUG_IDENTIFICATION_NUMBER = '02460424';

    -- ADMELOG
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - VIAL' WHERE DRUG_IDENTIFICATION_NUMBER = '02469901';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - CARTRIDGE' WHERE DRUG_IDENTIFICATION_NUMBER = '02469898';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - PREFILLED PEN' WHERE DRUG_IDENTIFICATION_NUMBER = '02469871';

    -- BASAGLAR
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - CARTRIDGE' WHERE DRUG_IDENTIFICATION_NUMBER = '02444844';
    UPDATE dbo.QRYM_DRUG SET BRAND_NAME = BRAND_NAME || ' - KWIKPEN' WHERE DRUG_IDENTIFICATION_NUMBER = '02461528';
END;
$$ LANGUAGE plpgsql;