-- Migration: GetPatientAppointmentTests stored procedure to PostgreSQL
-- Purpose: Get patient appointment test information
-- Migrated from: SQL Server stored procedure [dbo].[GetPatientAppointmentTests]

CREATE OR REPLACE FUNCTION dbo.GetPatientAppointmentTests(
    p_practice_id INTEGER,
    p_patient_record_id INTEGER
) 
RETURNS TABLE (
    appointmentId INTEGER,
    appointmentTime TIMESTAMP,
    office VARCHAR(100),
    appointmentTestId INTEGER,
    testId INTEGER,
    testName VARCHAR(100)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.Id::INTEGER as appointmentId,
        a.appointmentTime,
        COALESCE(o.name, '') as office,
        b.Id::INTEGER as appointmentTestId,
        b.TestId::INTEGER as testId,
        COALESCE(c.testShortName, '') as testName
    FROM Appointments a
    INNER JOIN AppointmentTests b ON a.Id = b.AppointmentId
    INNER JOIN Tests c ON b.TestId = c.Id
    INNER JOIN Offices o ON a.OfficeId = o.Id
    WHERE COALESCE(b.IsActive, false) = true
        AND a.PatientRecordId = p_patient_record_id 
        AND o.PracticeId = p_practice_id
    ORDER BY a.appointmentTime DESC;
END;
$$ LANGUAGE plpgsql;