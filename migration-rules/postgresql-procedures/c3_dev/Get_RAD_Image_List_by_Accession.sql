-- PostgreSQL function for Get_RAD_Image_List_by_Accession
-- Migrated from SQL Server stored procedure
-- Gets radiology image list by accession number with filtering options

CREATE OR REPLACE FUNCTION dbo.Get_RAD_Image_List_by_Accession(
    p_accession_number VARCHAR(100) DEFAULT NULL,
    p_for_web BOOLEAN DEFAULT FALSE,
    p_image_extension VARCHAR(50) DEFAULT NULL
)
RETURNS TABLE(
    studyuid VARCHAR(250),
    studydate TIMESTAMP,
    imagename VARCHAR(500)
) AS $$
BEGIN
    -- Handle PDF images (sorted by CreatedDateTime desc)
    IF LOWER(p_image_extension) = 'pdf' THEN
        RETURN QUERY
        SELECT 
            stu.StudyUID as studyuid,
            stu.StudyDate as studydate,
            img.ImageName as imagename
        FROM dbo.RAD_Image img
        JOIN dbo.RAD_Series ser ON img.SeriesId = ser.id
        JOIN dbo.RAD_Study stu ON ser.StudyId = stu.id
        WHERE stu.AccessionNum = p_accession_number 
        AND img.image_extension = p_image_extension
        ORDER BY img.CreatedDateTime DESC;
    ELSE
        -- Handle non-PDF images with modality filtering
        RETURN QUERY
        SELECT 
            stu.StudyUID as studyuid,
            stu.StudyDate as studydate,
            img.ImageName as imagename
        FROM dbo.RAD_Image img
        JOIN dbo.RAD_Series ser ON img.SeriesId = ser.id
        JOIN dbo.RAD_Study stu ON ser.StudyId = stu.id
        WHERE stu.AccessionNum = p_accession_number 
        AND img.image_extension = p_image_extension
        AND CASE 
            WHEN p_for_web = TRUE THEN img.Modality NOT IN ('OT', 'SR')
            ELSE img.Modality NOT IN ('OT')
        END
        ORDER BY stu.DateAdded DESC, img.ImageNumber;
    END IF;
    
END;
$$ LANGUAGE plpgsql;