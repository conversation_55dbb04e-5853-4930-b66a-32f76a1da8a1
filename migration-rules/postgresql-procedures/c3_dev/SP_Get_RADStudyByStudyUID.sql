-- PostgreSQL function for SP_Get_RADStudyByStudyUID
-- Migrated from SQL Server stored procedure
-- Gets radiology study information by Study UID

CREATE OR REPLACE FUNCTION dbo.SP_Get_RADStudyByStudyUID(
    p_study_uid VARCHAR(250)
)
RETURNS TABLE(
    id INTEGER,
    studyuid VARCHAR(250),
    studydate TIMESTAMP,
    accessionnum VARCHAR(100),
    studydescription TEXT,
    studystatus VARCHAR(50),
    modality VARCHAR(10),
    patientid INTEGER,
    refphysicianid INTEGER,
    studyid INTEGER,
    institution INTEGER,
    dateadded TIMESTAMP,
    imagecount INTEGER,
    srcount INTEGER,
    createddatetime TIMESTAMP,
    practiceid INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id,
        s.StudyUID as studyuid,
        s.StudyDate as studydate,
        s.AccessionNum as accessionnum,
        s.StudyDescription as studydescription,
        s.StudyStatus as studystatus,
        s.Modality as modality,
        s.PatientId as patientid,
        s.RefPhysicianId as refphysicianid,
        s.StudyId as studyid,
        s.Institution as institution,
        s.DateAdded as dateadded,
        s.ImageCount as imagecount,
        s.SRCount as srcount,
        s.CreatedDateTime as createddatetime,
        i.PracticeId as practiceid
    FROM dbo.RAD_Study s
    JOIN dbo.RAD_Institution i ON s.Institution = i.id
    WHERE s.StudyUIDHash = MD5(p_study_uid)::UUID::TEXT
    LIMIT 1;
    
END;
$$ LANGUAGE plpgsql;