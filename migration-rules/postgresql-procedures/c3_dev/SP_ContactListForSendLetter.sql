-- PostgreSQL function migration of SP_ContactListForSendLetter stored procedure
-- Original: SP_ContactListForSendLetter(@practiceId INT, @appointmentId INT)
-- Purpose: Gets contact list of doctors for sending letters related to an appointment

CREATE OR REPLACE FUNCTION dbo.SP_ContactListForSendLetter(
    p_practice_id INTEGER,
    p_appointment_id INTEGER
)
RETURNS TABLE(
    id integer,
    email boolean,
    fax boolean,
    hrm boolean,
    mail boolean,
    name varchar(200),
    lastname varchar(100),
    firstname varchar(100),
    emailaddress varchar(200),
    doctype integer,
    appointmentdatetime timestamp
) AS $$
BEGIN
    RETURN QUERY
    -- Main/Practice Doctor
    SELECT 
        e.id,
        e.email,
        e.fax,
        e.hrm,
        e.mail,
        (e.lastname || ' ' || e.firstname) as name,
        e.lastname,
        e.firstname,
        e.emailaddress,
        1 as doctype,
        a.appointmenttime AT TIME ZONE 'UTC' as appointmentdatetime
    FROM dbo.appointments a
    JOIN dbo.practicedoctors p ON a.practicedoctorid = p.id
    JOIN dbo.externaldoctors e ON p.externaldoctorid = e.id
    WHERE a.id = p_appointment_id 
      AND p.practiceid = p_practice_id
    ORDER BY e.id DESC
    LIMIT 1

    UNION ALL

    -- Referral Doctor
    SELECT 
        e.id,
        e.email,
        e.fax,
        e.hrm,
        e.mail,
        (e.lastname || ' ' || e.firstname) as name,
        e.lastname,
        e.firstname,
        e.emailaddress,
        2 as doctype,
        a.appointmenttime AT TIME ZONE 'UTC' as appointmentdatetime
    FROM dbo.appointments a
    JOIN dbo.externaldoctors e ON a.referraldoctorid = e.id
    WHERE a.id = p_appointment_id
    ORDER BY e.id DESC
    LIMIT 1

    UNION ALL

    -- Family Doctor
    SELECT 
        e.id,
        e.email,
        e.fax,
        e.hrm,
        e.mail,
        (e.lastname || ' ' || e.firstname) as name,
        e.lastname,
        e.firstname,
        e.emailaddress,
        3 as doctype,
        a.appointmenttime AT TIME ZONE 'UTC' as appointmentdatetime
    FROM dbo.demographicsfamilydoctors f
    JOIN dbo.demographics d ON f.demographicid = d.id
    JOIN dbo.patientrecords pr ON d.patientrecordid = pr.id
    JOIN dbo.externaldoctors e ON f.externaldoctorid = e.id
    JOIN dbo.appointments a ON pr.id = a.patientrecordid
    WHERE f.isactive = true 
      AND f.isremoved = false 
      AND a.id = p_appointment_id 
      AND pr.practiceid = p_practice_id
    ORDER BY e.id DESC
    LIMIT 1

    UNION ALL

    -- Associate Doctors
    SELECT 
        e.id,
        e.email,
        e.fax,
        e.hrm,
        e.mail,
        (e.lastname || ' ' || e.firstname) as name,
        e.lastname,
        e.firstname,
        e.emailaddress,
        4 as doctype,
        a.appointmenttime AT TIME ZONE 'UTC' as appointmentdatetime
    FROM dbo.demographicsassociateddoctors da
    JOIN dbo.demographics d ON da.demographicid = d.id
    JOIN dbo.patientrecords pr ON d.patientrecordid = pr.id
    JOIN dbo.externaldoctors e ON da.externaldoctorid = e.id
    JOIN dbo.appointments a ON pr.id = a.patientrecordid
    WHERE a.id = p_appointment_id 
      AND pr.practiceid = p_practice_id 
      AND da.isactive = true
    ORDER BY e.id DESC;
END;
$$ LANGUAGE plpgsql;