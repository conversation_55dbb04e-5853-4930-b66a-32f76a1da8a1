-- PostgreSQL function for GetReportsSent_V2 stored procedure
-- Migrated from SQL Server procedure GetReportsSent_V2

CREATE OR REPLACE FUNCTION dbo.GetReportsSent_V2(
    p_patient_id INTEGER,
    p_is_vp BOOLEAN
)
RETURNS TABLE(
    id integer,
    sendid integer,
    patientid integer,
    appointmentid integer,
    appointmentdate timestamp,
    officeid integer,
    officeurl text,
    docname text,
    url text,
    testid integer,
    datesent timestamp,
    sent boolean,
    amended boolean
) AS $$
BEGIN
    IF p_is_vp = true THEN
        -- Virtual Visit reports
        RETURN QUERY
        SELECT
            ROW_NUMBER() OVER (ORDER BY sr.AppointmentDate DESC)::integer AS id,
            sr.SendId::integer,
            sr.PatientId::integer,
            sr.AppointmentId::integer,
            sr.AppointmentDate AT TIME ZONE 'UTC' as appointmentdate,
            sr.OfficeId::integer,
            COALESCE((SELECT ou.url::text FROM dbo.OfficeUrls ou 
                     WHERE sr.OfficeId = ou.officeId AND ou.urlTypeId = 5 
                     LIMIT 1), '')::text as officeurl,
            sr.DocName::text,
            sr.Url::text,
            COALESCE((SELECT t.Id::integer FROM dbo.Tests t 
                     WHERE t.testShortName = 'VP' LIMIT 1), 29)::integer as testid,
            sr.DateSent AT TIME ZONE 'UTC' as datesent,
            sr.Sent::boolean,
            sr.Amended::boolean
        FROM (
            SELECT 
                s.Id as SendId,
                a.PatientRecordId as PatientId,
                a.Id as AppointmentId,
                a.appointmentTime as AppointmentDate,
                a.OfficeId,
                s.DocName,
                s.Url,
                s.DateEntered AS DateSent,
                s.Sent,
                s.Amended,
                ROW_NUMBER() OVER (PARTITION BY s.url ORDER BY a.appointmentTime DESC, s.Id DESC) as seq
            FROM dbo.VP_SendReport s
            JOIN dbo.Appointments a ON a.Id = s.AppointmentId
            WHERE s.PatientRecordId = p_patient_id 
            AND s.SendTypeId = 6 
            AND s.Sent = true
            AND a.IsActive = true
        ) sr 
        WHERE sr.seq = 1
        ORDER BY sr.AppointmentDate DESC, sr.SendId DESC;
    ELSE
        -- Regular reports
        RETURN QUERY
        SELECT 
            ROW_NUMBER() OVER (ORDER BY a.appointmentTime DESC, s.Id DESC)::integer AS id,
            s.Id::integer as sendid,
            a.PatientRecordId::integer as patientid,
            a.Id::integer as appointmentid,
            a.appointmentTime AT TIME ZONE 'UTC' as appointmentdate,
            a.OfficeId::integer,
            COALESCE((SELECT ou.url::text FROM dbo.OfficeUrls ou 
                     WHERE a.OfficeId = ou.officeId AND ou.urlTypeId = 5 
                     LIMIT 1), '')::text as officeurl,
            s.DocName::text,
            s.Url::text,
            s.TestId::integer,
            s.DateEntered AT TIME ZONE 'UTC' as datesent,
            s.Sent::boolean,
            s.Amended::boolean
        FROM dbo.WS_SendReport s
        JOIN dbo.Appointments a ON a.Id = s.AppointmentId
        WHERE a.PatientRecordId = p_patient_id
        AND s.SendTypeId = 6 
        AND s.Sent = true
        AND s.Active = true
        AND a.IsActive = true
        ORDER BY a.appointmentTime DESC, s.Id DESC;
    END IF;
END;
$$ LANGUAGE plpgsql;