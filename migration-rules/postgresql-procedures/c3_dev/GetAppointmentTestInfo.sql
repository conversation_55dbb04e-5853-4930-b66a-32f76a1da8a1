-- Migration: GetAppointmentTestInfo stored procedure to PostgreSQL
-- Purpose: Get appointment test information with office URLs
-- Migrated from: SQL Server stored procedure [dbo].[GetAppointmentTestInfo]

CREATE OR REPLACE FUNCTION dbo.GetAppointmentTestInfo(
    p_practice_id INTEGER,
    p_appointment_id INTEGER,
    p_test_id INTEGER
) 
RETURNS TABLE (
    PatientRecordId INTEGER,
    AppointmentId INTEGER,
    TestId INTEGER,
    AccessionNumber VARCHAR(50),
    OfficeId INTEGER,
    OfficeName VARCHAR(200),
    OfficeImageURL VARCHAR(500),
    OfficeDownloadURL VARCHAR(500)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.PatientRecordId::INTEGER,
        a.Id::INTEGER as AppointmentId,
        t.TestId::INTEGER,
        COALESCE(t.AccessionNumber, '') as AccessionNumber,
        a.OfficeId::INTEGER,
        COALESCE(o.name, '') as <PERSON><PERSON>ame,
        COALESCE(ourl_image.url, '') as OfficeImageURL,
        COALESCE(ourl_download.url, '') as OfficeDownloadURL
    FROM Appointments a
    INNER JOIN AppointmentTests t ON a.Id = t.AppointmentId
    INNER JOIN Offices o ON a.OfficeId = o.Id
    LEFT JOIN (
        SELECT ou.officeId, ou.url 
        FROM OfficeUrls ou 
        INNER JOIN OfficeUrlTypes ut ON ou.urlTypeId = ut.Id
        WHERE LOWER(ut.urlType) = 'image'
    ) ourl_image ON o.Id = ourl_image.officeId
    LEFT JOIN (
        SELECT ou.officeId, ou.url 
        FROM OfficeUrls ou 
        INNER JOIN OfficeUrlTypes ut ON ou.urlTypeId = ut.Id
        WHERE LOWER(ut.urlType) = 'downloadurl'
    ) ourl_download ON o.Id = ourl_download.officeId
    WHERE o.PracticeId = p_practice_id 
        AND t.AppointmentId = p_appointment_id 
        AND t.TestId = p_test_id
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;