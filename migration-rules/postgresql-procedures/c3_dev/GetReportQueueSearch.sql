-- PostgreSQL function for GetReportQueueSearch stored procedure
-- Migrated from SQL Server procedure GetReportQueueSearch

CREATE OR REPLACE FUNCTION dbo.GetReportQueueSearch(
    p_queue_date DATE,
    p_report_queue_status INTEGER,
    p_practice_id INTEGER
)
RETURNS TABLE(
    reportqueueid integer,
    testid integer,
    testdate timestamp,
    testfullname text,
    testshortname text,
    patientrecordid integer,
    patientfirstname text,
    patientlastname text,
    dateinqueue timestamp,
    userfullname text,
    numberofattempts integer,
    datelastmodified timestamp,
    sendstatusid integer,
    appointmenttestlogid integer,
    officename text
) AS $$
BEGIN
    -- When status filter is applied
    IF p_report_queue_status > 0 THEN
        RETURN QUERY
        SELECT  
            t5.Id::integer as reportqueueid,
            t1.TestId::integer as testid, 
            t1.startTime AT TIME ZONE 'UTC' as testdate, 
            t2.testFullName::text as testfullname, 
            t2.testShortName::text as testshortname, 
            t3.PatientRecordId::integer as patientrecordid, 
            t4.firstName::text as patientfirstname, 
            t4.lastName::text as patientlastname,
            t5.DateCreated AT TIME ZONE 'UTC' as dateinqueue,
            t5.UserFullName::text as userfullname,
            t5.NumberOfAttempts::integer as numberofattempts,
            t5.DateLastModified AT TIME ZONE 'UTC' as datelastmodified,
            t5.SendStatusId::integer as sendstatusid,
            t5.AppointmentTestLogId::integer as appointmenttestlogid,
            o."Name"::text AS officename
        FROM dbo.AppointmentTests t1
        INNER JOIN dbo.Tests t2 ON t1.TestId = t2.id
        INNER JOIN dbo.Appointments t3 ON t1.AppointmentId = t3.id
        JOIN dbo.Office o ON o.Id = t3.OfficeId
        INNER JOIN dbo.Demographics t4 ON t3.PatientRecordId = t4.PatientRecordId
        INNER JOIN dbo.ReportQueues t5 ON t5.AppointmentTestId = t1.Id
        WHERE t5.DateCreated::date = p_queue_date::date
        AND t5.SendStatusId = p_report_queue_status
        AND o.PracticeId = p_practice_id
        ORDER BY t5.DateCreated;
    ELSE
        -- When no status filter is applied (get all statuses)
        RETURN QUERY
        SELECT  
            t5.Id::integer as reportqueueid,
            t1.TestId::integer as testid, 
            t1.startTime AT TIME ZONE 'UTC' as testdate, 
            t2.testFullName::text as testfullname, 
            t2.testShortName::text as testshortname, 
            t3.PatientRecordId::integer as patientrecordid, 
            t4.firstName::text as patientfirstname, 
            t4.lastName::text as patientlastname,
            t5.DateCreated AT TIME ZONE 'UTC' as dateinqueue,
            t5.UserFullName::text as userfullname,
            t5.NumberOfAttempts::integer as numberofattempts,
            t5.DateLastModified AT TIME ZONE 'UTC' as datelastmodified,
            t5.SendStatusId::integer as sendstatusid,
            t5.AppointmentTestLogId::integer as appointmenttestlogid,
            o."Name"::text AS officename
        FROM dbo.AppointmentTests t1
        INNER JOIN dbo.Tests t2 ON t1.TestId = t2.id
        INNER JOIN dbo.Appointments t3 ON t1.AppointmentId = t3.id
        JOIN dbo.Office o ON o.Id = t3.OfficeId
        INNER JOIN dbo.Demographics t4 ON t3.PatientRecordId = t4.PatientRecordId
        INNER JOIN dbo.ReportQueues t5 ON t5.AppointmentTestId = t1.Id
        WHERE t5.DateCreated::date = p_queue_date::date
        AND o.PracticeId = p_practice_id
        ORDER BY t5.DateCreated;
    END IF;
END;
$$ LANGUAGE plpgsql;