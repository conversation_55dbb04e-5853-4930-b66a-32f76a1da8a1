-- PostgreSQL function for Get_VP_OpeningStatement stored procedure
-- Migrated from SQL Server procedure Get_VP_OpeningStatement

CREATE OR REPLACE FUNCTION dbo.Get_VP_OpeningStatement(
    p_appointment_id INTEGER
)
RETURNS TABLE(
    openingstatement text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.OpeningStatement::text
    FROM dbo.Appointments a
    WHERE a.Id = p_appointment_id;
END;
$$ LANGUAGE plpgsql;