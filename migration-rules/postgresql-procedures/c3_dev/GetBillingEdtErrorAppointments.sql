-- PostgreSQL function for GetBillingEdtErrorAppointments
-- Migrated from SQL Server stored procedure
-- Gets billing EDT error appointments by practice ID and appointment IDs

CREATE OR REPLACE FUNCTION dbo.GetBillingEdtErrorAppointments(
    p_practice_id INTEGER,
    p_appointment_ids TEXT
)
RETURNS TABLE(
    id integer,
    officeid integer,
    appointmenttime timestamp
) AS $$
BEGIN
    -- Check if appointment IDs parameter is empty
    IF p_appointment_ids IS NULL OR TRIM(p_appointment_ids) = '' THEN
        RETURN;
    END IF;

    RETURN QUERY
    SELECT 
        a.Id AS id,
        a.OfficeId AS officeId,
        a.appointmentTime
    FROM dbo.Appointments a 
    INNER JOIN dbo.PracticeDoctors b ON a.PracticeDoctorId = b.Id
    WHERE b.PracticeId = p_practice_id 
        AND a.id::text = ANY(string_to_array(p_appointment_ids, ','));
END;
$$ LANGUAGE plpgsql;