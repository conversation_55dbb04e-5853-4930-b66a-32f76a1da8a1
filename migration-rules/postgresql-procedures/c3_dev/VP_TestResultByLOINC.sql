-- PostgreSQL function migration of VP_TestResultByLOINC stored procedure
-- Original: VP_TestResultByLOINC(@patientRecordId INT, @loincCode VARCHAR(50), @fromDate DATETIME = NULL)
-- Purpose: Gets VP test results by LOINC code

CREATE OR REPLACE FUNCTION dbo.VP_TestResultByLOINC(
    p_patient_record_id INTEGER,
    p_loinc_code TEXT,
    p_from_date TIMESTAMP DEFAULT NULL
)
RETURNS TABLE(
    id integer,
    loinccode text,
    testname text,
    testresult text,
    units text,
    referencerange text,
    testdate timestamp,
    patientrecordid integer,
    appointmentid integer,
    status text,
    resultflag text,
    numericresult decimal
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        lr.id,
        lr.loinccode,
        lr.testname,
        lr.testresult,
        lr.units,
        lr.referencerange,
        lr.testdate AT TIME ZONE 'UTC' as testdate,
        lr.patientrecordid,
        lr.appointmentid,
        lr.status,
        lr.resultflag,
        lr.numericresult
    FROM dbo.labresults lr
    WHERE lr.patientrecordid = p_patient_record_id
      AND lr.loinccode = p_loinc_code
      AND (p_from_date IS NULL OR lr.testdate >= p_from_date)
    ORDER BY lr.testdate DESC;
END;
$$ LANGUAGE plpgsql;