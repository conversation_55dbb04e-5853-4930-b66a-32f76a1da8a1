-- Migration: P_Get_Patient_Appointments stored procedure to PostgreSQL
-- Purpose: Get paginated list of patient appointments with details
-- Migrated from: SQL Server stored procedure [dbo].[P_Get_Patient_Appointments]

CREATE OR REPLACE FUNCTION dbo.P_Get_Patient_Appointments(
    p_practice_id INTEGER,
    p_patient_record_id INTEGER,
    p_page_num INTEGER
) 
RETURNS TABLE (
    AppointmentId INTEGER,
    PracticeId INTEGER,
    OfficeId INTEGER,
    OfficeName VARCHAR(200),
    AppointmentTime TIMESTAMP,
    AppointmentTypeId INTEGER,
    AppointmentType VARCHAR(300),
    RequestedTests TEXT,
    PatientRecordId INTEGER,
    PatientFullName VARCHAR(200),
    PracticeDoctorId INTEGER,
    PracticeDoctor VARCHAR(200),
    ReferralDoctorId INTEGER,
    ReferralDoctor VARCHAR(200),
    AppointmentNotes VARCHAR(1000),
    AppointmentStatus INTEGER,
    WaitingListId INTEGER,
    AppointmentPurpose VARCHAR(500),
    PaymentMethodId INTEGER,
    ActionOnAbnormal BOOLEAN,
    MWLSentFlag BOOLEAN,
    BookingConfirmation BOOLEAN,
    ConsultCode VARCHAR(50),
    DiagnoseCode VARCHAR(50),
    DiagnoseCode2 VARCHAR(50),
    DiagnoseCode3 VARCHAR(50),
    LetterURL VARCHAR(500),
    DateCreated TIMESTAMP,
    LastModified TIMESTAMP,
    TotalRows INTEGER,
    TotalPages INTEGER,
    PageSize INTEGER
) AS $$
DECLARE
    v_page_size INTEGER := 15;
    v_total_rows INTEGER := 0;
    v_offset INTEGER;
BEGIN
    -- Calculate offset for pagination
    v_offset := (p_page_num - 1) * v_page_size;

    -- Get total count first
    SELECT COUNT(*)
    INTO v_total_rows
    FROM Appointments a
    INNER JOIN Offices o ON a.OfficeId = o.Id
    WHERE a.PatientRecordId = p_patient_record_id 
        AND o.PracticeId = p_practice_id;

    RETURN QUERY
    SELECT 
        a.Id::INTEGER as AppointmentId,
        o.PracticeId::INTEGER,
        a.OfficeId::INTEGER,
        COALESCE(o.name, '') as OfficeName,
        a.appointmentTime as AppointmentTime,
        COALESCE(a.AppointmentTypeId, 0)::INTEGER as AppointmentTypeId,
        COALESCE(atype.name || ' Duration: ' || atype.duration::TEXT, '') as AppointmentType,
        -- Requested tests (concatenated)
        (SELECT STRING_AGG(t.testShortName, ', ')
         FROM AppointmentTests apt
         INNER JOIN Tests t ON apt.TestId = t.Id
         WHERE apt.AppointmentId = a.Id 
             AND COALESCE(apt.IsActive, false) = true) as RequestedTests,
        a.PatientRecordId::INTEGER,
        -- Patient full name (latest demographic record)
        (SELECT COALESCE(d.lastName || ', ' || d.firstName, '')
         FROM Demographics d 
         WHERE d.PatientRecordId = a.PatientRecordId 
         ORDER BY d.CreatedDateTime DESC 
         LIMIT 1) as PatientFullName,
        a.PracticeDoctorId::INTEGER,
        COALESCE(ed_appointment.lastName || ', ' || ed_appointment.firstName, '') as PracticeDoctor,
        COALESCE(ed_referral.Id, 0)::INTEGER as ReferralDoctorId,
        COALESCE(ed_referral.lastName || ', ' || ed_referral.firstName, '') as ReferralDoctor,
        COALESCE(a.appointmentNotes, '') as AppointmentNotes,
        -- Appointment status logic (if booked but past due, mark as missed)
        CASE 
            WHEN a.appointmentStatus = 2 AND a.appointmentTime < NOW() THEN 6 
            ELSE a.appointmentStatus 
        END::INTEGER as AppointmentStatus,
        CASE 
            WHEN a.appointmentStatus = 1 THEN a.appointmentStatus 
            ELSE NULL 
        END::INTEGER as WaitingListId,
        COALESCE(a.appointmentPurpose, '') as AppointmentPurpose,
        COALESCE(a.appointmentPaymentMethod, 0)::INTEGER as PaymentMethodId,
        COALESCE(a.actionOnAbnormal, false) as ActionOnAbnormal,
        COALESCE(a.MWLSentFlag, false) as MWLSentFlag,
        COALESCE(a.bookingConfirmation, false) as BookingConfirmation,
        COALESCE(cc.Code, '') as ConsultCode,
        COALESCE(dc.Code, '') as DiagnoseCode,
        COALESCE(dc2.Code, '') as DiagnoseCode2,
        COALESCE(dc3.Code, '') as DiagnoseCode3,
        -- Letter URL (VP report completion check)
        COALESCE((SELECT '/PdfConversions/PdfTest/Letter_VP_Pdf?appointmentId=' || a.Id::TEXT
                  FROM AppointmentTests vpt 
                  WHERE vpt.AppointmentTestStatusId = 11 
                      AND vpt.AppointmentId = a.Id 
                      AND vpt.TestId = 29
                  LIMIT 1), '') as LetterURL,
        a.DateCreated,
        a.LastModified,
        v_total_rows as TotalRows,
        ((v_total_rows + v_page_size - 1) / v_page_size)::INTEGER as TotalPages,
        v_page_size as PageSize
    FROM Appointments a
    INNER JOIN Offices o ON a.OfficeId = o.Id
    INNER JOIN AppointmentTypes atype ON a.AppointmentTypeId = atype.Id
    LEFT JOIN PracticeDoctors pd_appointment ON a.PracticeDoctorId = pd_appointment.Id
    LEFT JOIN ExternalDoctors ed_appointment ON pd_appointment.ExternalDoctorId = ed_appointment.Id
    LEFT JOIN ExternalDoctors ed_referral ON a.referralDoctorId = ed_referral.Id
    LEFT JOIN AppointmentBills ab ON a.Id = ab.AppointmentId
    LEFT JOIN ConsultCodes cc ON ab.ConsultCode = cc.Id
    LEFT JOIN DiagnoseCodes dc ON ab.DiagnosticCode = dc.Id
    LEFT JOIN DiagnoseCodes dc2 ON ab.DiagnosticCode2 = dc2.Id
    LEFT JOIN DiagnoseCodes dc3 ON ab.DiagnosticCode3 = dc3.Id
    WHERE a.PatientRecordId = p_patient_record_id 
        AND o.PracticeId = p_practice_id
    ORDER BY a.appointmentTime DESC
    OFFSET v_offset 
    LIMIT v_page_size;
END;
$$ LANGUAGE plpgsql;