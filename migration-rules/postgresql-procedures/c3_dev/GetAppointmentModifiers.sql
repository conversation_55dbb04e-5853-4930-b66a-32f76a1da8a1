CREATE OR REPLACE FUNCTION dbo.GetAppointmentModifiers(
    p_appointment_ids integer[]
)
RETURNS TABLE (
    "Id" integer,
    "AppointmentId" integer,
    "userId" integer,
    "reasonforChange" text,
    "changes" text,
    "createDate" timestamp
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Return appointment modifiers for the specified appointment IDs
    RETURN QUERY
    SELECT 
        appMods."Id",
        appMods."AppointmentId",
        appMods."userId",
        appMods."reasonforChange",
        appMods."changes",
        appMods."createDate"
    FROM "AppointmentModifiers" appMods
    WHERE appMods."AppointmentId" = ANY(p_appointment_ids)
    ORDER BY appMods."createDate" ASC;
END;
$$;