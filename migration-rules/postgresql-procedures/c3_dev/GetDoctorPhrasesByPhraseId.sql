-- PostgreSQL function for GetD<PERSON>tor<PERSON><PERSON><PERSON>ByPhraseId
-- Migrated from SQL Server stored procedure
-- Gets doctor phrases hierarchically using recursive CTE by phrase ID

CREATE OR REPLACE FUNCTION dbo.GetDoctorPhrasesByPhraseId(
    p_group_id INTEGER,
    p_external_doctor_id INTEGER,
    p_practice_id INTEGER,
    p_practice_template_id INTEGER,
    p_phrase_id INTEGER -- RootCategoryPhraseId
)
RETURNS TABLE(
    doctorphraseid INTEGER,
    rootcategoryphraseid INTEGER,
    pracrootcategorytempid INTEGER,
    rootcategoryid INTEGER,
    groupid INTEGER,
    isvisible BOOLEAN,
    phrasename TEXT,
    phrasevalue TEXT,
    parentid INTEGER,
    issubcategory BOOLEAN,
    level INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH RECURSIVE CustomPhraseCTE AS (
        -- Base case
        SELECT 
            drp.Id as doctorphraseid,
            drp.PracRootCategoryTempId as pracrootcategorytempid,
            rcp.Id as rootcategoryphraseid,
            CAST(CASE WHEN drp.PhraseName IS NULL THEN rcp.PhraseName ELSE drp.PhraseName END AS TEXT) AS phrasename,
            CAST(CASE WHEN drp.PhraseValue IS NULL THEN rcp.PhraseValue ELSE drp.PhraseValue END AS TEXT) AS phrasevalue,
            CAST(CASE WHEN drp.IsVisible IS NULL THEN TRUE ELSE drp.IsVisible END AS BOOLEAN) AS isvisible,
            COALESCE(drp.DisplayOrder, 1000000) as displayorder,
            rcp.ParentId as parentid,
            rc.Id as rootcategoryid,
            rc.GroupId as groupid,
            rcp.IsSubCategory as issubcategory,
            CAST(CASE WHEN drp.IsActive IS NULL THEN TRUE ELSE drp.IsActive END AS BOOLEAN) as isactive,
            0 as level
        FROM dbo.RootCategoryPhrases rcp 
        JOIN dbo.RootCategories rc ON rcp.RootCategoryId = rc.Id
        JOIN dbo.DoctorRootCategoryPhrases drp ON drp.RootCategoryPhraseId = rcp.Id 
            AND drp.PracRootCategoryTempId = p_practice_template_id 
            AND drp.ExternalDoctorId = p_external_doctor_id
        WHERE rc.GroupId = p_group_id AND rcp.Id = p_phrase_id
        
        UNION ALL
        
        -- Recursive case
        SELECT 
            drp.Id as doctorphraseid,
            cte.pracrootcategorytempid,
            drp.RootCategoryPhraseId as rootcategoryphraseid,
            CAST(CASE WHEN drp.PhraseName IS NULL THEN rcp.PhraseName ELSE drp.PhraseName END AS TEXT) AS phrasename,
            CAST(CASE WHEN drp.PhraseValue IS NULL THEN rcp.PhraseValue ELSE drp.PhraseValue END AS TEXT) AS phrasevalue,
            CAST(CASE WHEN drp.IsVisible IS NULL THEN TRUE ELSE drp.IsVisible END AS BOOLEAN) AS isvisible,
            COALESCE(drp.DisplayOrder, 1000000) as displayorder,
            rcp.ParentId as parentid,
            rc.Id as rootcategoryid,
            rc.GroupId as groupid,
            rcp.IsSubCategory as issubcategory,
            CAST(CASE WHEN drp.IsActive IS NULL THEN TRUE ELSE drp.IsActive END AS BOOLEAN) as isactive,
            cte.level + 1 as level
        FROM dbo.RootCategoryPhrases rcp 
        JOIN CustomPhraseCTE cte ON rcp.ParentId = cte.rootcategoryphraseid
        JOIN dbo.RootCategories rc ON rcp.RootCategoryId = rc.Id
        JOIN dbo.DoctorRootCategoryPhrases drp ON drp.RootCategoryPhraseId = rcp.Id 
            AND drp.PracRootCategoryTempId = p_practice_template_id 
            AND drp.ExternalDoctorId = p_external_doctor_id
        WHERE rc.GroupId = p_group_id
    )
    SELECT 
        cte.doctorphraseid,
        cte.rootcategoryphraseid,
        cte.pracrootcategorytempid,
        cte.rootcategoryid,
        cte.groupid,
        cte.isvisible,
        cte.phrasename,
        cte.phrasevalue,
        COALESCE(cte.parentid, 0) AS parentid,
        cte.issubcategory,
        cte.level
    FROM CustomPhraseCTE cte;
    
END;
$$ LANGUAGE plpgsql;