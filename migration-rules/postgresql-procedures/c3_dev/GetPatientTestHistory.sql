-- Migration: GetPatientTestHistory stored procedure to PostgreSQL
-- Purpose: Get comprehensive patient test history with billing codes
-- Migrated from: SQL Server stored procedure [dbo].[GetPatientTestHistory]
-- Note: Simplified version - complex fn_GetPatientTests function needs separate migration

CREATE OR REPLACE FUNCTION dbo.GetPatientTestHistory(
    p_practice_id INTEGER DEFAULT 0,
    p_patient_id INTEGER DEFAULT 0,
    p_appointment_test_id INTEGER DEFAULT 0
) 
RETURNS TABLE (
    PatientId INTEGER,
    PracticeId INTEGER,
    OfficeId INTEGER,
    AppointmentTestId INTEGER,
    AppointmentId INTEGER,
    AppointmentTypeId INTEGER,
    AppointmentStatus INTEGER,
    AppointmentTime TIMESTAMP,
    PracticeDoctorId INTEGER,
    TestId INTEGER,
    TestName VARCHAR(200),
    TestStatusId INTEGER,
    TestStatusColor VARCHAR(50),
    TestDate TIMESTAMP,
    PracticeDoctor VARCHAR(200),
    ExternalDoctorId INTEGER,
    BillStatusId INTEGER,
    AppointmentTestLogId INTEGER,
    AppointmentTestLogDate TIMESTAMP,
    ConsultCodeId INTEGER,
    DiagnosticCodeId INTEGER,
    ConsultCode VARCHAR(50),
    DiagnosticCode VARCHAR(50)
) AS $$
DECLARE
    v_practice_id INTEGER := p_practice_id;
    v_patient_id INTEGER := p_patient_id;
BEGIN
    -- Auto-determine practice and patient from appointment test if provided
    IF p_appointment_test_id > 0 AND v_practice_id = 0 AND v_patient_id = 0 THEN
        SELECT 
            o.PracticeId,
            app.PatientRecordId
        INTO v_practice_id, v_patient_id
        FROM AppointmentTests appTest
        INNER JOIN Appointments app ON appTest.AppointmentId = app.Id	
        INNER JOIN Offices o ON app.OfficeId = o.Id
        WHERE appTest.Id = p_appointment_test_id
        LIMIT 1;
    END IF;

    RETURN QUERY
    SELECT
        apt.PatientRecordId::INTEGER as PatientId,
        o.PracticeId::INTEGER as PracticeId,
        apt.OfficeId::INTEGER,
        at.Id::INTEGER as AppointmentTestId,
        apt.Id::INTEGER as AppointmentId,
        apt.AppointmentTypeId::INTEGER,
        apt.appointmentStatus::INTEGER as AppointmentStatus,
        apt.appointmentTime as AppointmentTime,
        apt.PracticeDoctorId::INTEGER,
        at.TestId::INTEGER,
        COALESCE(t.testShortName, '') as TestName,
        COALESCE(at.testStatusId, 0)::INTEGER as TestStatusId,
        COALESCE(ts.color, '') as TestStatusColor,
        COALESCE(at.testDate, apt.appointmentTime) as TestDate,
        COALESCE(pdu.firstName || ' ' || pdu.lastName, '') as PracticeDoctor,
        COALESCE(at.ExternalDoctorId, 0)::INTEGER,
        COALESCE(ab.billStatusId, 0)::INTEGER as BillStatusId,
        COALESCE(atl.Id, 0)::INTEGER as AppointmentTestLogId,
        atl.logDate as AppointmentTestLogDate,
        COALESCE(ab.ConsultCode, 0)::INTEGER as ConsultCodeId,
        COALESCE(ab.DiagnosticCode, 0)::INTEGER as DiagnosticCodeId,
        COALESCE(cc.Code, '') as ConsultCode,
        COALESCE(dc.Code, '') as DiagnosticCode
    FROM Appointments apt
    INNER JOIN AppointmentTests at ON apt.Id = at.AppointmentId
    INNER JOIN Tests t ON at.TestId = t.Id
    INNER JOIN Offices o ON apt.OfficeId = o.Id
    LEFT JOIN TestStatuses ts ON at.testStatusId = ts.Id
    LEFT JOIN PracticeDoctors pd ON apt.PracticeDoctorId = pd.Id
    LEFT JOIN PracticeDoctorUsers pdu ON pd.PracticeDoctorUserId = pdu.Id
    LEFT JOIN AppointmentBills ab ON apt.Id = ab.AppointmentId
    LEFT JOIN ConsultCodes cc ON ab.ConsultCode = cc.Id
    LEFT JOIN DiagnoseCodes dc ON ab.DiagnosticCode = dc.Id
    LEFT JOIN AppointmentTestSaveLogs atl ON at.Id = atl.appointmentTestId
    WHERE apt.PatientRecordId = v_patient_id
        AND o.PracticeId = v_practice_id
        AND (p_appointment_test_id = 0 OR at.Id = p_appointment_test_id)
        AND COALESCE(at.IsActive, false) = true
    ORDER BY apt.appointmentTime DESC;
END;
$$ LANGUAGE plpgsql;