-- PostgreSQL function migration of GetVPLabResults stored procedure
-- Original: GetVPLabResults(@practiceId INT, @appointmentTestLogId INT)
-- Purpose: Gets VP lab results for a specific appointment test log

CREATE OR REPLACE FUNCTION dbo.GetVPLabResults(
    p_practice_id INTEGER,
    p_appointment_test_log_id INTEGER
)
RETURNS TABLE(
    vplabresultid integer,
    labresultdate timestamp,
    hl7reportid integer,
    hl7collectiondate timestamp,
    appointmenttestsavelogid integer
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        l.id as vplabresultid,
        l.labresultdate AT TIME ZONE 'UTC' as labresultdate,
        COALESCE(l.hl7reportid, 0) as hl7reportid,
        r.collectiondatetime AT TIME ZONE 'UTC' as hl7collectiondate,
        l.appointmenttestsavelogid
    FROM dbo.vplabresults l
    JOIN dbo.appointmenttestsavelogs sl ON l.appointmenttestsavelogid = sl.id
    LEFT JOIN dbo.hl7report r ON r.id = l.hl7reportid
    WHERE l.appointmenttestsavelogid = p_appointment_test_log_id;
END;
$$ LANGUAGE plpgsql;