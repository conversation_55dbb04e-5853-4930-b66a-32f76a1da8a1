-- PostgreSQL function migration of Get_VP_AssociatedDocs stored procedure
-- Original: Get_VP_AssociatedDocs(@patientID INT)
-- Purpose: Gets associated doctors for a VP patient with contact information

CREATE OR REPLACE FUNCTION dbo.Get_VP_AssociatedDocs(
    p_patient_id INTEGER
)
RETURNS TABLE(
    externaldocid integer,
    name varchar(200),
    patientid integer,
    contact boolean,
    hasfax boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        e.id as externaldocid,
        (e.lastname || ' ' || e.firstname) as name,
        p_patient_id as patientid,
        -- Contact is true if doctor has fax, HRM, or email enabled
        CASE 
            WHEN e.fax = false AND e.hrm = false AND e.email = false 
            THEN false 
            ELSE true 
        END as contact,
        -- HasFax combines fax flag with actual fax number existence
        CASE 
            WHEN e.fax = true AND dbo.fn_HasFaxNumber(e.id) = true 
            THEN true 
            ELSE false 
        END as hasfax
    FROM dbo.demographicsassociateddoctors doc
    JOIN dbo.demographics d ON doc.demographicid = d.id
    JOIN dbo.externaldoctors e ON doc.externaldoctorid = e.id
    WHERE d.patientrecordid = p_patient_id 
      AND doc.iscc = true 
      AND doc.isremoved = false 
      AND doc.isactive = true;
END;
$$ LANGUAGE plpgsql;