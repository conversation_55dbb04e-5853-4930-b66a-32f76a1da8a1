-- PostgreSQL function for SP_GetPatientAppointmentsForRAD
-- Migrated from SQL Server stored procedure
-- Patient appointment tests for radiology

CREATE OR REPLACE FUNCTION dbo.SP_GetPatientAppointmentsForRAD(
    p_practice_id INTEGER DEFAULT NULL,
    p_accession_number VARCHAR(50) DEFAULT NULL,
    p_test_date TIMESTAMP DEFAULT NULL,
    p_patient_record_id INTEGER DEFAULT 0,
    p_patient_dob TIMESTAMP DEFAULT NULL
)
RETURNS TABLE(
    patientrecordid integer,
    appointmenttestid integer,
    testname text,
    accessionnumber varchar(50),
    firstname text,
    lastname text,
    patientdob varchar(20),
    testdate varchar(20)
) AS $$
DECLARE
    v_init_dt TIMESTAMP;
    v_end_dt TIMESTAMP;
BEGIN
    -- First condition: AccessionNumber is provided
    IF p_accession_number IS NOT NULL AND TRIM(COALESCE(p_accession_number, '')) <> '' THEN
        RETURN QUERY
        SELECT 
            ap.PatientRecordId,
            apt.Id AS AppointmentTestId,
            t.TestFullName AS TestName,
            COALESCE(apt.accessionnumber, '') AS AccessionNumber,
            d.FirstName,
            d.LastName,
            COALESCE(TO_CHAR(d.dateofbirth, 'YYYY-MM-DD HH24:MI:SS'), '') AS PatientDOB,
            COALESCE(TO_CHAR(apt.starttime, 'YYYY-MM-DD HH24:MI:SS'), '') AS TestDate
        FROM dbo.AppointmentTests AS apt 
        JOIN dbo.Appointments AS ap ON apt.AppointmentId = ap.Id
        JOIN dbo.PatientRecords PR ON ap.PatientRecordId = PR.Id
        JOIN dbo.Demographics AS d ON ap.PatientRecordId = d.PatientRecordId
        JOIN dbo.Tests t ON apt.TestId = t.Id
        WHERE PR.PracticeId = p_practice_id 
            AND ap.Id IN (
                SELECT appT.AppointmentId 
                FROM dbo.AppointmentTests appT 
                WHERE appT.AccessionNumber = p_accession_number
            );
    ELSE
        -- Second condition: PatientRecordId is provided
        IF p_patient_record_id IS NOT NULL AND p_patient_record_id > 0 THEN
            -- Set date range if test date is provided
            IF p_test_date IS NOT NULL THEN
                v_init_dt := DATE_TRUNC('day', p_test_date);
                v_end_dt := DATE_TRUNC('day', p_test_date) + INTERVAL '23 hours 59 minutes 59 seconds';
            END IF;

            RETURN QUERY
            SELECT 
                ap.PatientRecordId,
                apt.Id AS AppointmentTestId,
                t.TestFullName AS TestName,
                COALESCE(apt.accessionnumber, '') AS AccessionNumber,
                d.FirstName,
                d.LastName,
                COALESCE(TO_CHAR(d.dateofbirth, 'YYYY-MM-DD HH24:MI:SS'), '') AS PatientDOB,
                COALESCE(TO_CHAR(apt.starttime, 'YYYY-MM-DD HH24:MI:SS'), '') AS TestDate
            FROM dbo.AppointmentTests AS apt 
            JOIN dbo.Appointments AS ap ON apt.AppointmentId = ap.Id
            JOIN dbo.PatientRecords PR ON ap.PatientRecordId = PR.Id
            JOIN dbo.Demographics AS d ON ap.PatientRecordId = d.PatientRecordId
            JOIN dbo.Tests t ON apt.TestId = t.Id
            WHERE ap.Id = apt.AppointmentId 
                AND ap.PatientRecordId = d.PatientRecordId
                AND PR.PracticeId = p_practice_id
                AND ap.PatientRecordId = p_patient_record_id
                AND (p_accession_number IS NULL OR TRIM(p_accession_number) = '' OR apt.accessionnumber = p_accession_number)
                AND (p_test_date IS NULL OR (apt.StartTime BETWEEN v_init_dt AND v_end_dt))
                AND (p_patient_dob IS NULL OR d.dateOfBirth = p_patient_dob);
        END IF;
    END IF;
END;
$$ LANGUAGE plpgsql;