-- PostgreSQL function for Get_VP_CPP_Setting stored procedure
-- Migrated from SQL Server procedure Get_VP_CPP_Setting

CREATE OR REPLACE FUNCTION dbo.Get_VP_CPP_Setting(
    p_doctor_id INTEGER
)
RETURNS TABLE(
    id integer,
    doctorid integer,
    vp_cpp_category_id integer,
    visible boolean,
    required boolean,
    colwidth integer,
    colorder integer,
    enabled boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        v.Id::integer,
        v.DoctorID::integer,
        v.VP_CPP_Category_Id::integer,
        v.Visible::boolean,
        v.Required::boolean,
        v.ColWidth::integer,
        v.ColOrder::integer,
        v.Enabled::boolean
    FROM dbo.VP_CPP_Setting v
    WHERE v.DoctorID = p_doctor_id;
END;
$$ LANGUAGE plpgsql;