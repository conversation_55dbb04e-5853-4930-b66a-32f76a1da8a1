-- PostgreSQL function for APP_PrepareMWL
-- Migrated from SQL Server stored procedure
-- Prepares MWL (Modality Work List) data for radiology

CREATE OR REPLACE FUNCTION dbo.APP_PrepareMWL(
    p_practice_id INTEGER,
    p_appointment_id INTEGER
)
RETURNS TABLE(
    appointmentid INTEGER,
    mwlsentflag BOOLEAN,
    mwlurl VARCHAR(1000),
    officeid INTEGER,
    patientid INTEGER,
    sin VARCHAR(50),
    ohip VARCHAR(50),
    patientlastname VARCHAR(100),
    patientfirstname VARCHAR(100),
    patientdateofbirth VARCHAR(8),
    gender VARCHAR(10),
    doctorlastname VARCHAR(100),
    doctorfirstname VARCHAR(100),
    cpso VARCHAR(50),
    ohipphysicianid VARCHAR(50),
    officeurl VARCHAR(500),
    tests JSON
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.Id as appointmentid,
        a.<PERSON><PERSON> as mwlsentflag,
        a.MWLUrl as mwlurl,
        a.OfficeId as officeid,
        a.<PERSON>ient<PERSON>ecordId as patientid,
        demo.SIN as sin,
        (SELECT H.number 
         FROM dbo.DemographicsHealthCards H 
         WHERE H.DemographicId = demo.Id AND H.IsActive = true 
         ORDER BY H.Id DESC 
         LIMIT 1) as ohip,
        demo.lastName as patientlastname,
        demo.firstName as patientfirstname,
        TO_CHAR(demo.dateOfBirth, 'YYYYMMDD') as patientdateofbirth,
        demo.gender as gender,
        ed.LastName as doctorlastname,
        ed.firstName as doctorfirstname,
        ed.CPSO as cpso,
        ed.OHIPPhysicianId as ohipphysicianid,
        ourl.url as officeurl,
        COALESCE(
            (SELECT json_agg(json_build_object(
                'AppointmentTestId', APT.Id,
                'AccessionNumber', APT.AccessionNumber,
                'TestId', T.Id,
                'TestName', T.testShortName,
                'ModalityId', M.Id,
                'ModalityName', M.modalityName,
                'ModalityDescription', M.Description
            ))
            FROM dbo.AppointmentTests APT
            JOIN dbo.Tests T ON APT.TestId = T.Id
            LEFT JOIN dbo.TestModalities TM ON APT.TestId = TM.TestId
            LEFT JOIN dbo.Modalities M ON TM.ModalityId = M.Id
            WHERE APT.AppointmentId = A.Id AND APT.IsActive = true),
            NULL::json
        ) as tests
    FROM dbo.Appointments a
    JOIN dbo.PatientRecords pr ON a.PatientRecordId = pr.Id
    JOIN dbo.Demographics demo ON pr.Id = demo.PatientRecordId
    JOIN dbo.PracticeDoctors pd ON a.PracticeDoctorId = pd.Id
    JOIN dbo.ExternalDoctors ed ON pd.ExternalDoctorId = ed.Id
    JOIN dbo.OfficeUrls ourl ON a.OfficeId = ourl.officeId
    JOIN dbo.OfficeUrlTypes ty ON ourl.urlTypeId = ty.Id
    WHERE pr.PracticeId = p_practice_id 
        AND a.Id = p_appointment_id 
        AND ty.urlType = 'MWL' 
        AND demo.dateOfBirth IS NOT NULL
    ORDER BY a.appointmentTime DESC;
END;
$$ LANGUAGE plpgsql;