-- PostgreSQL function equivalent of SQL Server GetDaysheetAppointments stored procedure
-- This function gets daysheet appointments with complex filtering and business logic
-- Note: This is a simplified version of the complex SQL Server procedure with dynamic SQL

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create GetDaysheetAppointments function
CREATE OR REPLACE FUNCTION dbo.GetDaysheetAppointments(
    p_office_id INTEGER DEFAULT NULL,
    p_selected_date TIMESTAMP DEFAULT NULL,
    p_show_expected BOOLEAN DEFAULT false,
    p_exclude_test_only BOOLEAN DEFAULT false,
    p_exclude_cancelled BOOLEAN DEFAULT false,
    p_appointment_status INTEGER DEFAULT NULL,
    p_appointment_id INTEGER DEFAULT NULL,
    p_filter_patient VARCHAR(50) DEFAULT NULL,
    p_user_id INTEGER DEFAULT 0
)
RETURNS TABLE(
    id integer,
    practiceid integer,
    officeid integer,
    appointmenttime timestamp,
    arrivedtime text,
    lefttime text,
    appointmentpurpose text,
    appointmentstatus integer,
    appointmentnotes text,
    appointmentregistrar integer,
    mwlurl text,
    mwlsentflag boolean,
    actiononabnormal boolean,
    bookingconfirmation boolean,
    roomnumber text,
    practicedoctorid integer,
    billstatusid integer,
    billstatus text,
    billstatuscolor text,
    openingstatement text,
    referraldoctorid integer,
    referraldoctor text,
    refdocohipphysicianid text,
    refdoccpso text,
    referraldoctoraddressid integer,
    referraldoctoraddress text,
    referraldoctorphoneid integer,
    referraldoctorfaxnumber text,
    appointmenttypeid integer,
    appointmenttype text,
    appointmenttypeparentid integer,
    appointmenttypeparent text,
    appointmentconfirmation integer,
    appointmentpaymentmethod integer,
    triageurgencyid integer,
    triagestatusid integer,
    isactive boolean,
    patientrecordid integer,
    datecreated timestamp,
    lastmodified timestamp,
    practicedoctor text,
    patientfirstname text,
    patientlastname text,
    dateofbirth timestamp,
    consultcodeid integer,
    diagnosticcodeid integer,
    diagnosticcodeid2 integer,
    diagnosticcodeid3 integer,
    consultcode text,
    diagnosticcode text,
    diagnosticcode2 text,
    diagnosticcode3 text,
    totalhealthcards integer,
    totalreferraldocuments integer,
    totaldoctorcomments integer,
    totalreferraldocfax integer,
    totalreferraldocnumbers integer,
    isdoctordoublebook boolean,
    totalvpbooked integer,
    isimported integer,
    familydoctor text,
    patientphonenumbers text
) AS $$
DECLARE
    v_is_active INTEGER := 1;
    v_cancel_flag INTEGER := 1;
    v_exclude_test_flag INTEGER := 1;
    v_triage_app_status INTEGER := 16;
    v_exclude_cancel_id INTEGER := 7;
    v_no_list_status INTEGER := 1;
    v_lookup_date DATE;
    v_init_dt TIMESTAMP;
    v_end_dt TIMESTAMP;
    v_office_id_new INTEGER;
    v_practice_id INTEGER;
BEGIN
    -- Handle office ID and date parameters
    v_office_id_new := p_office_id;
    
    IF p_appointment_id IS NULL OR p_appointment_id = 0 THEN
        v_init_dt := DATE_TRUNC('day', p_selected_date);
        v_end_dt := DATE_TRUNC('day', p_selected_date) + INTERVAL '23 hours 59 minutes 59 seconds';
        v_lookup_date := DATE(p_selected_date);
    ELSE
        SELECT 
            DATE_TRUNC('day', appointmenttime),
            DATE_TRUNC('day', appointmenttime) + INTERVAL '23 hours 59 minutes 59 seconds',
            DATE(appointmenttime),
            officeid
        INTO v_init_dt, v_end_dt, v_lookup_date, v_office_id_new
        FROM dbo.appointments
        WHERE id = p_appointment_id;
    END IF;
    
    -- Get practice ID
    SELECT practiceid INTO v_practice_id
    FROM dbo.office
    WHERE id = v_office_id_new;

    RETURN QUERY
    WITH filtered_appointments AS (
        SELECT DISTINCT
            apps.id,
            o.practiceid,
            apps.officeid,
            apps.appointmenttime,
            apps.arrivedtime,
            apps.lefttime,
            apps.appointmentpurpose,
            apps.appointmentstatus,
            apps.appointmentnotes,
            apps.appointmentregistrar,
            apps.mwlurl,
            apps.mwlsentflag,
            apps.actiononabnormal,
            apps.bookingconfirmation,
            apps.roomnumber,
            apps.practicedoctorid,
            apps.openingstatement,
            apps.referraldoctorid,
            apps.referraldoctoraddressid,
            apps.referraldoctorphonenumberid as referraldoctorphoneid,
            apps.appointmenttypeid,
            apptype.name as appointmenttype,
            COALESCE(apptype.appointmenttypeid, 0) as appointmenttypeparentid,
            apps.appointmentconfirmation,
            apps.appointmentpaymentmethod,
            apps.triageurgencyid,
            apps.triagestatusid,
            apps.isactive,
            apps.patientrecordid,
            apps.datecreated,
            apps.lastmodified,
            apps.isimported,
            -- Window function for double booking detection
            CASE 
                WHEN COUNT(*) OVER (
                    PARTITION BY apps.officeid, apps.appointmenttime, apps.practicedoctorid
                ) > 1 THEN true 
                ELSE false 
            END as is_doctor_double_book
        FROM dbo.appointments apps
        JOIN dbo.office o ON o.id = apps.officeid
        JOIN dbo.appointmenttypes apptype ON apps.appointmenttypeid = apptype.id
        WHERE apps.appointmenttime BETWEEN v_init_dt AND v_end_dt
        AND apps.officeid = v_office_id_new
        AND apps.isactive = (v_is_active = 1)
        -- Apply status filters based on parameters
        AND (
            (p_show_expected AND apps.appointmentstatus NOT IN (7, 1, 0, 5, 12))
            OR (NOT p_show_expected AND p_appointment_status IS NULL AND apps.appointmentstatus > v_no_list_status)
            OR (NOT p_show_expected AND p_appointment_status IS NOT NULL AND apps.appointmentstatus = p_appointment_status)
            OR (p_appointment_id IS NOT NULL AND apps.id = p_appointment_id)
        )
        AND (NOT p_exclude_cancelled OR apps.appointmentstatus != v_exclude_cancel_id)
        AND (NOT p_exclude_test_only OR apptype.appointmenttypeid != v_exclude_test_flag)
        AND apps.appointmentstatus != v_triage_app_status
    )
    SELECT 
        fa.id,
        fa.practiceid,
        fa.officeid,
        fa.appointmenttime AT TIME ZONE 'UTC',
        fa.arrivedtime::text,
        fa.lefttime::text,
        fa.appointmentpurpose::text,
        fa.appointmentstatus,
        fa.appointmentnotes::text,
        fa.appointmentregistrar,
        fa.mwlurl::text,
        fa.mwlsentflag,
        fa.actiononabnormal,
        fa.bookingconfirmation,
        fa.roomnumber::text,
        fa.practicedoctorid,
        -- Bill status (simplified)
        COALESCE(
            (SELECT 2 FROM dbo.billdetails WHERE appointmentid = fa.id LIMIT 1), 
            0
        ),
        COALESCE(bs.name, '')::text,
        COALESCE(bs.color, '')::text,
        fa.openingstatement::text,
        fa.referraldoctorid,
        -- Referral doctor name
        COALESCE(
            ref_ed.firstname || ' ' || ref_ed.lastname,
            ''
        )::text,
        COALESCE(ref_ed.ohipphysicianid, '')::text,
        COALESCE(ref_ed.cpso, '')::text,
        fa.referraldoctoraddressid,
        ''::text as referral_doctor_address, -- Simplified
        fa.referraldoctorphoneid,
        COALESCE(
            (SELECT faxnumber FROM dbo.externaldoctorphonenumbers 
             WHERE id = fa.referraldoctorphoneid LIMIT 1),
            ''
        )::text,
        fa.appointmenttypeid,
        fa.appointmenttype::text,
        fa.appointmenttypeparentid,
        COALESCE(
            (SELECT name FROM dbo.appointmenttypes 
             WHERE id = fa.appointmenttypeparentid LIMIT 1),
            ''
        )::text,
        fa.appointmentconfirmation,
        fa.appointmentpaymentmethod,
        fa.triageurgencyid,
        fa.triagestatusid,
        fa.isactive,
        fa.patientrecordid,
        fa.datecreated AT TIME ZONE 'UTC',
        fa.lastmodified AT TIME ZONE 'UTC',
        -- Practice doctor name
        COALESCE(
            prac_ed.firstname || ' ' || prac_ed.lastname,
            ''
        )::text,
        -- Patient info
        demo.firstname::text,
        demo.lastname::text,
        demo.dateofbirth AT TIME ZONE 'UTC',
        -- Billing codes (simplified)
        COALESCE(ab.consultcode, 0),
        COALESCE(ab.diagnosticcode, 0),
        COALESCE(ab.diagnosticcode2, 0),
        COALESCE(ab.diagnosticcode3, 0),
        COALESCE(cc.code, '')::text,
        COALESCE(dc1.diagnosis, '')::text,
        COALESCE(dc2.diagnosis, '')::text,
        COALESCE(dc3.diagnosis, '')::text,
        -- Counts (simplified)
        COALESCE(
            (SELECT 1 FROM dbo.demographicshealthcards 
             WHERE demographicid = demo.id AND TRIM(number) != '' LIMIT 1),
            0
        ),
        COALESCE(
            (SELECT 1 FROM dbo.reportreceiveds 
             WHERE appointmentid = fa.id LIMIT 1),
            0
        ),
        COALESCE(
            (SELECT 1 FROM dbo.doctorcomments 
             WHERE patientrecordid = fa.patientrecordid LIMIT 1),
            0
        ),
        0 as total_referral_doc_fax,  -- Simplified
        0 as total_referral_doc_numbers,  -- Simplified
        fa.is_doctor_double_book,
        0 as total_vp_booked,  -- Simplified - would need VP booking logic
        0 as is_imported,  -- Simplified
        -- Family doctor
        COALESCE(
            (SELECT fam_ed.firstname || ' ' || fam_ed.lastname
             FROM dbo.externaldoctors fam_ed
             JOIN dbo.demographicsfamilydoctors fd ON fam_ed.id = fd.externaldoctorid
             WHERE fd.demographicid = demo.id
             AND fd.isactive = true 
             AND fd.isremoved = false
             ORDER BY fd.id DESC
             LIMIT 1),
            ''
        )::text,
        -- Patient phone numbers
        COALESCE(
            (SELECT STRING_AGG(
                ph.phonenumber || ' ' || 
                CASE ph.typeofphonenumber
                    WHEN 0 THEN 'H'
                    WHEN 1 THEN 'C'
                    WHEN 2 THEN 'W'
                    ELSE ''
                END,
                ', '
                ORDER BY ph.typeofphonenumber, ph.isactive DESC, ph.id DESC
            )
             FROM dbo.demographicsphonenumbers ph
             WHERE ph.demographicid = demo.id
             AND ph.phonenumber IS NOT NULL
             AND TRIM(ph.phonenumber) != ''
             AND ph.isremoved = false),
            ''
        )::text
    FROM filtered_appointments fa
    JOIN dbo.demographics demo ON fa.patientrecordid = demo.patientrecordid
    JOIN dbo.practicedoctors prac_doc ON fa.practicedoctorid = prac_doc.id
    JOIN dbo.externaldoctors prac_ed ON prac_doc.externaldoctorid = prac_ed.id
    LEFT JOIN dbo.externaldoctors ref_ed ON fa.referraldoctorid = ref_ed.id
    LEFT JOIN dbo.appointmentbills ab ON fa.id = ab.appointmentid
    LEFT JOIN dbo.consultcodes cc ON ab.consultcode = cc.id
    LEFT JOIN dbo.diagnosecodes dc1 ON ab.diagnosticcode = dc1.id
    LEFT JOIN dbo.diagnosecodes dc2 ON ab.diagnosticcode2 = dc2.id
    LEFT JOIN dbo.diagnosecodes dc3 ON ab.diagnosticcode3 = dc3.id
    LEFT JOIN dbo.billstatus bs ON (
        CASE 
            WHEN EXISTS(SELECT 1 FROM dbo.billdetails WHERE appointmentid = fa.id) 
            THEN 2 ELSE 0 
        END
    ) = bs.id
    WHERE (
        p_filter_patient IS NULL 
        OR LOWER(demo.lastname) LIKE LOWER(p_filter_patient) || '%'
        OR LOWER(demo.firstname) LIKE LOWER(p_filter_patient) || '%'
    )
    ORDER BY fa.id ASC;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.GetDaysheetAppointments TO postgres;