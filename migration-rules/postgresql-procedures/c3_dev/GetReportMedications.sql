-- PostgreSQL function migration of GetReportMedications stored procedure
-- Original: GetReportMedications(@appointmentTestId INT)
-- Purpose: Retrieves medication information for report generation

CREATE OR REPLACE FUNCTION dbo.GetReportMedications(
    p_appointment_test_id INTEGER
)
RETURNS TABLE(
    patientid integer,
    patientmedicationid integer,
    medicationsetid integer,
    medicationname varchar(500),
    medicationnameconcat varchar(4000),
    din varchar(50),
    medicationnodinid integer,
    dose varchar(50),
    strength varchar(50),
    ingredients varchar(1500),
    form varchar(100),
    route varchar(100),
    sig varchar(50),
    datestarted timestamp,
    datediscontinued timestamp,
    appointmentid integer,
    appointmenttestid integer,
    appointmenttime timestamp,
    actiontype varchar(100),
    rownum integer
) AS $$
DECLARE
    v_practice_id INTEGER;
    v_office_id INTEGER;
    v_appointment_id INTEGER;
    v_patient_id INTEGER;
    v_appointment_time TIMESTAMP;
    v_practice_doctor_id INTEGER;
    v_external_doctor_id INTEGER;
    v_is_vp BOOLEAN := false;
    v_active_medication VARCHAR(4000);
    
    -- Action type constants
    v_added_med VARCHAR(100) := 'Added';
    v_prior_med VARCHAR(100) := 'Prior';
    v_discontinued_med VARCHAR(100) := 'Discontinued';
    v_dose_change_med VARCHAR(100) := 'Dose Changed';
    v_active_med VARCHAR(100) := 'Active';
BEGIN
    -- Get appointment and practice information
    SELECT 
        o.practiceid,
        o.id,
        app_test.appointmentid,
        app.patientrecordid,
        app.appointmenttime AT TIME ZONE 'UTC',
        app.practicedoctorid,
        COALESCE(pd.externaldoctorid, 0),
        CASE WHEN app_test.testid = 29 THEN true ELSE false END
    INTO 
        v_practice_id,
        v_office_id,
        v_appointment_id,
        v_patient_id,
        v_appointment_time,
        v_practice_doctor_id,
        v_external_doctor_id,
        v_is_vp
    FROM dbo.appointmenttests app_test
    JOIN dbo.appointments app ON app_test.appointmentid = app.id
    JOIN dbo.practicedoctors pd ON app.practicedoctorid = pd.id
    JOIN dbo.office o ON app.officeid = o.id
    WHERE app_test.id = p_appointment_test_id
    LIMIT 1;

    -- Build active medication concatenated string
    SELECT string_agg(
        medicationname || ' ' || COALESCE(dose, '') || ' ' || COALESCE(sig, ''), 
        ',  '
    )
    INTO v_active_medication
    FROM dbo.fn_getreportmedications(
        v_practice_id, 
        v_patient_id, 
        v_external_doctor_id, 
        v_appointment_time, 
        v_is_vp, 
        false
    )
    WHERE actiontype IN (v_active_med, v_prior_med);

    -- Return the medications with concatenated string
    RETURN QUERY
    SELECT 
        m.patientid,
        m.patientmedicationid,
        m.medicationsetid,
        m.medicationname,
        COALESCE(v_active_medication, '') as medicationnameconcat,
        m.din,
        m.medicationnodinid,
        m.dose,
        m.strength,
        m.ingredients,
        m.form,
        m.route,
        m.sig,
        m.datestarted,
        m.datediscontinued,
        v_appointment_id as appointmentid,
        p_appointment_test_id as appointmenttestid,
        v_appointment_time as appointmenttime,
        m.actiontype,
        m.rownum
    FROM dbo.fn_getreportmedications(
        v_practice_id, 
        v_patient_id, 
        v_external_doctor_id, 
        v_appointment_time, 
        v_is_vp, 
        false
    ) m;
END;
$$ LANGUAGE plpgsql;