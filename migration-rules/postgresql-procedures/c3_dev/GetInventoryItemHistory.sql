-- GetInventoryItemHistory procedure for PostgreSQL
-- Migrated from SQL Server stored procedure with table variable and complex joins

CREATE OR REPLACE FUNCTION dbo.GetInventoryItemHistory(
    p_inventory_id integer
)
RETURNS TABLE (
    devicenumberid bigint,
    officeid integer,
    officename text,
    devicetypeid integer,
    devicenumber text,
    devicetype text,
    patientequipmentid bigint,
    patientid integer,
    patientfullname text,
    datestarted timestamp with time zone,
    dateexpectedreturn timestamp with time zone,
    datecreated timestamp with time zone,
    datereturned timestamp with time zone,
    notes text,
    appointmenttestid integer,
    assignedbyuserid integer,
    assignedbyuser text,
    testid integer,
    testname text,
    appointmentteststatusid integer,
    appointmentteststatuscolor text,
    appointmentteststatuscsclass text,
    appointmentteststatus text
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        i.id AS devicenumberid,
        i.officeid,
        o.name AS officename,
        i.inventorytypeid AS devicetypeid,
        i.code AS devicenumber,
        it.name AS devicetype,
        pe.id AS patientequipmentid,
        pe.patientrecordid AS patientid,
        COALESCE(d.lastname, '') || ', ' || COALESCE(d.firstname, '') AS patientfullname,
        pe.datestarted,
        pe.dateexpectedreturn,
        pe.datecreated,
        pe.datereturned,
        COALESCE(pe.notes, '') AS notes,
        pe.appointmenttestid,
        pe.assignedbyuserid,
        COALESCE((
            SELECT COALESCE(u.lastname, '') || ', ' || COALESCE(u.firstname, '')
            FROM dbo.aspnetusers u 
            WHERE u.userid = pe.assignedbyuserid 
            LIMIT 1
        ), '') AS assignedbyuser,
        apt.testid,
        COALESCE(t.testshortname, '') AS testname,
        apt.appointmentteststatusid,
        COALESCE(ats.color, '') AS appointmentteststatuscolor,
        COALESCE(ats.cssclass, '') AS appointmentteststatuscsclass,
        COALESCE(ats.status, '') AS appointmentteststatus
    FROM dbo.patientequipments pe
    JOIN dbo.demographics d ON pe.patientrecordid = d.patientrecordid
    JOIN dbo.storeinventories i ON pe.inventoryid = i.id
    JOIN dbo.storeinventorytypes it ON i.inventorytypeid = it.id
    JOIN dbo.office o ON i.officeid = o.id
    JOIN dbo.appointmenttests apt ON pe.appointmenttestid = apt.id
    JOIN dbo.tests t ON apt.testid = t.id
    JOIN dbo.appointmentteststatus ats ON apt.appointmentteststatusid = ats.id
    WHERE i.id = p_inventory_id
    ORDER BY pe.datereturned DESC NULLS FIRST;
END;
$$;