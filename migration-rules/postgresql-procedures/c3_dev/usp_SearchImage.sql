-- PostgreSQL function for usp_SearchImage
-- Migrated from SQL Server stored procedure
-- Searches for image by Image<PERSON><PERSON> using hash-based lookup

CREATE OR REPLACE FUNCTION dbo.usp_SearchImage(
    p_image_uid VARCHAR(4000)
)
RETURNS TABLE(
    id INTEGER,
    seriesid INTEGER,
    imagename VARCHAR(500),
    imageuid VARCHAR(4000),
    imagenumber INTEGER,
    modality VARCHAR(50),
    adddate TIMESTAMP,
    image_extension VARCHAR(50),
    createddatetime TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        i.id,
        i.SeriesId as seriesid,
        i.ImageName as imagename,
        i.ImageUID as imageuid,
        i.ImageNumber as imagenumber,
        i.Modality as modality,
        i.AddDate as adddate,
        i.image_extension,
        i.CreatedDateTime as createddatetime
    FROM dbo.rad_image i
    WHERE i.ImageUIDHash = MD5(p_image_uid)::UUID::TEXT
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;