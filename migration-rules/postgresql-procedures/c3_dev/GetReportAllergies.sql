-- PostgreSQL function for GetReportAllergies stored procedure
-- Migrated from SQL Server procedure GetReportAllergies

CREATE OR REPLACE FUNCTION dbo.GetReportAllergies(
    p_appointment_test_id INTEGER
)
RETURNS TABLE(
    patientid integer,
    patientallergyid integer,
    medicationname text,
    ingredient text,
    displayname text,
    severity text,
    datestarted timestamp,
    allergiesnameconcat text
) AS $$
DECLARE
    v_practice_id INTEGER;
    v_office_id INTEGER;
    v_appointment_id INTEGER;
    v_patient_id INTEGER;
    v_appointment_time TIMESTAMP;
    v_practice_doctor_id INTEGER;
    v_external_doctor_id INTEGER;
    v_is_visible_allergies BOOLEAN := false;
    v_cpp_category_id INTEGER := 8;
    v_active_allergies TEXT;
BEGIN
    -- Get appointment and related information
    SELECT 
        o.PracticeId,
        o.Id,
        appTest.appointmentId,
        app.PatientRecordId,
        app.appointmentTime AT TIME ZONE 'UTC',
        app.PracticeDoctorId,
        pd.ExternalDoctorId
    INTO
        v_practice_id,
        v_office_id,
        v_appointment_id,
        v_patient_id,
        v_appointment_time,
        v_practice_doctor_id,
        v_external_doctor_id
    FROM dbo.AppointmentTests appTest
    JOIN dbo.Appointments app ON appTest.AppointmentId = app.Id
    JOIN dbo.PracticeDoctors pd ON app.PracticeDoctorId = pd.Id
    JOIN dbo.Office o ON app.OfficeId = o.Id
    WHERE appTest.Id = p_appointment_test_id
    LIMIT 1;

    -- Check if allergies are visible for this doctor
    SELECT COALESCE(cs.Visible, false)
    INTO v_is_visible_allergies
    FROM dbo.VP_CPP_Setting cs 
    WHERE cs.DoctorID = v_external_doctor_id 
    AND cs.VP_CPP_Category_Id = v_cpp_category_id
    LIMIT 1;

    IF v_is_visible_allergies = true THEN
        -- Create temporary table for allergies
        CREATE TEMP TABLE temp_allergies AS
        SELECT 
            pa.Id as PatientAllergyId,
            pa.PatientRecordId as PatientId,
            pa.MedicationName,
            pal.Ingredient,
            CASE WHEN pal.Ingredient IS NULL THEN pa.MedicationName ELSE pal.Ingredient END as DisplayName,
            s.Description as Severity,
            pa.DateStarted AT TIME ZONE 'UTC' as DateStarted
        FROM dbo.PatientAllergies pa
        JOIN dbo.Severity s ON pa.SeverityId = s.Id
        LEFT JOIN dbo.PatientAllergyIngredients pal ON pa.Id = pal.PatientAllergyId
        WHERE pa.PatientRecordId = v_patient_id 
        AND pa.DateStarted::date <= v_appointment_time::date
        AND pa.IsActive = true
        AND pa.AllergyStatusId = 1
        ORDER BY pa.DateStarted DESC;

        -- Create concatenated allergies string
        SELECT STRING_AGG(DisplayName || ' ' || COALESCE(Severity, ''), ', ' ORDER BY DateStarted DESC)
        INTO v_active_allergies
        FROM temp_allergies;

        -- Return the results
        RETURN QUERY
        SELECT
            al.PatientId::integer,
            al.PatientAllergyId::integer,
            al.MedicationName::text,
            al.Ingredient::text,
            al.DisplayName::text,
            al.Severity::text,
            al.DateStarted,
            v_active_allergies::text as AllergiesNameConCat
        FROM temp_allergies al;

        -- Clean up temp table
        DROP TABLE temp_allergies;
    ELSE
        -- Return empty result set if allergies not visible
        RETURN QUERY
        SELECT
            NULL::integer as PatientId,
            NULL::integer as PatientAllergyId,
            NULL::text as MedicationName,
            NULL::text as Ingredient,
            NULL::text as DisplayName,
            NULL::text as Severity,
            NULL::timestamp as DateStarted,
            NULL::text as AllergiesNameConCat
        WHERE false; -- Empty result set
    END IF;
END;
$$ LANGUAGE plpgsql;