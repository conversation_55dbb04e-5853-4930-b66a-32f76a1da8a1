-- PostgreSQL function equivalent of SQL Server PUNCH_Can_User_Punch_InOut stored procedure
-- This function checks if a user can punch in/out based on their schedule

-- Create dbo schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS dbo;

-- Create PUNCH_Can_User_Punch_InOut function
CREATE OR REPLACE FUNCTION dbo.PUNCH_Can_User_Punch_InOut(
    p_practice_id INTEGER,
    p_user_id INTEGER,
    p_punch_date TIMESTAMP,
    p_office_ids INTEGER[]
)
RETURNS TABLE(
    AnyExists boolean
) AS $$
DECLARE
    v_any_exists boolean DEFAULT false;
    v_day_of_week integer;
BEGIN
    -- Calculate day of week (PostgreSQL: Sunday=0, Monday=1, ..., Saturday=6)
    -- SQL Server DATEPART(WEEKDAY) returns Sunday=1, Monday=2, ..., Saturday=7
    -- So we need to adjust: PostgreSQL EXTRACT(DOW) gives 0-6, SQL Server gives 1-7
    v_day_of_week := EXTRACT(DOW FROM p_punch_date);
    
    -- Check if user exists in the schedule for the given conditions
    SELECT EXISTS(
        SELECT 1
        FROM AspNetUsers u
        JOIN UserOffices o ON (u.Id = o.ApplicationUserId)
        JOIN ScheduleWeekDays w ON o.OfficeId = w.officeId
        WHERE u.PracticeID = p_practice_id 
        AND u.UserID = p_user_id
        AND o.OfficeId = ANY(p_office_ids)
        AND w.dayOfWeek = v_day_of_week
    ) INTO v_any_exists;

    RETURN QUERY SELECT v_any_exists;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION dbo.PUNCH_Can_User_Punch_InOut TO postgres;