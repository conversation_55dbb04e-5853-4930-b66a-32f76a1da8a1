-- PostgreSQL function migration of SCH_GetCCDoctors stored procedure
-- Original: SCH_GetCCDoctors(@practiceId INT, @patientId INT, @appointmentId INT = 0)
-- Purpose: Gets CC (Carbon Copy) doctors for a patient

CREATE OR REPLACE FUNCTION dbo.SCH_GetCCDoctors(
    p_practice_id INTEGER,
    p_patient_id INTEGER,
    p_appointment_id INTEGER DEFAULT 0
)
RETURNS TABLE(
    externaldoctorid bigint,
    firstname text,
    lastname text,
    cpso text,
    ohipphysicianid text,
    fax boolean,
    email boolean,
    hrm boolean,
    hrmid text,
    emailaddress text,
    isvip boolean,
    externaldoctoraddressid bigint,
    externaldoctorlocationid bigint,
    addressline1 text,
    addressline2 text,
    city text,
    province text,
    postalcode text,
    externaldoctorphonenumberid bigint,
    faxnumber text,
    phonenumber text,
    hasfaxnumber boolean,
    isactive boolean
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        exd.id as externaldoctorid,
        exd.firstname,
        exd.lastname,
        exd.cpso,
        exd.ohipphysicianid,
        exd.fax,
        exd.email,
        exd.hrm,
        exd.hrmid,
        exd.emailaddress,
        CAST(true AS boolean) as isvip,
        COALESCE(ea.id, 0) as externaldoctoraddressid,
        COALESCE(el.id, 0) as externaldoctorlocationid,
        ea.addressline1,
        ea.addressline2,
        ea.city,
        ea.province,
        ea.postalcode,
        COALESCE(ep.id, 0) as externaldoctorphonenumberid,
        ep.faxnumber,
        ep.phonenumber,
        CAST(COALESCE(
            (SELECT CASE WHEN COUNT(*) > 0 THEN true ELSE false END 
             FROM dbo.externaldoctorphonenumbers ep2
             WHERE ep2.externaldoctorid = exd.id 
               AND ep2.faxnumber IS NOT NULL 
               AND TRIM(ep2.faxnumber) != ''), false) AS boolean) as hasfaxnumber,
        exd.active as isactive
    FROM dbo.demographicsassociateddoctors doc
    JOIN dbo.demographics d ON doc.demographicid = d.id
    JOIN dbo.patientrecords pr ON d.patientrecordid = pr.id
    JOIN dbo.externaldoctors exd ON doc.externaldoctorid = exd.id
    LEFT JOIN dbo.externaldoctorlocations el ON doc.externaldoctorlocationid = el.id 
    LEFT JOIN dbo.externaldoctoraddresses ea ON doc.externaldoctorid = ea.externaldoctorid 
        AND doc.externaldoctorid = exd.id 
        AND ea.id = el.externaldoctoraddressid
    LEFT JOIN dbo.externaldoctorphonenumbers ep ON doc.externaldoctorid = ep.externaldoctorid 
        AND doc.externaldoctorid = exd.id 
        AND ep.id = el.externaldoctorphonenumberid
    WHERE d.patientrecordid = p_patient_id
      AND pr.practiceid = p_practice_id  
      AND doc.iscc = true 
      AND doc.isremoved = false;
END;
$$ LANGUAGE plpgsql;