# Critical Issues Found in PostgreSQL Procedure Testing Framework

## Summary

The `run_all_tests.sh` script was giving **FALSE POSITIVES** - reporting procedures as "PASS" when they were actually broken or didn't exist.

## Root Cause

The test framework has multiple layers that can mask real issues:

1. **Test Script Logic**: Only certain procedures get actual execution (line 1946 in run_all_tests.sh)
2. **Python Script Override**: The `execute_procedure_test.py` script returns hardcoded statuses instead of running real tests
3. **Name/Parameter Mismatches**: Function names and parameters don't match between test script and actual functions

## Specific Issues with sp_get_demographicenrolment

### What the test script reported:
- ✅ **PASS** - "ACTUALLY FIXED 2025-09-12"

### What was actually broken:
1. **Function name mismatch**: 
   - Test calls: `dbo.sp_get_demographicenrolment`
   - Actual function: `dbo.SP_Get_DemographicEnrolment`

2. **Parameter name mismatch**:
   - Test calls: `p_patient_id := 2`
   - Function expects: `p_patient_record_id`

3. **SQL Server parameter mismatch**:
   - Test calls: `@PatientId = 2`
   - Procedure expects: `@PatientRecordId`

4. **Data type issues**:
   - Function returns: `bigint` IDs
   - Function declares: `integer` IDs
   - Error: "Returned type bigint does not match expected type integer"

## Fixes Applied

1. **Renamed function** to match test expectations: `sp_get_demographicenrolment`
2. **Fixed parameter name** to match test: `p_patient_id`
3. **Updated SQL Server test** to use correct parameter: `@PatientRecordId`
4. **Fixed data types** in RETURNS TABLE: `bigint` for ID columns
5. **Updated Python script** to reflect actual status: `FAIL` → needs validation

## Recommendations

### For Future Validation:
1. **Always test manually first** before trusting the automated test results
2. **Check function exists** with correct name and parameters
3. **Verify data types** match between function declaration and actual table columns
4. **Test both databases independently** before comparing results

### For the Testing Framework:
1. **Remove hardcoded statuses** from Python script - make it actually test
2. **Add better error handling** for missing functions
3. **Validate function signatures** before attempting to call them
4. **Add logging** to show what's actually being executed vs. what's being skipped

## Pattern for Manual Validation

```bash
# 1. Check if function exists
\df dbo.sp_get_demographicenrolment

# 2. Test function call
SELECT * FROM dbo.sp_get_demographicenrolment(p_patient_id := 2);

# 3. Compare with SQL Server
EXEC [dbo].[SP_Get_DemographicEnrolment] @PatientRecordId = 2;

# 4. Check for errors and data differences
```

## Status Update

- **sp_get_demographicenrolment**: Fixed function definition, needs deployment and validation
- **Testing framework**: Identified as unreliable, needs manual verification for all "PASS" results

## Next Steps

1. Deploy the fixed function to PostgreSQL
2. Run manual validation tests
3. Update the checklist with actual results
4. Review other procedures marked as "PASS" for similar issues