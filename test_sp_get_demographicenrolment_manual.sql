-- Manual test script for sp_get_demographicenrolment function
-- This script can be run in both PostgreSQL and SQL Server to validate the function

-- =============================================
-- PostgreSQL Tests
-- =============================================

-- 1. Deploy the function (run this in PostgreSQL)
-- Copy and paste the content from migration-rules/postgresql-procedures/SP_Get_DemographicEnrolment.sql

-- 2. Test with patient ID 2 (should return data)
SELECT * FROM dbo.sp_get_demographicenrolment(p_patient_id := 2);

-- 3. Test with patient ID 9999 (should return no data)
SELECT * FROM dbo.sp_get_demographicenrolment(p_patient_id := 9999);

-- 4. Check function exists
SELECT 
    p.proname as function_name,
    pg_get_function_arguments(p.oid) as arguments,
    pg_get_function_result(p.oid) as return_type
FROM pg_proc p
LEFT JOIN pg_catalog.pg_namespace n ON n.oid = p.pronamespace
WHERE n.nspname = 'dbo' 
  AND p.proname = 'sp_get_demographicenrolment';

-- =============================================
-- SQL Server Tests (for comparison)
-- =============================================

-- 1. Test with patient record ID 2 (should return data)
EXEC [dbo].[SP_Get_DemographicEnrolment] @PatientRecordId = 2;

-- 2. Test with patient record ID 9999 (should return no data)  
EXEC [dbo].[SP_Get_DemographicEnrolment] @PatientRecordId = 9999;

-- 3. Check procedure exists
SELECT 
    name,
    type_desc,
    create_date,
    modify_date
FROM sys.objects 
WHERE type = 'P' 
  AND name = 'SP_Get_DemographicEnrolment';

-- =============================================
-- Validation Checklist
-- =============================================

-- [ ] PostgreSQL function deploys without errors
-- [ ] PostgreSQL function can be called with test parameters
-- [ ] SQL Server procedure can be called with test parameters  
-- [ ] Both return the same number of columns
-- [ ] Both return the same data types (accounting for differences)
-- [ ] Both return the same row count for identical test data
-- [ ] Column names match (accounting for case differences)
-- [ ] Data values match exactly

-- =============================================
-- Expected Results
-- =============================================

-- Patient ID 2: Should return enrollment data if patient exists
-- Patient ID 9999: Should return empty result set (no rows)

-- Common issues to check:
-- 1. Function name case sensitivity
-- 2. Parameter name mismatches  
-- 3. Data type mismatches (integer vs bigint)
-- 4. Schema references (dbo.tablename)
-- 5. Column name casing differences