#!/bin/bash

echo "Testing sp_get_demographicenrolment function..."

# First, deploy the updated function to PostgreSQL
echo "Deploying function to PostgreSQL..."
cat migration-rules/postgresql-procedures/SP_Get_DemographicEnrolment.sql

echo ""
echo "Testing function call with patient ID 2..."
echo "SELECT * FROM dbo.sp_get_demographicenrolment(p_patient_id := 2);"

echo ""
echo "Testing function call with patient ID 9999 (non-existent)..."
echo "SELECT * FROM dbo.sp_get_demographicenrolment(p_patient_id := 9999);"

echo ""
echo "Function should now work correctly with:"
echo "1. Correct function name: sp_get_demographicenrolment (lowercase)"
echo "2. Correct parameter name: p_patient_id"
echo "3. Correct data types: bigint for ID columns"
echo "4. Simplified query logic"