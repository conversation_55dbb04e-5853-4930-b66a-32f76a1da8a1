#!/bin/bash

# Comprehensive list of PostgreSQL functions and SQL Server stored procedures 
# that need to be added to run_all_tests.sh
# 
# Usage: Review each section and add procedures systematically to the test framework
# Format: postgres_function_name|sql_server_procedure_name|suggested_test_cases

echo "=================================="
echo "COMPREHENSIVE POSTGRESQL/SQL SERVER PROCEDURE PAIRS FOR TESTING"
echo "=================================="
echo ""

echo "=== ALREADY IN TESTS ==="
echo "getdaysheetcohorts|GetDaysheetCohorts|empty_list:EMPTY single_patient:1001"
echo "getdaysheetpreconditions|GetDaysheetPreconditions|empty_list:EMPTY single_appointment:1"  
echo "sch_getccdoctors|SCH_GetCCDoctors|single_patient:1:1001 non_existent:1:9999"
echo "sp_find_patients_v1|SP_Find_Patients_V1|ohip_search:********** name_search:Smith patient_id:2"
echo ""

echo "=== USER & PERMISSION PROCEDURES ==="
echo "getusermenucount|GetUserMenuCount|user_with_menu:1 user_no_menu:9999"
echo "getuserpermissions|GetUserPermissions|valid_user:1 invalid_user:9999"
echo "getuserroles|GetUserRoles|user_with_roles:1 user_no_roles:9999"
echo "getuseroffices|GetUserOffices|user_with_offices:1 user_no_offices:9999"
echo "getpracticescheduledusers|GetPracticeScheduledUsers|practice_with_users:1 practice_no_users:9999"
echo "getpracticescheduleduserweeķdays|GetPracticeScheduledUserWeekDays|user_schedule:1:1 no_schedule:1:9999"
echo ""

echo "=== APPOINTMENT PROCEDURES ==="
echo "getappointmentreminders|GetAppointmentReminders|practice_appointments:1 date_range:1:2024-01-01"
echo "getappointmentreminders_v2|GetAppointmentReminders_v2|practice_v2:1 date_range_v2:1:2024-01-01"
echo "sp_getpatientappointment|SP_GetPatientAppointment|patient_appointment:2 invalid_patient:9999"
echo "sp_getpatientpreviousappointments|SP_GetPatientPreviousAppointments|patient_history:3 no_history:9999"
echo "getwaitlistappointments_v2|GetWaitlistAppointments_v2|practice_waitlist:1 empty_waitlist:9999"
echo "p_get_patient_appointments|P_Get_Patient_Appointments|patient_appts:2 no_appointments:9999"
echo "getappointmenttestinfo|GetAppointmentTestInfo|appointment_with_tests:1 appointment_no_tests:9999"
echo "getappointmenttestsavedlogs|GetAppointmentTestSavedLogs|appointment_logs:1 no_logs:9999"
echo "getappointmenttests|GetAppointmentTests|practice_tests:1 no_tests:9999"
echo "getappointmentmodifiers|GetAppointmentModifiers|appointment_mods:1 no_mods:9999"
echo "getscheduleappointments|GetScheduleAppointments|schedule_date:1:2024-01-01 no_appointments:1:2025-01-01"
echo "getdaysheetappointments|GetDaysheetAppointments|daysheet_date:1:2024-01-01 no_daysheet:1:2025-01-01"
echo ""

echo "=== PATIENT PROCEDURES ==="
echo "sp_getpatientdemographicinfo|SP_GetPatientDemographicInfo|patient_demo:1:2 invalid_patient:1:9999"
echo "getpatientlocations|GetPatientLocations|patient_locations:2:1 no_locations:9999:1"
echo "getmaindoctorinfo|GetMainDoctorInfo|patient_doctor:2 patient_no_doctor:9999"
echo "getpracticepatientinfo|GetPracticePatientInfo|practice_patient:1:2 invalid_combo:1:9999"
echo "getpatientappointmenttests|GetPatientAppointmentTests|patient_tests:2 no_tests:9999"
echo "getpatienttesthistory|GetPatientTestHistory|test_history:2 no_history:9999"
echo "getpatientprevioustests|GetPatientPreviousTests|previous_tests:2 no_previous:9999"
echo "searchpatientsbyoldchartnumber|SearchPatientsByOldChartNumber|chart_search:1:OLD123 invalid_chart:1:INVALID"
echo "sp_get_demographicenrolment|SP_Get_DemographicEnrolment|patient_enrolment:2 no_enrolment:9999"
echo "sp_get_patient_immunizationtype|SP_Get_Patient_ImmunizationType|patient_immunization:2 no_immunization:9999"
echo "sp_get_patient_vp_cpp_immunization_types|SP_Get_Patient_VP_CPP_Immunization_Types|vp_immunization:2:1 no_vp_immunization:9999:1"
echo ""

echo "=== KIOSK PROCEDURES ==="
echo "getkioskcheckins|GetKioskCheckins|kiosk_checkins:1 no_checkins:9999"
echo "getkioskappointmentinfo|GetKioskAppointmentInfo|kiosk_appointment:1 invalid_appointment:9999"
echo "getkioskofficeinfo|GetKioskOfficeInfo|office_kiosk:1 invalid_office:9999"
echo "sp_getkioskappointmentroominfo|SP_GetKioskAppointmentRoomInfo|room_info:1 no_room:9999"
echo ""

echo "=== WORK LIST & PRACTICE PROCEDURES ==="
echo "getpracticeworklist_v2|GetPracticeWorkList_v2|practice_worklist:1 empty_worklist:9999"
echo "getwaitlisttests|GetWaitlistTests|waitlist_tests:1 no_waitlist_tests:9999"
echo "tapp_getpracticedoctors|TAPP_GetPracticeDoctors|practice_doctors:1 no_doctors:9999"
echo ""

echo "=== VP (VISIT PAGE) PROCEDURES ==="
echo "get_vp_doctoroptions|Get_VP_DoctorOptions|doctor_options:1:1 no_options:1:9999"
echo "get_vp_options|Get_VP_Options|vp_options: no_vp_options:"
echo "get_vp_cpp_setting|Get_VP_CPP_Setting|cpp_setting:1:1:2 no_cpp:1:9999:2"
echo "get_vp_openingstatement|Get_VP_OpeningStatement|opening_statement:1:2:1 no_statement:9999:2:1"
echo "getcppcategoriesbydoctor|GetCPPCategoriesByDoctor|doctor_cpp:1 no_cpp_doctor:9999"
echo "get_vp_cpp_skipped|Get_VP_CPP_Skipped|cpp_skipped:1:2:1 no_skipped:9999:2:1"
echo "get_vp_summary|Get_VP_Summary|vp_summary:1:2:1 no_summary:9999:2:1"
echo "get_vp_privacynotes|Get_VP_PrivacyNotes|privacy_notes:1:2 no_privacy:9999:2"
echo "get_vp_logs|Get_VP_Logs|vp_logs:1:2 no_logs:9999:2"
echo "get_vp_associateddocs|Get_VP_AssociatedDocs|associated_docs:1:2 no_docs:9999:2"
echo "sp_vp_getdoctorbyuserid|SP_VP_GetDoctorByUserId|doctor_by_user:1 invalid_user:9999"
echo "sp_get_vp_measurementsavedvalue|SP_Get_VP_MeasurementSavedValue|measurement_saved:1:2:3 no_saved:9999:2:3"
echo "getvplabresults|GetVPLabResults|vp_lab_results:1:2 no_lab_results:9999:2"
echo "getvpreportphrasesbyrootcategoryid|GetVPReportPhrasesByRootCategoryId|phrases_by_category:1:2 no_phrases:9999:2"
echo "get_vp_reportphrases_custom|Get_VP_ReportPhrases_Custom|custom_phrases:1:2:3 no_custom:9999:2:3"
echo "get_vp_reportphrases_skipped|Get_VP_ReportPhrases_Skipped|skipped_phrases:1:2:3 no_skipped:9999:2:3"
echo "get_vp_reportphrasessavedtext|Get_VP_ReportPhrasesSavedText|saved_text:1:2:3 no_saved_text:9999:2:3"
echo "vp_templatedetailsbypatient|VP_TemplateDetailsByPatient|template_patient:2:1 no_template:9999:1"
echo "vp_templatepatientdata|VP_TemplatePatientData|template_data:2:1 no_data:9999:1"
echo "sp_vp_templatedetailswithloincvalues|SP_VP_TemplateDetailsWithLOINCValues|template_loinc:1:2 no_loinc:9999:2"
echo "vp_testresultbyloinc|VP_TestResultByLOINC|test_loinc:TEST123 invalid_loinc:INVALID"
echo "sp_vp_getvitalsandlabs_acc|SP_VP_GetVitalsAndLabs_Acc|vitals_labs:2:1 no_vitals:9999:1"
echo "sp_vp_labresults_acc|SP_VP_LabResults_Acc|lab_results_acc:2:1 no_results:9999:1"
echo ""

echo "=== REPORT PROCEDURES ==="
echo "getreportqueueSearch|GetReportQueueSearch|report_queue:1 empty_queue:9999"
echo "getreportssent_v2|GetReportsSent_V2|reports_sent:1:2024-01-01 no_reports:1:2025-01-01"
echo "getreportallergies|GetReportAllergies|report_allergies:2 no_allergies:9999"
echo "getreportmedications|GetReportMedications|report_medications:2:1 no_medications:9999:1"
echo "fn_getreportmedications|fn_GetReportMedications|fn_medications:2:1 fn_no_medications:9999:1"
echo "getreportphrasesavetextbylogids|GetReportPhraseSaveTextByLogIds|phrase_logs:1,2,3 no_phrase_logs:9999"
echo "getreportclinicdailyregister|GetReportClinicDailyRegister|daily_register:1:2024-01-01 no_register:1:2025-01-01"
echo "getreportdoctors|GetReportDoctors|report_doctors:1 no_report_doctors:9999"
echo "getreportpracticetestgroup|GetReportPracticeTestGroup|practice_testgroup:1 no_testgroup:9999"
echo "getreportpracticedoctorfooter|GetReportPracticeDoctorFooter|doctor_footer:1:1 no_footer:1:9999"
echo "getssrsreportbyappointmenttestid|GetSSRSReportByAppointmentTestId|ssrs_report:1 no_ssrs:9999"
echo ""

echo "=== INVENTORY PROCEDURES ==="
echo "getinventoryitems|GetInventoryItems|inventory_items:1 no_items:9999"
echo "getinventoryitem|GetInventoryItem|single_item:1 invalid_item:9999"
echo "getinventoryitemhistory|GetInventoryItemHistory|item_history:1 no_history:9999"
echo "getinventoryoverdue|GetInventoryOverDue|overdue_items:1 no_overdue:9999"
echo ""

echo "=== AUDIT PROCEDURES ==="
echo "searchauditbydate|SearchAuditByDate|audit_date:2024-01-01:2024-01-31 no_audit:2025-01-01:2025-01-31"
echo "searchauditbydatenip|SearchAuditByDateNIP|audit_nip:2024-01-01:2024-01-31 no_audit_nip:2025-01-01:2025-01-31"
echo "searchauditbydatenpatient|SearchAuditByDateNPatient|audit_patient:2024-01-01:2024-01-31:2 no_audit_patient:2025-01-01:2025-01-31:9999"
echo "searchauditbydatenipnuser|SearchAuditByDateNIPNuser|audit_user:2024-01-01:2024-01-31:1 no_audit_user:2025-01-01:2025-01-31:9999"
echo "searchauditbydatenpatientnip|SearchAuditByDateNPatientNip|audit_patient_nip:2024-01-01:2024-01-31:2 no_audit_patient_nip:2025-01-01:2025-01-31:9999"
echo "searchauditbydatenpatientnipnuser|SearchAuditByDateNPatientNipNuser|audit_full:2024-01-01:2024-01-31:2:1 no_audit_full:2025-01-01:2025-01-31:9999:9999"
echo "getaudit|GetAudit|get_audit:1 no_get_audit:9999"
echo "getauditlogdata|GetAuditLogData|audit_logdata:1 no_logdata:9999"
echo ""

echo "=== DOCTOR & EXTERNAL DOCTOR PROCEDURES ==="
echo "getdoctorcomments|GetDoctorComments|doctor_comments:1:2 no_comments:9999:2"
echo "getcustommeasurements|GetCustomMeasurements|custom_measurements:1:2 no_custom:9999:2"
echo "getallpracticedoctorsforolis|GetAllPracticeDoctorsForOLIS|olis_doctors:1 no_olis_doctors:9999"
echo "getdoctorinfo|GetDoctorInfo|doctor_info:1 invalid_doctor:9999"
echo "getexternaldoctorlocations|GetExternalDoctorLocations|external_locations:1 no_external:9999"
echo ""

echo "=== CONTACT & LETTER PROCEDURES ==="
echo "sp_contactlistforsendletter|SP_ContactListForSendLetter|contact_letter:1 no_contacts:9999"
echo ""

echo "=== ECONSULT PROCEDURES ==="
echo "geteconsultpatientreports|GetEconsultPatientReports|econsult_reports:2:1 no_econsult:9999:1"
echo "geteconsults|GetEConsults|econsults:1 no_econsults:9999"
echo "geteconsultmetadata|GetEconsultMetadata|econsult_metadata:1 no_metadata:9999"
echo ""

echo "=== BONUS & RECALL PROCEDURES ==="
echo "sp_generatebonusreport|sp_GenerateBonusReport|bonus_report:1:2024-01-01:2024-12-31 no_bonus:9999:2025-01-01:2025-12-31"
echo "sp_getrecalllist|sp_GetRecallList|recall_list:1 no_recalls:9999"
echo ""

echo "=== OLIS & LAB PROCEDURES ==="
echo "sp_update_practicedoctor_olis_lastaccessdatetime|SP_Update_PracticeDoctor_OLIS_LastAccessDateTime|olis_update:1:1 invalid_olis:9999:9999"
echo "getunmappedloinc|GetUnmappedLOINC|unmapped_loinc:1 no_unmapped:9999"
echo ""

echo "=== PUNCH PROCEDURES ==="
echo "punch_can_user_punch_inout|PUNCH_Can_User_Punch_InOut|punch_check:1 invalid_punch_user:9999"
echo ""

echo ""
echo "=== USAGE INSTRUCTIONS ==="
echo ""
echo "1. Review each procedure pair above"
echo "2. Use the ADD_PROCEDURE_TO_TESTS_PROMPT.md to add procedures systematically"
echo "3. Test each procedure using the suggested test cases"
echo "4. For each procedure, follow this pattern:"
echo ""
echo "   # Add to WORKING_PROCEDURES array in run_all_tests.sh:"
echo "   \"procedure_name\""
echo ""
echo "   # Add to PROCEDURE_TEST_CASES array:"
echo "   PROCEDURE_TEST_CASES[\"procedure_name\"]=\"test_case_1:params test_case_2:params\""
echo ""
echo "   # Add SQL Server execution case:"
echo "   \"procedure_name\")"
echo "       case \"\$test_case\" in"
echo "           \"test_case_1\") echo \"EXEC [dbo].[ProcedureName] @Param = '\$params';\" ;;"
echo "           *) echo \"-- No SQL Server test case defined for \$proc_name.\$test_case\" ;;"
echo "       esac"
echo "       ;;"
echo ""
echo "   # Add PostgreSQL execution case:"
echo "   \"procedure_name\")"
echo "       case \"\$test_case\" in"
echo "           \"test_case_1\") echo \"SELECT * FROM dbo.procedure_name(p_param := '\$params')\" ;;"
echo "           *) echo \"-- No PostgreSQL test case defined for \$func_name.\$test_case\" ;;"
echo "       esac"
echo "       ;;"
echo ""
echo "5. After adding each procedure:"
echo "   - Run ./run_all_tests.sh to verify test case generation"
echo "   - Use MCP tools to execute actual queries and compare results"
echo "   - Fix any PostgreSQL function issues found"
echo "   - Deploy fixes with ./deploy-postgres-functions.sh -f procedure.sql"
echo ""
echo "=== PRIORITY SUGGESTIONS ==="
echo ""
echo "Start with these high-impact procedures first:"
echo "1. Patient procedures (used frequently in UI)"
echo "2. Appointment procedures (core scheduling functionality)"
echo "3. VP procedures (visit page functionality)"
echo "4. Report procedures (reporting features)"
echo "5. Remaining procedures by usage frequency"
echo ""

# Make this file executable
chmod +x "$0"

echo "Total procedures identified: ~90+ PostgreSQL/SQL Server pairs"
echo "Already in tests: 4"
echo "Remaining to add: ~86+ procedures"
echo ""
echo "This script saved as: $0"